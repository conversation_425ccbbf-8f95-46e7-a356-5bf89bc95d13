{"QRHardwareInvalidTransactionTitle": {"message": "Erro"}, "QRHardwareMismatchedSignId": {"message": "Os dados da transação são inconsistentes. Verifique os detalhes da transação."}, "QRHardwarePubkeyAccountOutOfRange": {"message": "Não há mais contas. Se você gostaria de acessar outra conta não listada abaixo, reconecte sua carteira de hardware e selecione-a."}, "QRHardwareScanInstructions": {"message": "Posicione o código QR na frente da sua câmera. A tela está desfocada, mas isso não afetará a leitura."}, "QRHardwareSignRequestCancel": {"message": "Recusar"}, "QRHardwareSignRequestDescription": {"message": "Depois de ter assinado com a sua carteira, clique em \"Receber assinatura\""}, "QRHardwareSignRequestGetSignature": {"message": "Receber assinatura"}, "QRHardwareSignRequestSubtitle": {"message": "Leia o código QR com a sua carteira"}, "QRHardwareSignRequestTitle": {"message": "Solicitar assinatura"}, "QRHardwareUnknownQRCodeTitle": {"message": "Erro"}, "QRHardwareUnknownWalletQRCode": {"message": "Código QR inválido. Leia o código QR de sincronização da carteira de hardware."}, "QRHardwareWalletImporterTitle": {"message": "<PERSON><PERSON> c<PERSON><PERSON>"}, "QRHardwareWalletSteps1Description": {"message": "Você pode escolher abaixo em uma lista de parceiros oficiais que aceitam códigos QR."}, "QRHardwareWalletSteps1Title": {"message": "Conecte sua carteira de hardware QR"}, "QRHardwareWalletSteps2Description": {"message": "<PERSON><PERSON>"}, "SrpListHideAccounts": {"message": "Ocultar $1 contas", "description": "$1 is the number of accounts"}, "SrpListHideSingleAccount": {"message": "Ocultar 1 conta"}, "SrpListShowAccounts": {"message": "Exibir $1 contas", "description": "$1 is the number of accounts"}, "SrpListShowSingleAccount": {"message": "Exibir 1 conta"}, "about": {"message": "Sobre"}, "accept": {"message": "Aceitar"}, "acceptTermsOfUse": {"message": "Eu li e concordo com os $1", "description": "$1 is the `terms` message"}, "accessingYourCamera": {"message": "Acessando sua câmera..."}, "account": {"message": "Conta"}, "accountActivity": {"message": "Atividade da conta"}, "accountActivityText": {"message": "<PERSON><PERSON><PERSON><PERSON> as contas das quais deseja receber notificações:"}, "accountDetails": {"message": "<PERSON>al<PERSON> da conta"}, "accountIdenticon": {"message": "Identicon da conta"}, "accountIsntConnectedToastText": {"message": "$1 não está conectado a $2"}, "accountName": {"message": "Nome da conta"}, "accountNameDuplicate": {"message": "Esse nome de conta já existe", "description": "This is an error message shown when the user enters a new account name that matches an existing account name"}, "accountNameReserved": {"message": "Esse nome de conta está reservado", "description": "This is an error message shown when the user enters a new account name that is reserved for future use"}, "accountOptions": {"message": "Opções da conta"}, "accountPermissionToast": {"message": "Permissões de conta atualizadas"}, "accountSelectionRequired": {"message": "Você precisa selecionar uma conta!"}, "accountTypeNotSupported": {"message": "Tipo de conta não compatível"}, "accounts": {"message": "<PERSON><PERSON>"}, "accountsConnected": {"message": "Contas conectadas"}, "accountsPermissionsTitle": {"message": "Ver suas contas e sugerir transações"}, "accountsSmallCase": {"message": "contas"}, "active": {"message": "Ativo"}, "activity": {"message": "Atividade"}, "activityLog": {"message": "Registro de atividades"}, "add": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "addACustomNetwork": {"message": "Adicionar uma rede personalizada"}, "addANetwork": {"message": "Adicionar uma rede"}, "addANickname": {"message": "Adicionar um apelido"}, "addAUrl": {"message": "Adicionar um URL"}, "addAccount": {"message": "Adicionar conta"}, "addAccountFromNetwork": {"message": "Adicionar conta $1", "description": "$1 is the network name, e.g. Bitcoin or Solana"}, "addAccountToNeoNix": {"message": "Adicionar conta à NeoNix"}, "addAcquiredTokens": {"message": "Adicione os tokens que você adquiriu usando a NeoNix"}, "addAlias": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "addBitcoinAccountLabel": {"message": "Conta Bitcoin"}, "addBlockExplorer": {"message": "Adicionar um explorador de blocos"}, "addBlockExplorerUrl": {"message": "Adicionar URL de um explorador de blocos"}, "addContact": {"message": "Adicionar contato"}, "addCustomNetwork": {"message": "Adicionar rede personalizada"}, "addEthereumChainWarningModalHeader": {"message": "Adicione esse provedor de RPC apenas se tiver certeza de que é confiável. $1", "description": "$1 is addEthereumChainWarningModalHeaderPartTwo passed separately so that it can be bolded"}, "addEthereumChainWarningModalHeaderPartTwo": {"message": "Provedores mal-intencionados podem mentir sobre o estado da blockchain e registrar a atividade da sua rede."}, "addEthereumChainWarningModalListHeader": {"message": "É importante que o seu provedor seja confiável, pois ele tem poder para:"}, "addEthereumChainWarningModalListPointOne": {"message": "Ver suas contas e endereços IP e cruzá-los"}, "addEthereumChainWarningModalListPointThree": {"message": "Exibir saldos de conta e outros estados na rede"}, "addEthereumChainWarningModalListPointTwo": {"message": "Transmitir suas transações"}, "addEthereumChainWarningModalTitle": {"message": "Você está adicionando um novo provedor RPC para a Mainnet da Ethereum"}, "addEthereumWatchOnlyAccount": {"message": "Visualize uma conta Ethereum (Beta)"}, "addFriendsAndAddresses": {"message": "Adicionar amigos e endereços confiáveis"}, "addHardwareWalletLabel": {"message": "Carteira de hardware"}, "addIPFSGateway": {"message": "Adicione seu gateway IPFS preferencial"}, "addImportAccount": {"message": "Adicionar conta ou carteira de hardware"}, "addMemo": {"message": "Adicionar observação"}, "addNetwork": {"message": "Adicionar rede"}, "addNetworkConfirmationTitle": {"message": "Adicionar $1", "description": "$1 represents network name"}, "addNewAccount": {"message": "Adicionar uma nova conta Ethereum"}, "addNewEthereumAccountLabel": {"message": "Conta Ethereum"}, "addNewSolanaAccountLabel": {"message": "Conta <PERSON>"}, "addNft": {"message": "Adicionar NFT"}, "addNfts": {"message": "Adicionar NFTs"}, "addNonEvmAccount": {"message": "Adicionar $1 conta", "description": "$1 is the non EVM network where the account is going to be created, e.g. Bitcoin or Solana"}, "addNonEvmAccountFromNetworkPicker": {"message": "Para ativar a rede $1, é necessário criar uma conta $2.", "description": "$1 is the non EVM network where the account is going to be created, e.g. Solana Mainnet or Solana Devnet. $2 is the account type, e.g. Bitcoin or Solana"}, "addRpcUrl": {"message": "Adicionar URL da RPC"}, "addSnapAccountToggle": {"message": "Ativar \"Adicionar <PERSON> conta (Beta)\""}, "addSnapAccountsDescription": {"message": "Ao ativar este recurso, você terá a opção de adicionar os novos Snaps de conta (Beta) direto da sua lista de contas. Se você instalar um Snap de conta, lembre-se de que ele é um serviço terceirizado."}, "addSuggestedNFTs": {"message": "Adicionar NFTs sugeridos"}, "addSuggestedTokens": {"message": "Adicionar tokens sugeridos"}, "addToken": {"message": "Adicionar token"}, "addTokenByContractAddress": {"message": "Não consegue encontrar um token? Você pode adicioná-lo manualmente colando seu endereço. Os endereços de contrato do token se encontram em $1", "description": "$1 is a blockchain explorer for a specific network, e.g. Etherscan for Ethereum"}, "addUrl": {"message": "Adicionar URL"}, "addingAccount": {"message": "Adicionando conta"}, "addingCustomNetwork": {"message": "Adicionar rede"}, "additionalNetworks": {"message": "Redes adicionais"}, "address": {"message": "Endereço"}, "addressCopied": {"message": "Endereço copiado!"}, "addressMismatch": {"message": "Divergência no endereço do site"}, "addressMismatchOriginal": {"message": "URL atual: $1", "description": "$1 replaced by origin URL in confirmation request"}, "addressMismatchPunycode": {"message": "Versão Punycode: $1", "description": "$1 replaced by punycode version of the URL in confirmation request"}, "advanced": {"message": "Avançado"}, "advancedBaseGasFeeToolTip": {"message": "Quando a sua transação for incluída no bloco, qualquer diferença entre a sua taxa-base máxima e a taxa-base real será reembolsada. O cálculo do valor total é feito da seguinte forma: taxa-base máxima (em GWEI) * limite de gás."}, "advancedDetailsDataDesc": {"message": "<PERSON><PERSON>"}, "advancedDetailsHexDesc": {"message": "Hexadecimal"}, "advancedDetailsNonceDesc": {"message": "<PERSON><PERSON>"}, "advancedDetailsNonceTooltip": {"message": "Esse é o número de transação de uma conta. O nonce da primeira transação é 0 e aumenta em ordem sequencial."}, "advancedGasFeeDefaultOptIn": {"message": "Salvar esses valores como padrão para a rede $1.", "description": "$1 is the current network name."}, "advancedGasFeeModalTitle": {"message": "Taxa de gás avançada"}, "advancedGasPriceTitle": {"message": "Preço do gás"}, "advancedPriorityFeeToolTip": {"message": "A taxa de prioridade (ou seja, \"gorjeta do minerador\") vai diretamente para os mineradores e os incentiva a priorizar a sua transação."}, "airDropPatternDescription": {"message": "O histórico do token na rede revela ocorrências anteriores de atividades de airdrop suspeitas."}, "airDropPatternTitle": {"message": "Padrão de airdrop"}, "airgapVault": {"message": "AirGap Vault"}, "alert": {"message": "<PERSON><PERSON><PERSON>"}, "alertAccountTypeUpgradeMessage": {"message": "Você está atualizando sua conta para uma conta inteligente. Você manterá o mesmo endereço de conta, além de desbloquear transações mais rápidas e taxas de rede mais baixas. $1."}, "alertAccountTypeUpgradeTitle": {"message": "Tipo de conta"}, "alertActionBuyWithNativeCurrency": {"message": "Comprar $1"}, "alertActionUpdateGas": {"message": "Atualizar limite de gás"}, "alertActionUpdateGasFee": {"message": "Atualizar taxa"}, "alertActionUpdateGasFeeLevel": {"message": "Atualizar opções de gás"}, "alertDisableTooltip": {"message": "<PERSON><PERSON> pode ser alterado em \"Configurações > Alertas\""}, "alertMessageAddressMismatchWarning": {"message": "Golpistas às vezes imitam os sites fazendo pequenas alterações em seus endereços. Certifique-se de estar interagindo com o site correto antes de continuar."}, "alertMessageChangeInSimulationResults": {"message": "As mudanças estimadas para esta transação foram atualizadas. Revise-as com atenção antes de prosseguir."}, "alertMessageFirstTimeInteraction": {"message": "Esta é sua primeira interação com este endereço. Certifique-se de que ele está correto antes de continuar."}, "alertMessageGasEstimateFailed": {"message": "Não conseguimos fornecer uma taxa precisa, e essa estimativa pode estar alta. Sugerimos que você informe um limite de gás personalizado, mas há o risco de a transação falhar mesmo assim."}, "alertMessageGasFeeLow": {"message": "Ao escolher uma taxa baixa, a expectativa é de transações mais lentas e tempos de espera maiores. Para transações mais rápidas, escolha as opções de taxa Mercado ou Agressiva."}, "alertMessageGasTooLow": {"message": "Para continuar com essa transação, você precisará aumentar o limite de gás para 21000 ou mais."}, "alertMessageInsufficientBalanceWithNativeCurrency": {"message": "Você não tem $1 suficiente em sua conta para pagar as taxas de rede."}, "alertMessageNetworkBusy": {"message": "Os preços do gás são altos e as estimativas são menos precisas."}, "alertMessageNoGasPrice": {"message": "Não podemos prosseguir com essa transação até você atualizar manualmente a taxa."}, "alertMessageSignInDomainMismatch": {"message": "O site solicitante não é o mesmo em que você está entrando. <PERSON><PERSON> pode se tratar de uma tentativa de roubar suas credenciais de login."}, "alertMessageSignInWrongAccount": {"message": "Este site está pedindo que você entre usando a conta incorreta."}, "alertModalAcknowledge": {"message": "Eu reconheço o risco e ainda quero prosseguir"}, "alertModalDetails": {"message": "Detalhes do alerta"}, "alertModalReviewAllAlerts": {"message": "Conferir todos os alertas"}, "alertReasonChangeInSimulationResults": {"message": "Os resultados mudaram"}, "alertReasonFirstTimeInteraction": {"message": "1ª interação"}, "alertReasonGasEstimateFailed": {"message": "Taxa imprecisa"}, "alertReasonGasFeeLow": {"message": "Velocidade lenta"}, "alertReasonGasTooLow": {"message": "Baixo limite de gás"}, "alertReasonInsufficientBalance": {"message": "Fundos insuficientes"}, "alertReasonNetworkBusy": {"message": "Rede ocupada"}, "alertReasonNoGasPrice": {"message": "Estimativa de taxa indisponível"}, "alertReasonPendingTransactions": {"message": "Transação pendente"}, "alertReasonSignIn": {"message": "Solicitação de entrada suspeita"}, "alertReasonWrongAccount": {"message": "Conta incorreta"}, "alertSelectedAccountWarning": {"message": "Esta solicitação é para uma conta diferente daquela selecionada em sua carteira. Para usar outra conta, conecte-a ao site."}, "alerts": {"message": "<PERSON><PERSON><PERSON>"}, "all": {"message": "<PERSON><PERSON>"}, "allNetworks": {"message": "<PERSON><PERSON> as redes"}, "allPermissions": {"message": "<PERSON><PERSON> as permissões"}, "allTimeHigh": {"message": "Alta histórica"}, "allTimeLow": {"message": "Baixa histórica"}, "allowNotifications": {"message": "Permitir <PERSON>"}, "allowWithdrawAndSpend": {"message": "Permitir que $1 saque e gaste até o seguinte valor:", "description": "The url of the site that requested permission to 'withdraw and spend'"}, "amount": {"message": "Valor"}, "amountReceived": {"message": "Valor recebido"}, "amountSent": {"message": "Valor enviado"}, "andForListItems": {"message": "$1 e $2", "description": "$1 is the first item, $2 is the last item in a list of items. Used in Snap Install Warning modal."}, "andForTwoItems": {"message": "$1 e $2", "description": "$1 is the first item, $2 is the second item. Used in Snap Install Warning modal."}, "appDescription": {"message": "A carteira de criptomoedas mais confiável do mundo", "description": "The description of the application"}, "appName": {"message": "NeoNix", "description": "The name of the application"}, "appNameBeta": {"message": "NeoNix Beta", "description": "The name of the application (Beta)"}, "appNameFlask": {"message": "NeoNix Flask", "description": "The name of the application (Flask)"}, "apply": {"message": "Aplicar"}, "approve": {"message": "Aprovar limite de gastos"}, "approveButtonText": {"message": "<PERSON><PERSON><PERSON>"}, "approveIncreaseAllowance": {"message": "Aumentar limite de gastos de $1", "description": "The token symbol that is being approved"}, "approveSpendingCap": {"message": "Aprovar limite de gastos de $1", "description": "The token symbol that is being approved"}, "approved": {"message": "<PERSON><PERSON><PERSON>"}, "approvedOn": {"message": "Aprovada em $1", "description": "$1 is the approval date for a permission"}, "approvedOnForAccounts": {"message": "Aprovada em $1 para $2", "description": "$1 is the approval date for a permission. $2 is the AvatarGroup component displaying account images."}, "areYouSure": {"message": "Tem certeza?"}, "asset": {"message": "Ativo"}, "assetChartNoHistoricalPrices": {"message": "Não foi possível obter dados históricos"}, "assetMultipleNFTsBalance": {"message": "NFTs de $1"}, "assetOptions": {"message": "Opções do ativo"}, "assetSingleNFTBalance": {"message": "NFT de $1"}, "assets": {"message": "Ativos"}, "assetsDescription": {"message": "Detecte automaticamente os tokens em sua carteira, exiba NFTs e receba atualizações de saldo de contas em lote"}, "attemptToCancelSwapForFree": {"message": "Tentar cancelar a troca sem custo"}, "attributes": {"message": "Atributos"}, "attributions": {"message": "Atribuições"}, "auroraRpcDeprecationMessage": {"message": "A URL de RPC (Chamadas de Procedimento Remoto) da Infura não suporta mais Aurora."}, "authorizedPermissions": {"message": "<PERSON><PERSON><PERSON> concedeu as seguintes permissões"}, "autoDetectTokens": {"message": "Detectar tokens automaticamente"}, "autoDetectTokensDescription": {"message": "Usamos APIs de terceiros para detectar e exibir novos tokens enviados à sua carteira. Desative essa opção se não quiser que o app extraia dados desses serviços automaticamente. $1", "description": "$1 is a link to a support article"}, "autoLockTimeLimit": {"message": "Timer com bloqueio automático (minutos)"}, "autoLockTimeLimitDescription": {"message": "Defina o tempo ocioso, em minutos, antes de a NeoNix ser bloqueada."}, "average": {"message": "Média"}, "back": {"message": "Voltar"}, "backupAndSync": {"message": "Backup e sincronização"}, "backupAndSyncBasicFunctionalityNameMention": {"message": "funcionalidade básica"}, "backupAndSyncEnable": {"message": "Ativar backup e sincronização"}, "backupAndSyncEnableConfirmation": {"message": "Ao ativar o backup e a sincronização, você também ativa a $1. Deseja continuar?", "description": "$1 is backupAndSyncBasicFunctionalityNameMention in bold."}, "backupAndSyncEnableDescription": {"message": "O backup e a sincronização nos permitem armazenar dados criptografados para suas configurações e recursos personalizados. Isso mantém a uniformidade de sua experiência com a NeoNix em todos os dispositivos e restaura configurações e recursos se você precisar reinstalar a NeoNix. Não será feito backup da sua Frase de Recuperação Secreta. $1.", "description": "$1 is link to the backup and sync privacy policy."}, "backupAndSyncEnableDescriptionUpdatePreferences": {"message": "Você pode atualizar suas preferências quando quiser em $1", "description": "$1 is a bolded text that highlights the path to the settings page."}, "backupAndSyncEnableDescriptionUpdatePreferencesPath": {"message": "Configurações > Backup e sincronização."}, "backupAndSyncFeatureAccounts": {"message": "<PERSON><PERSON>"}, "backupAndSyncManageWhatYouSync": {"message": "Gere<PERSON><PERSON> o que você sincroniza"}, "backupAndSyncManageWhatYouSyncDescription": {"message": "Ative o que é sincronizado entre seus dispositivos."}, "backupAndSyncPrivacyLink": {"message": "Saiba como protegemos sua privacidade"}, "backupAndSyncSlideDescription": {"message": "Faça backup de suas contas e configurações de sincronização."}, "backupAndSyncSlideTitle": {"message": "Apresentando o backup e a sincronização"}, "backupApprovalInfo": {"message": "Esse código secreto é obrigatório para recuperar sua carteira caso você perca seu dispositivo, esqueça sua senha, precise reinstalar a NeoNix ou queira acessar sua carteira em outro dispositivo."}, "backupApprovalNotice": {"message": "Faça backup da Frase de Recuperação Secreta para manter sua carteira e seus fundos em segurança."}, "backupKeyringSnapReminder": {"message": "Confirme se você consegue acessar por conta própria as contas criadas por esse Snap antes de removê-las"}, "backupNow": {"message": "Fazer backup agora"}, "balance": {"message": "<PERSON><PERSON>"}, "balanceOutdated": {"message": "O saldo pode estar desatualizado"}, "baseFee": {"message": "Taxa-base"}, "basic": {"message": "Básico"}, "basicConfigurationBannerTitle": {"message": "A funcionalidade básica está desativada"}, "basicConfigurationDescription": {"message": "A NeoNix oferece recursos básicos como detalhes de token e configurações de gás por meio de serviços de internet. Quando você usa serviços de internet, seu endereço IP é compartilhado, neste caso com a NeoNix. É exatamente igual a quando você visita qualquer site. A NeoNix usa esses dados temporariamente e nunca os vende. Você pode usar uma VPN ou desligar esses serviços, mas isso poderá afetar sua experiência com a NeoNix. Para saber mais, leia nossa $1.", "description": "$1 is to be replaced by the message for privacyMsg, and will link to https://nnxscan.io"}, "basicConfigurationLabel": {"message": "Funcionalidade básica"}, "basicConfigurationModalCheckbox": {"message": "Entendo e quero continuar"}, "basicConfigurationModalDisclaimerOff": {"message": "Isso significa que você não otimizará totalmente seu tempo na NeoNix. Os recursos básicos (como detalhes de tokens, configurações ideias de gás e outros) não estarão disponíveis."}, "basicConfigurationModalDisclaimerOffAdditionalText": {"message": "Desativar isso também desativa todos os recursos em $1 e as $2.", "description": "$1 and $2 are bold text for basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst and basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast respectively"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst": {"message": "segurança e privacidade, backup e sincronização"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast": {"message": "notificações"}, "basicConfigurationModalDisclaimerOn": {"message": "Para otimizar seu tempo na NeoNix, você precisará ativar este recurso. As funções básicas (como detalhes dos tokens, configurações ideais de gás e outras) são importantes para a experiência na web3."}, "basicConfigurationModalHeadingOff": {"message": "Desativar funcionalidade básica"}, "basicConfigurationModalHeadingOn": {"message": "Ativar funcionalidade básica"}, "bestPrice": {"message": "<PERSON><PERSON>"}, "beta": {"message": "Beta"}, "betaHeaderText": {"message": "Esta é uma versão beta. Pedimos que relatem os bugs $1"}, "betaNeoNixVersion": {"message": "Versão Beta da NeoNix"}, "betaTerms": {"message": "Termos de uso do Beta"}, "billionAbbreviation": {"message": "B", "description": "Shortened form of 'billion'"}, "blockExplorerAccountAction": {"message": "Conta", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Account in Explorer"}, "blockExplorerAssetAction": {"message": "Ativo", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Asset in Explorer"}, "blockExplorerSwapAction": {"message": "Troca", "description": "This is used with viewOnEtherscan e.g View Swap on Etherscan"}, "blockExplorerUrl": {"message": "URL do explorador de blocos"}, "blockExplorerUrlDefinition": {"message": "O URL usado como explorador de blocos para essa rede."}, "blockExplorerView": {"message": "Exibir conta em $1", "description": "$1 replaced by URL for custom block explorer"}, "blockaid": {"message": "Blockaid"}, "blockaidAlertDescriptionBlur": {"message": "Se você continuar, todos os ativos que você listou na Blur podem estar em risco."}, "blockaidAlertDescriptionMalicious": {"message": "Você está interagindo com um site mal-intencionado. Se você continuar, perderá seus ativos."}, "blockaidAlertDescriptionOpenSea": {"message": "Se você continuar, todos os ativos listados na OpenSea podem estar em risco."}, "blockaidAlertDescriptionOthers": {"message": "Se você confirmar essa solicitação, poderá perder seus ativos. Recomendamos que você cancele esta solicitação."}, "blockaidAlertDescriptionTokenTransfer": {"message": "Você está enviando seus ativos para um golpista. Se você continuar, perderá esses ativos."}, "blockaidAlertDescriptionWithdraw": {"message": "Se você confirmar essa solicitação, estará permitindo que um golpista saque e gaste seus ativos. Você não os receberá de volta."}, "blockaidDescriptionApproveFarming": {"message": "Se você aprovar essa solicitação, algum terceiro conhecido por aplicar golpes poderá tomar todos os seus ativos."}, "blockaidDescriptionBlurFarming": {"message": "Se você aprovar essa solicitação, alguém poderá roubar seus ativos listados na Blur."}, "blockaidDescriptionErrored": {"message": "<PERSON>do a um erro, não foi possível verificar os alertas de segurança. Prossiga somente se você confiar em todos os endereços envolvidos."}, "blockaidDescriptionMaliciousDomain": {"message": "Você está interagindo com um domínio mal-intencionado. Se você aprovar essa solicitação, poderá perder seus ativos."}, "blockaidDescriptionMightLoseAssets": {"message": "Se você aprovar essa solicitação, poderá perder seus ativos."}, "blockaidDescriptionSeaportFarming": {"message": "Se você aprovar essa solicitação, alguém poderá roubar seus ativos listados na OpenSea."}, "blockaidDescriptionTransferFarming": {"message": "Se você aprovar essa solicitação, algum terceiro conhecido por aplicar golpes poderá tomar todos os seus ativos."}, "blockaidMessage": {"message": "Proteção de privacidade: nenhum dado é compartilhado com terceiros. Disponível em Arbitrum, Avalanche, BNB Chain, Mainnet da Ethereum, Linea, Optimism, Polygon, Base e Sepolia."}, "blockaidTitleDeceptive": {"message": "Esta solicitação é enganosa"}, "blockaidTitleMayNotBeSafe": {"message": "<PERSON><PERSON> cautela"}, "blockaidTitleSuspicious": {"message": "Esta solicitação é suspeita"}, "blockies": {"message": "Blockies"}, "borrowed": {"message": "<PERSON><PERSON> emprestado"}, "boughtFor": {"message": "Comprado para"}, "bridge": {"message": "<PERSON><PERSON>"}, "bridgeAllowSwappingOf": {"message": "Permitir acesso exato a $1 $2 em $3 para fazer ponte", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "bridgeApproval": {"message": "Aprovar $1 para fazer ponte", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be bridged. $1 is the symbol of a token that has been approved."}, "bridgeApprovalWarning": {"message": "Você está permitindo acesso ao valor especificado, $1 $2. O contrato não acessará nenhum fundo adicional."}, "bridgeApprovalWarningForHardware": {"message": "Será necessário que você permita acesso a $1 $2 para fazer a ponte e em seguida, aprovar a ponte de $2. <PERSON><PERSON> exigirá duas confirmações separadas."}, "bridgeBlockExplorerLinkCopied": {"message": "Link do explorador de blocos copiado!"}, "bridgeCalculatingAmount": {"message": "Calculando..."}, "bridgeConfirmTwoTransactions": {"message": "Será necessário confirmar 2 transações na sua carteira de hardware:"}, "bridgeCreateSolanaAccount": {"message": "<PERSON><PERSON><PERSON> conta <PERSON>"}, "bridgeCreateSolanaAccountDescription": {"message": "Para trocar para a rede Solana, é necessário ter uma conta e um endereço de recebimento."}, "bridgeCreateSolanaAccountTitle": {"message": "<PERSON><PERSON>, você precisará de uma conta Solana."}, "bridgeDetailsTitle": {"message": "<PERSON><PERSON><PERSON> da ponte", "description": "Title for the modal showing details about a bridge transaction."}, "bridgeEnterAmount": {"message": "Selecione o valor"}, "bridgeEnterAmountAndSelectAccount": {"message": "Insira o valor e selecione a conta de destino"}, "bridgeExplorerLinkViewOn": {"message": "Visualizar em $1"}, "bridgeFetchNewQuotes": {"message": "Buscar um novo?"}, "bridgeFrom": {"message": "Realizar ponte de"}, "bridgeFromTo": {"message": "Realizar ponte de $1 $2 para $3", "description": "Tells a user that they need to confirm on their hardware wallet a bridge. $1 is amount of source token, $2 is the source network, and $3 is the destination network"}, "bridgeGasFeesSplit": {"message": "Qualquer taxa de rede cotada na tela anterior inclui ambas as transações e será dividida."}, "bridgeNetCost": {"message": "<PERSON>usto l<PERSON>"}, "bridgeQuoteExpired": {"message": "O prazo da sua cotação esgotou."}, "bridgeSelectDestinationAccount": {"message": "Selecione a conta de destino"}, "bridgeSelectNetwork": {"message": "Selecionar rede"}, "bridgeSelectTokenAmountAndAccount": {"message": "Selecione token, valor e conta de destino"}, "bridgeSelectTokenAndAmount": {"message": "Selecionar token e valor"}, "bridgeSolanaAccountCreated": {"message": "Conta Solana criada"}, "bridgeStatusComplete": {"message": "Conclu<PERSON><PERSON>", "description": "Status text indicating a bridge transaction has successfully completed."}, "bridgeStatusFailed": {"message": "Fal<PERSON>", "description": "Status text indicating a bridge transaction has failed."}, "bridgeStatusInProgress": {"message": "Em andamento", "description": "Status text indicating a bridge transaction is currently processing."}, "bridgeStepActionBridgeComplete": {"message": "$1 recebido em $2", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionBridgePending": {"message": "Recebendo $1 em $2", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionSwapComplete": {"message": "Realizado swap de $1 para $2", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeStepActionSwapPending": {"message": "Realizando swap de $1 para $2", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeTerms": {"message": "Termos"}, "bridgeTimingMinutes": {"message": "$1 mín", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "bridgeTo": {"message": "Realizar ponte para"}, "bridgeToChain": {"message": "Realizar ponte para $1"}, "bridgeTokenCannotVerifyDescription": {"message": "Se você adicionou esse token manualmente, confirme que está ciente dos riscos para seus fundos antes de fazer a ponte."}, "bridgeTokenCannotVerifyTitle": {"message": "Não foi possível confirmar esse token."}, "bridgeTransactionProgress": {"message": "Transação $1 de 2"}, "bridgeTxDetailsBridging": {"message": "Realizando ponte"}, "bridgeTxDetailsDelayedDescription": {"message": "Entre em contato com"}, "bridgeTxDetailsDelayedDescriptionSupport": {"message": "Suporte da NeoNix"}, "bridgeTxDetailsDelayedTitle": {"message": "Já se passaram mais de 3 horas?"}, "bridgeTxDetailsNonce": {"message": "<PERSON><PERSON>"}, "bridgeTxDetailsStatus": {"message": "Status"}, "bridgeTxDetailsTimestamp": {"message": "Carimbo de data/hora"}, "bridgeTxDetailsTimestampValue": {"message": "$1 às $2", "description": "$1 is the date, $2 is the time"}, "bridgeTxDetailsTokenAmountOnChain": {"message": "$1 $2 em", "description": "$1 is the amount of the token, $2 is the ticker symbol of the token"}, "bridgeTxDetailsTotalGasFee": {"message": "Taxa de gás total"}, "bridgeTxDetailsYouReceived": {"message": "<PERSON><PERSON><PERSON> recebeu"}, "bridgeTxDetailsYouSent": {"message": "Voc<PERSON> en<PERSON>u"}, "bridgeValidationInsufficientGasMessage": {"message": "Você não tem $1 suficiente para pagar a taxa de gás para esta ponte. Insira um valor menor ou compre mais $1."}, "bridgeValidationInsufficientGasTitle": {"message": "Necessário mais $1 para gás"}, "bridging": {"message": "Realizando ponte"}, "browserNotSupported": {"message": "Seu navegador não é compatível..."}, "buildContactList": {"message": "Crie sua lista de contatos"}, "builtAroundTheWorld": {"message": "A NeoNix é concebida e desenvolvida em todo o mundo."}, "bulletpoint": {"message": "·"}, "busy": {"message": "Ocupado"}, "buyAndSell": {"message": "Comprar/vender"}, "buyMoreAsset": {"message": "Comprar mais $1", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "buyNow": {"message": "Comprar agora"}, "bytes": {"message": "Bytes"}, "canToggleInSettings": {"message": "Você pode reativar essa notificação em Configurações -> Alertas."}, "cancel": {"message": "<PERSON><PERSON><PERSON>"}, "cancelPopoverTitle": {"message": "Cancelar transação"}, "cancelSpeedUpLabel": {"message": "Essa taxa de gás vai $1 a original.", "description": "$1 is text 'replace' in bold"}, "cancelSpeedUpTransactionTooltip": {"message": "Para $1 uma transação, a taxa de gás deve ser aumentada em pelo menos 10% para que seja reconhecida pela rede.", "description": "$1 is string 'cancel' or 'speed up'"}, "cancelled": {"message": "Cancelada"}, "chainId": {"message": "ID da cadeia"}, "chainIdDefinition": {"message": "O ID da cadeia usado para assinar transações para essa rede."}, "chainIdExistsErrorMsg": {"message": "Esse ID da cadeia é usado pela rede $1."}, "chainListReturnedDifferentTickerSymbol": {"message": "O símbolo deste token não corresponde ao nome ou ID da cadeia inseridos para a rede. Muitos tokens populares apresentam símbolos semelhantes, que podem ser usados por golpistas para induzir você ao erro de enviar um token mais valioso em troca. Verifique todos os detalhes antes de continuar."}, "chooseYourNetwork": {"message": "Escolha sua rede"}, "chooseYourNetworkDescription": {"message": "Quando você utiliza nossas configurações e definições padrão, utilizamos a Infura como nosso provedor padrão de chamada de procedimento remoto (RPC) para oferecer o acesso mais confiável e privado possível aos dados Ethereum. Em alguns casos, podemos utilizar outros provedores de RPC para proporcionar a melhor experiência possível aos nossos usuários. Você pode escolher sua própria RPC, mas lembre-se de que qualquer uma delas receberá seu endereço IP e sua carteira Ethereum para realizar transações. Para saber mais sobre como a Infura trata os dados de contas EVM, leia a nossa $1 e, para contas Solana, $2.", "description": "$1 is a link to the privacy policy, $2 is a link to Solana accounts support"}, "chooseYourNetworkDescriptionCallToAction": {"message": "clique aqui"}, "chromeRequiredForHardwareWallets": {"message": "Você precisa usar a NeoNix no Google Chrome para se conectar com a sua carteira de hardware."}, "circulatingSupply": {"message": "Suprimento em circulação"}, "clear": {"message": "Limpar"}, "clearActivity": {"message": "Limpar dados de atividades e nonce"}, "clearActivityButton": {"message": "Limpar dados da aba de atividades"}, "clearActivityDescription": {"message": "Isso redefinirá o nonce da conta e apagará os dados da aba de atividades em sua carteira. Somente a conta e rede atuais serão afetadas. Seus saldos e transações recebidas não mudarão."}, "click": {"message": "Clique"}, "clickToConnectLedgerViaWebHID": {"message": "Clique aqui para conectar seu Ledger por meio do WebHID", "description": "Text that can be clicked to open a browser popup for connecting the ledger device via webhid"}, "close": {"message": "<PERSON><PERSON><PERSON>"}, "closeExtension": {"message": "<PERSON><PERSON><PERSON>"}, "closeWindowAnytime": {"message": "Você pode fechar esta janela a qualquer momento."}, "coingecko": {"message": "CoinGecko"}, "collectionName": {"message": "Nome da coleção"}, "comboNoOptions": {"message": "Nenhuma opção encontrada", "description": "Default text shown in the combo field dropdown if no options."}, "concentratedSupplyDistributionDescription": {"message": "A maior parte do suprimento de tokens está em posse dos principais detentores do token, o que representa um risco de manipulação centralizada de preços"}, "concentratedSupplyDistributionTitle": {"message": "Suprimento com distribuição concentrada"}, "configureSnapPopupDescription": {"message": "Você está saindo da NeoNix para configurar esse snap."}, "configureSnapPopupInstallDescription": {"message": "Você está saindo da NeoNix para instalar esse snap."}, "configureSnapPopupInstallTitle": {"message": "Instalar snap"}, "configureSnapPopupLink": {"message": "Clique neste link para continuar:"}, "configureSnapPopupTitle": {"message": "Configurar snap"}, "confirm": {"message": "Confirmar"}, "confirmAccountTypeSmartContract": {"message": "Conta inteligente"}, "confirmAccountTypeStandard": {"message": "Conta padrão"}, "confirmAlertModalAcknowledgeMultiple": {"message": "Confirmo que recebi os alertas e ainda quero prosseguir"}, "confirmAlertModalAcknowledgeSingle": {"message": "Reconheço o alerta e quero prosseguir mesmo assim"}, "confirmFieldPaymaster": {"message": "Taxa paga por"}, "confirmFieldTooltipPaymaster": {"message": "A taxa dessa transação será paga pelo contrato inteligente do tesoureiro."}, "confirmGasFeeTokenBalance": {"message": "Saldo:"}, "confirmGasFeeTokenInsufficientBalance": {"message": "Fundos insuficientes"}, "confirmGasFeeTokenNeoNixFee": {"message": "Inclui taxa de $1"}, "confirmGasFeeTokenModalNativeToggleNeoNix": {"message": "A NeoNix está complementando o saldo para realizar esta transação."}, "confirmGasFeeTokenModalNativeToggleWallet": {"message": "Pague a taxa de rede usando o saldo da sua carteira."}, "confirmGasFeeTokenModalPayETH": {"message": "Pagar com ETH"}, "confirmGasFeeTokenModalPayToken": {"message": "Pagar com outros tokens"}, "confirmGasFeeTokenModalTitle": {"message": "Selecione um token"}, "confirmGasFeeTokenToast": {"message": "Você está pagando esta taxa de rede com $1"}, "confirmGasFeeTokenTooltip": {"message": "Este valor é pago à rede para processar sua transação. Inclui uma taxa de $1 da NeoNix para tokens não ETH ou ETH pré-financiado."}, "confirmInfoAccountNow": {"message": "<PERSON><PERSON><PERSON>"}, "confirmInfoSwitchingTo": {"message": "Mudando para"}, "confirmNestedTransactionTitle": {"message": "Transação: $1"}, "confirmPassword": {"message": "Confirmar a senha"}, "confirmRecoveryPhrase": {"message": "Confirmar Frase de Recuperação Secreta"}, "confirmSimulationApprove": {"message": "<PERSON><PERSON><PERSON>"}, "confirmTitleAccountTypeSwitch": {"message": "Atualização da conta"}, "confirmTitleApproveTransactionNFT": {"message": "Solicitação de saque"}, "confirmTitleDeployContract": {"message": "Implementar um contrato"}, "confirmTitleDescApproveTransaction": {"message": "Este site quer permissão para sacar seus NFTs"}, "confirmTitleDescDelegationRevoke": {"message": "Você está voltando para uma conta padrão (EOA)."}, "confirmTitleDescDelegationUpgrade": {"message": "Você está mudando para uma conta inteligente"}, "confirmTitleDescDeployContract": {"message": "Este site quer que você implemente um contrato"}, "confirmTitleDescERC20ApproveTransaction": {"message": "Este site quer permissão para sacar seus tokens"}, "confirmTitleDescPermitSignature": {"message": "Este site quer permissão para gastar seus tokens."}, "confirmTitleDescSIWESignature": {"message": "Um site quer que você faça login para comprovar que é titular desta conta."}, "confirmTitleDescSign": {"message": "Revise os detalhes da solicitação antes de confirmar."}, "confirmTitlePermitTokens": {"message": "Solicitação de limite de gastos"}, "confirmTitleRevokeApproveTransaction": {"message": "Remover permis<PERSON>"}, "confirmTitleSIWESignature": {"message": "Solicitação de entrada"}, "confirmTitleSetApprovalForAllRevokeTransaction": {"message": "Remover permis<PERSON>"}, "confirmTitleSignature": {"message": "Solicitação de assinatura"}, "confirmTitleTransaction": {"message": "Solicitação de transação"}, "confirmationAlertDetails": {"message": "Para proteger seus ativos, é recomendável que você recuse a solicitação."}, "confirmationAlertModalTitleDescription": {"message": "Seus ativos podem estar em risco"}, "confirmed": {"message": "<PERSON><PERSON>rma<PERSON>"}, "confusableUnicode": {"message": "'$1' é similar a '$2'."}, "confusableZeroWidthUnicode": {"message": "Foi encontrado um caractere de tamanho zero."}, "confusingEnsDomain": {"message": "Detectamos um caractere ambíguo no nome ENS. Verifique o nome ENS para evitar um possível golpe."}, "connect": {"message": "Conectar"}, "connectAccount": {"message": "Conectar conta"}, "connectAccountOrCreate": {"message": "Conectar conta ou criar nova"}, "connectAccounts": {"message": "Conectar contas"}, "connectAnAccountHeader": {"message": "Conectar uma conta"}, "connectManually": {"message": "Conectar manualmente ao site atual"}, "connectMoreAccounts": {"message": "Conectar mais contas"}, "connectSnap": {"message": "Conectar $1", "description": "$1 is the snap for which a connection is being requested."}, "connectWithNeoNix": {"message": "Conectar-se com a NeoNix"}, "connectedAccounts": {"message": "Contas conectadas"}, "connectedAccountsDescriptionPlural": {"message": "Você tem $1 contas conectadas a este site.", "description": "$1 is the number of accounts"}, "connectedAccountsDescriptionSingular": {"message": "Você tem 1 conta conectada a este site."}, "connectedAccountsEmptyDescription": {"message": "A NeoNix não está conectada a esse site. Para conectar-se a um site da web3, encontre e clique no botão \"conectar\"."}, "connectedAccountsListTooltip": {"message": "$1 pode ver o saldo, endereço e atividade da conta, além das transações sugeridas para aprovar para contas conectadas.", "description": "$1 is the origin name"}, "connectedAccountsToast": {"message": "As contas conectadas foram atualizadas"}, "connectedSites": {"message": "Sites conectados"}, "connectedSitesAndSnaps": {"message": "Sites e Snaps conectados"}, "connectedSitesDescription": {"message": "$1 está conectada a esses sites. Eles podem visualizar o endereço da sua conta.", "description": "$1 is the account name"}, "connectedSitesEmptyDescription": {"message": "$1 não está conectada a nenhum site.", "description": "$1 is the account name"}, "connectedSnapAndNoAccountDescription": {"message": "A NeoNix está conectada a este site, mas nenhuma conta está conectada ainda"}, "connectedSnaps": {"message": "Snaps conectados"}, "connectedWithAccount": {"message": "$1 contas conectadas", "description": "$1 represents account length"}, "connectedWithAccountName": {"message": "Conectado com $1", "description": "$1 represents account name"}, "connectedWithNetwork": {"message": "$1 redes conectadas", "description": "$1 represents network length"}, "connectedWithNetworkName": {"message": "Conectado com $1", "description": "$1 represents network name"}, "connecting": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "connectingTo": {"message": "Conectando a $1"}, "connectingToDeprecatedNetwork": {"message": "'$1' está sendo descontinuada e poderá não funcionar. Tente outra rede."}, "connectingToGoerli": {"message": "Conectando à rede de teste Goerli"}, "connectingToLineaGoerli": {"message": "Conectando à rede de teste Linea Goerli"}, "connectingToLineaMainnet": {"message": "Conectando-se à Mainnet do Linea"}, "connectingToLineaSepolia": {"message": "Conectando à rede de teste Linea Sepolia"}, "connectingToMainnet": {"message": "Conectando à Mainnet da Ethereum"}, "connectingToSepolia": {"message": "Conectando à rede de teste Sepolia"}, "connectionDescription": {"message": "Conectar este site com a NeoNix"}, "connectionFailed": {"message": "Falha na conexão"}, "connectionFailedDescription": {"message": "Falha ao buscar $1. Verifique sua rede e tente de novo.", "description": "$1 is the name of the snap being fetched."}, "connectionPopoverDescription": {"message": "Para se conectar a um site, selecione o botão \"conectar\". A NeoNix só pode se conectar a sites Web3."}, "connectionRequest": {"message": "Solicitação de conexão"}, "contactUs": {"message": "Fale conosco"}, "contacts": {"message": "Contatos"}, "contentFromSnap": {"message": "Conteúdo de $1", "description": "$1 represents the name of the snap"}, "continue": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "contract": {"message": "Contrato"}, "contractAddress": {"message": "Endereço do contrato"}, "contractAddressError": {"message": "Você está enviando tokens ao endereço de contrato do token. <PERSON><PERSON> pode resultar na perda desses tokens."}, "contractDeployment": {"message": "Implementação do contrato"}, "contractInteraction": {"message": "Interação com o contrato"}, "convertTokenToNFTDescription": {"message": "Detectamos que esse ativo é um NFT. A NeoNix agora oferece suporte nativo a NFTs. Gostaria de removê-lo de sua lista de tokens e adicioná-lo como NFT?"}, "convertTokenToNFTExistDescription": {"message": "Detectamos que esse ativo foi adicionado como NFT. Deseja removê-lo da sua lista de tokens?"}, "coolWallet": {"message": "CoolWallet"}, "copiedExclamation": {"message": "Copiado."}, "copyAddress": {"message": "Copiar endereço para a área de transferência"}, "copyAddressShort": {"message": "<PERSON><PERSON><PERSON>"}, "copyPrivateKey": {"message": "Copiar chave privada"}, "copyToClipboard": {"message": "Copiar para a área de transferência"}, "copyTransactionId": {"message": "Copiar ID da transação"}, "create": {"message": "<PERSON><PERSON><PERSON>"}, "createNewAccountHeader": {"message": "Crie uma conta"}, "createPassword": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "createSnapAccountDescription": {"message": "$1 quer adicionar uma nova conta à NeoNix."}, "createSnapAccountTitle": {"message": "C<PERSON><PERSON> conta"}, "createSolanaAccount": {"message": "<PERSON><PERSON><PERSON> conta <PERSON>"}, "creatorAddress": {"message": "Endereço do criador"}, "crossChainSwapsLink": {"message": "Faça trocas entre redes com o NeoNix Portfolio"}, "crossChainSwapsLinkNative": {"message": "Realize swap entre redes com Ponte"}, "cryptoCompare": {"message": "CryptoCompare"}, "currencyConversion": {"message": "<PERSON><PERSON>"}, "currencyRateCheckToggle": {"message": "Exibir saldo e verificador de preços de tokens"}, "currencyRateCheckToggleDescription": {"message": "Usamos as APIs $1 e $2 para mostrar o seu saldo e o preço dos tokens. $3", "description": "$1 represents Coingecko, $2 represents CryptoCompare and $3 represents Privacy Policy"}, "currencySymbol": {"message": "Símbolo da moeda"}, "currencySymbolDefinition": {"message": "O símbolo do ticker exibido para a moeda dessa rede."}, "currentAccountNotConnected": {"message": "Sua conta atual não está conectada"}, "currentExtension": {"message": "Página atual da extensão"}, "currentLanguage": {"message": "Idioma atual"}, "currentNetwork": {"message": "Rede atual", "description": "Speicifies to token network filter to filter by current Network. Will render when network nickname is not available"}, "currentRpcUrlDeprecated": {"message": "O atual URL da RPC para essa rede foi descontinuado."}, "currentTitle": {"message": "Atual:"}, "currentlyUnavailable": {"message": "Indisponível nessa rede"}, "curveHighGasEstimate": {"message": "Gráfico de estimativa agressiva de gás"}, "curveLowGasEstimate": {"message": "Gráfico de estimativa reduzida de gás"}, "curveMediumGasEstimate": {"message": "Gráfico de estimativa de gás do mercado"}, "custom": {"message": "Avançado"}, "customGasSettingToolTipMessage": {"message": "Use $1 para personalizar o preço do gás. <PERSON><PERSON> pode parecer confuso se você não estiver familiarizado. Interaja por sua conta e risco.", "description": "$1 is key 'advanced' (text: 'Advanced') separated here so that it can be passed in with bold font-weight"}, "customSlippage": {"message": "Personalizado"}, "customSpendLimit": {"message": "Limite de gastos personalizado"}, "customToken": {"message": "Token personalizado"}, "customTokenWarningInNonTokenDetectionNetwork": {"message": "A detecção de tokens ainda não está disponível nesta rede. Por favor, importe o token manualmente e certifique-se de que ele é confiável. Saiba mais sobre $1"}, "customTokenWarningInTokenDetectionNetwork": {"message": "Qualquer pessoa pode criar um token, inclusive versões falsas de tokens existentes. Saiba mais sobre $1"}, "customTokenWarningInTokenDetectionNetworkWithTDOFF": {"message": "Certifique-se de que confia no token antes de importá-lo. Saiba como evitar $1. Você também pode ativar a detecção de tokens $2."}, "customerSupport": {"message": "suporte ao cliente"}, "customizeYourNotifications": {"message": "Personalize suas notificações"}, "customizeYourNotificationsText": {"message": "Ative os tipos de notificação que deseja receber:"}, "dappSuggested": {"message": "Site sugerido"}, "dappSuggestedGasSettingToolTipMessage": {"message": "$1 sugeriu esse preço.", "description": "$1 is url for the dapp that has suggested gas settings"}, "dappSuggestedHigh": {"message": "Site sugerido"}, "dappSuggestedHighShortLabel": {"message": "Site (alto)"}, "dappSuggestedShortLabel": {"message": "Site"}, "dappSuggestedTooltip": {"message": "$1 recomendou esse preço.", "description": "$1 represents the Dapp's origin"}, "darkTheme": {"message": "Escuro"}, "data": {"message": "<PERSON><PERSON>"}, "dataCollectionForMarketing": {"message": "Coleta de dados para marketing"}, "dataCollectionForMarketingDescription": {"message": "Usaremos o MetaMetrics para saber como você interage com nossas comunicações de marketing. Poderemos compartilhar novidades relevantes (como recursos de produtos e outros materiais)."}, "dataCollectionWarningPopoverButton": {"message": "OK"}, "dataCollectionWarningPopoverDescription": {"message": "Você desativou a coleta de dados para fins de marketing. Isso é aplicável apenas a este dispositivo. Se você usa a NeoNix em outros dispositivos, desative-a neles também."}, "dataUnavailable": {"message": "dados não disponíveis"}, "dateCreated": {"message": "Data de criação"}, "dcent": {"message": "<PERSON><PERSON><PERSON>nt"}, "debitCreditPurchaseOptions": {"message": "Opções de compra com cartão de débito ou crédito"}, "decimal": {"message": "Decimal do token"}, "decimalsMustZerotoTen": {"message": "Decimais devem ser no mínimo 0 e não passar de 36."}, "decrypt": {"message": "Descriptografar"}, "decryptCopy": {"message": "Copiar mensagem criptografada"}, "decryptInlineError": {"message": "Essa mensagem não pode ser descriptografada devido ao seguinte erro: $1", "description": "$1 is error message"}, "decryptMessageNotice": {"message": "$1 gostaria de ler essa mensagem para concluir sua ação", "description": "$1 is the web3 site name"}, "decryptNeoNix": {"message": "Descriptografar mensagem"}, "decryptRequest": {"message": "Solicitação de descriptografia"}, "defaultRpcUrl": {"message": "URL padrão da RPC"}, "defaultSettingsSubTitle": {"message": "A NeoNix usa as configurações padrão para melhor equilibrar a segurança e a facilidade de uso. Altere essas configurações para aumentar ainda mais sua privacidade."}, "defaultSettingsTitle": {"message": "Configurações de privacidade padrão"}, "defi": {"message": "<PERSON><PERSON><PERSON>"}, "defiTabErrorContent": {"message": "Tente acessar novamente mais tarde."}, "defiTabErrorTitle": {"message": "Não foi possível carregar esta página."}, "delete": {"message": "Excluir"}, "deleteContact": {"message": "Excluir contato"}, "deleteMetaMetricsData": {"message": "Excluir dados do MetaMetrics"}, "deleteMetaMetricsDataDescription": {"message": "Isso excluirá dados históricos do MetaMetrics associados ao seu uso neste dispositivo. Sua carteira e contas continuarão exatamente como estão agora após a exclusão desses dados. Esse processo pode levar até 30 dias. Veja nossa $1.", "description": "$1 will have text saying Privacy Policy "}, "deleteMetaMetricsDataErrorDesc": {"message": "Não é possível atender a essa solicitação no momento devido a um problema no servidor do sistema de análises. Tente novamente mais tarde"}, "deleteMetaMetricsDataErrorTitle": {"message": "Não é possível excluir estes dados no momento"}, "deleteMetaMetricsDataModalDesc": {"message": "Estamos prestes a remover todos os seus dados do MetaMetrics. Tem certeza?"}, "deleteMetaMetricsDataModalTitle": {"message": "Excluir dados do MetaMetrics?"}, "deleteMetaMetricsDataRequestedDescription": {"message": "Você iniciou essa ação em $1. Esse processo pode levar até 30 dias. Consulte a $2", "description": "$1 will be the date on which teh deletion is requested and $2 will have text saying Privacy Policy "}, "deleteNetworkIntro": {"message": "Se excluir essa rede, você precisará adicioná-la novamente para ver seus ativos nela"}, "deleteNetworkTitle": {"message": "Excluir rede $1?", "description": "$1 represents the name of the network"}, "depositCrypto": {"message": "Deposite criptomoedas de outra conta com um endereço de carteira ou código QR."}, "deprecatedGoerliNtwrkMsg": {"message": "Devido a atualizações no sistema Ethereum, a rede de teste Goerli será descontinuada em breve."}, "deprecatedNetwork": {"message": "Essa rede foi descontinuada"}, "deprecatedNetworkButtonMsg": {"message": "<PERSON><PERSON><PERSON>"}, "deprecatedNetworkDescription": {"message": "A rede à qual você está tentando conectar-se não é mais suportada pela NeoNix. $1"}, "description": {"message": "Descrição"}, "descriptionFromSnap": {"message": "Descrição de $1", "description": "$1 represents the name of the snap"}, "destinationAccountPickerNoEligible": {"message": "Nenhuma conta qualificada encontrada"}, "destinationAccountPickerNoMatching": {"message": "Nenhuma conta correspondentes encontrada"}, "destinationAccountPickerReceiveAt": {"message": "Receber em"}, "destinationAccountPickerSearchPlaceholderToMainnet": {"message": "Endereço de recebimento ou ENS"}, "destinationAccountPickerSearchPlaceholderToSolana": {"message": "Endereço de recebimento"}, "destinationTransactionIdLabel": {"message": "ID da transação de destino", "description": "Label for the destination transaction ID field."}, "details": {"message": "<PERSON><PERSON><PERSON>"}, "developerOptions": {"message": "Opções para desenvolvedores"}, "disabledGasOptionToolTipMessage": {"message": "“$1” está desativado porque não satisfaz o aumento mínimo de 10% em relação à taxa de gás original.", "description": "$1 is gas estimate type which can be market or aggressive"}, "disconnect": {"message": "Desconectar"}, "disconnectAllAccounts": {"message": "Desconectar todas as contas"}, "disconnectAllAccountsConfirmationDescription": {"message": "Quer mesmo desconectar? Você pode perder a funcionalidade do site."}, "disconnectAllAccountsText": {"message": "contas"}, "disconnectAllDescriptionText": {"message": "Se você se desconectar deste site, precisará reconectar suas contas e redes para usar este site novamente."}, "disconnectAllSnapsText": {"message": "Snaps"}, "disconnectMessage": {"message": "Isso desconectará você deste site"}, "disconnectPrompt": {"message": "Desconectar $1"}, "disconnectThisAccount": {"message": "Desconectar esta conta"}, "disconnectedAllAccountsToast": {"message": "<PERSON><PERSON> as contas foram desconectadas de $1", "description": "$1 is name of the dapp`"}, "disconnectedSingleAccountToast": {"message": "$1 desconectada de $2", "description": "$1 is name of the name and $2 represents the dapp name`"}, "discover": {"message": "Descobrir"}, "discoverSnaps": {"message": "Descobrir Snaps", "description": "Text that links to the Snaps website. Displayed in a banner on Snaps list page in settings."}, "dismiss": {"message": "Descar<PERSON>"}, "dismissReminderDescriptionField": {"message": "Ative isso para descartar a mensagem de lembrete de backup da Frase de Recuperação Secreta. Recomendamos enfaticamente que você faça o backup da sua Frase de Recuperação Secreta para evitar a perda de fundos"}, "dismissReminderField": {"message": "Descartar o lembrete de backup da Frase de Recuperação Secreta"}, "dismissSmartAccountSuggestionEnabledDescription": {"message": "Ative esta opção para não ver mais a sugestão \"Mudar para conta inteligente\" em nenhuma conta. Contas inteligentes permitem transações mais rápidas, taxas de rede mais baixas e maior flexibilidade em seu pagamento."}, "dismissSmartAccountSuggestionEnabledTitle": {"message": "<PERSON><PERSON><PERSON> sugestão \"Mudar para conta inteligente\""}, "displayNftMedia": {"message": "Exibir arquivos de mídia de NFTs"}, "displayNftMediaDescription": {"message": "Exibir arquivos de mídia e dados de NFTs expõe seu endereço IP à OpenSea ou outros terceiros. Isso pode possibilitar que invasores associem seu endereço IP ao seu endereço Ethereum. A detecção automática de NFTs depende dessa configuração e ficará indisponível quando ela estiver desativada."}, "doNotShare": {"message": "Não compartilhe isso com ninguém"}, "domain": {"message": "<PERSON><PERSON><PERSON>"}, "done": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dontShowThisAgain": {"message": "<PERSON><PERSON> exibir isso novamente"}, "downArrow": {"message": "seta para baixo"}, "downloadGoogleChrome": {"message": "Baixar o Google Chrome"}, "downloadNow": {"message": "Baixar agora"}, "downloadStateLogs": {"message": "Baixar logs de estado"}, "dragAndDropBanner": {"message": "<PERSON>oc<PERSON> pode arrastar as redes para reordená-las. "}, "dropped": {"message": "Abandonada"}, "duplicateContactTooltip": {"message": "Este nome de contato já está em uso por uma conta ou contato existente"}, "duplicateContactWarning": {"message": "Você tem contatos duplicados"}, "durationSuffixDay": {"message": "D", "description": "Shortened form of 'day'"}, "durationSuffixHour": {"message": "H", "description": "Shortened form of 'hour'"}, "durationSuffixMillisecond": {"message": "MS", "description": "Shortened form of 'millisecond'"}, "durationSuffixMinute": {"message": "M", "description": "Shortened form of 'minute'"}, "durationSuffixMonth": {"message": "M", "description": "Shortened form of 'month'"}, "durationSuffixSecond": {"message": "S", "description": "Shortened form of 'second'"}, "durationSuffixWeek": {"message": "S", "description": "Shortened form of 'week'"}, "durationSuffixYear": {"message": "A", "description": "Shortened form of 'year'"}, "earn": {"message": "Ganhar"}, "edit": {"message": "<PERSON><PERSON>"}, "editANickname": {"message": "<PERSON><PERSON>"}, "editAccounts": {"message": "<PERSON><PERSON> contas"}, "editAddressNickname": {"message": "Editar apelido do endereço"}, "editCancellationGasFeeModalTitle": {"message": "Editar taxa de gás por cancelamento"}, "editContact": {"message": "Editar contato"}, "editGasFeeModalTitle": {"message": "Editar taxa de gás"}, "editGasLimitOutOfBounds": {"message": "O limite de gás deve ser de pelo menos $1"}, "editGasLimitOutOfBoundsV2": {"message": "O limite de gás deve ser superior a $1 e inferior a $2", "description": "$1 is the minimum limit for gas and $2 is the maximum limit"}, "editGasLimitTooltip": {"message": "O limite de gás são as unidades máximas de gás que você está disposto a utilizar. Unidades de gás são um multiplicador para “Taxa de prioridade máxima” e “Taxa máxima”."}, "editGasMaxBaseFeeGWEIImbalance": {"message": "A taxa-base máxima não pode ser inferior à taxa de prioridade"}, "editGasMaxBaseFeeHigh": {"message": "A taxa-base máxima está mais elevada que o necessário"}, "editGasMaxBaseFeeLow": {"message": "A taxa-base máxima está baixa para as condições atuais da rede"}, "editGasMaxFeeHigh": {"message": "A taxa máxima está mais elevada que o necessário"}, "editGasMaxFeeLow": {"message": "A taxa máxima está muito baixa para as condições da rede"}, "editGasMaxFeePriorityImbalance": {"message": "A taxa máxima não pode ser inferior à taxa de prioridade máxima"}, "editGasMaxPriorityFeeBelowMinimum": {"message": "A taxa de prioridade máxima deve ser superior a 0 GWEI"}, "editGasMaxPriorityFeeBelowMinimumV2": {"message": "A taxa de prioridade deve ser superior a 0."}, "editGasMaxPriorityFeeHigh": {"message": "A taxa de prioridade máxima está mais alta que o necessário. Talvez você pague mais que o necessário."}, "editGasMaxPriorityFeeHighV2": {"message": "A taxa de prioridade está mais alta que o necessário. Talvez você pague mais que o necessário"}, "editGasMaxPriorityFeeLow": {"message": "A taxa de prioridade máxima está baixa para as condições atuais da rede"}, "editGasMaxPriorityFeeLowV2": {"message": "A taxa de prioridade está baixa para as condições atuais da rede"}, "editGasPriceTooLow": {"message": "O preço do gás deve ser superior a 0"}, "editGasPriceTooltip": {"message": "Essa rede requer um campo de \"preço do gás\" ao enviar uma transação. O preço do gás é o valor pago por unidade de gás."}, "editGasSubTextFeeLabel": {"message": "Taxa máxima:"}, "editGasTitle": {"message": "Editar prioridade"}, "editGasTooLow": {"message": "Tempo de processamento desconhecido"}, "editInPortfolio": {"message": "Editar no portfólio"}, "editNetworkLink": {"message": "editar a rede original"}, "editNetworksTitle": {"message": "<PERSON>ar redes"}, "editNonceField": {"message": "<PERSON><PERSON> nonce"}, "editNonceMessage": {"message": "Esse é um recurso avançado; use com cautela."}, "editPermission": {"message": "<PERSON><PERSON>"}, "editPermissions": {"message": "<PERSON><PERSON>"}, "editSpeedUpEditGasFeeModalTitle": {"message": "Editar taxa de gás para aceleração"}, "editSpendingCap": {"message": "Editar limite de gastos"}, "editSpendingCapAccountBalance": {"message": "Saldo da conta: $1 $2"}, "editSpendingCapDesc": {"message": "Insira o valor que você considera adequado que seja gasto em seu nome."}, "editSpendingCapError": {"message": "O limite de gastos não pode exceder $1 dígitos decimais. Remova os dígitos decimais para continuar."}, "editSpendingCapSpecialCharError": {"message": "Insira somente números"}, "enableAutoDetect": {"message": " Ativar detecção automática"}, "enableFromSettings": {"message": " Ative nas Configurações."}, "enableSnap": {"message": "Ativar"}, "enableToken": {"message": "ativar $1", "description": "$1 is a token symbol, e.g. ETH"}, "enabled": {"message": "<PERSON><PERSON>do"}, "enabledNetworks": {"message": "Redes habilitadas"}, "encryptionPublicKeyNotice": {"message": "$1 gostaria da sua chave pública de criptografia. Ao consentir, este site conseguirá redigir mensagens criptografadas para você.", "description": "$1 is the web3 site name"}, "encryptionPublicKeyRequest": {"message": "Solicitar chave pública de criptografia"}, "endpointReturnedDifferentChainId": {"message": "O URL da RPC inserido retornou um ID de cadeia diferente ($1).", "description": "$1 is the return value of eth_chainId from an RPC endpoint"}, "enhancedTokenDetectionAlertMessage": {"message": "A detecção aprimorada de tokens está disponível no momento em $1. $2"}, "ensDomainsSettingDescriptionIntroduction": {"message": "A NeoNix permite que você veja domínios ENS direto na barra de endereços do seu navegador. Veja como funciona:"}, "ensDomainsSettingDescriptionOutroduction": {"message": "Tenha em mente que usar esse recurso expõe seu endereço IP a serviços de IPFS de terceiros."}, "ensDomainsSettingDescriptionPart1": {"message": "A NeoNix verifica o contrato ENS da Ethereum para encontrar o código conectado ao nome ENS."}, "ensDomainsSettingDescriptionPart2": {"message": "Se o código estiver vinculado ao IPFS, você poderá ver o conteúdo associado a ele (geralmente um site)."}, "ensDomainsSettingTitle": {"message": "Exibir domínios ENS na barra de endereço"}, "ensUnknownError": {"message": "Falha na busca de ENS."}, "enterANameToIdentifyTheUrl": {"message": "Insira um nome para identificar o URL"}, "enterChainId": {"message": "Insira a ID da cadeia"}, "enterMaxSpendLimit": {"message": "Digite um limite máximo de gastos"}, "enterNetworkName": {"message": "Insira o nome da rede"}, "enterOptionalPassword": {"message": "Insira a senha opcional"}, "enterPasswordContinue": {"message": "Insira a senha para continuar"}, "enterRpcUrl": {"message": "Insira o URL da RPC"}, "enterSymbol": {"message": "Insira o símbolo"}, "enterTokenNameOrAddress": {"message": "Insira o nome do token ou cole o endereço"}, "enterYourPassword": {"message": "Insira sua senha"}, "errorCode": {"message": "Código: $1", "description": "Displayed error code for debugging purposes. $1 is the error code"}, "errorGettingSafeChainList": {"message": "Erro ao obter uma lista segura da cadeia. Por favor, prossiga com cautela."}, "errorMessage": {"message": "Mensagem: $1", "description": "Displayed error message for debugging purposes. $1 is the error message"}, "errorName": {"message": "Código: $1", "description": "Displayed error name for debugging purposes. $1 is the error name"}, "errorPageContactSupport": {"message": "Falar com o suporte", "description": "Button for contact MM support"}, "errorPageDescribeUsWhatHappened": {"message": "Descreva o que aconteceu", "description": "<PERSON><PERSON> for submitting report to sentry"}, "errorPageInfo": {"message": "Suas informações não podem ser exibidas. Não se preocupe, sua carteira e fundos estão seguros.", "description": "Information banner shown in the error page"}, "errorPageMessageTitle": {"message": "Mensagem de erro", "description": "Title for description, which is displayed for debugging purposes"}, "errorPageSentryFormTitle": {"message": "Descreva o que aconteceu", "description": "In sentry feedback form, The title at the top of the feedback form."}, "errorPageSentryMessagePlaceholder": {"message": "Compartilhar detalhes como a forma de reproduzir o erro nos ajudará a corrigir o problema.", "description": "In sentry feedback form, The placeholder for the feedback description input field."}, "errorPageSentrySuccessMessageText": {"message": "Obrigado! Verificaremos em breve.", "description": "In sentry feedback form, The message displayed after a successful feedback submission."}, "errorPageTitle": {"message": "A NeoNix encontrou um erro", "description": "Title of generic error page"}, "errorPageTryAgain": {"message": "Tentar novamente", "description": "<PERSON><PERSON> for try again"}, "errorStack": {"message": "Lista:", "description": "Title for error stack, which is displayed for debugging purposes"}, "errorWhileConnectingToRPC": {"message": "Erro ao conectar à rede personalizada."}, "errorWithSnap": {"message": "Erro com $1", "description": "$1 represents the name of the snap"}, "estimatedFee": {"message": "Taxa estimada"}, "estimatedFeeTooltip": {"message": "Valor pago para processar a transação na rede."}, "ethGasPriceFetchWarning": {"message": "O preço de backup do gás é fornecido porque a estimativa de gás principal está indisponível no momento."}, "ethereumProviderAccess": {"message": "Conceder ao Ethereum acesso de provedor a $1", "description": "The parameter is the name of the requesting origin"}, "ethereumPublicAddress": {"message": "Endereço público Ethereum"}, "etherscan": {"message": "Etherscan"}, "etherscanView": {"message": "Ver conta no Etherscan"}, "etherscanViewOn": {"message": "Ver no Etherscan"}, "existingChainId": {"message": "As informações que você inseriu estão associadas a um ID de cadeia existente."}, "expandView": {"message": "Expandir exibição"}, "experimental": {"message": "Experimental"}, "exploreweb3": {"message": "Explore a Web3"}, "exportYourData": {"message": "Exportar seus dados"}, "exportYourDataButton": {"message": "Baixar"}, "exportYourDataDescription": {"message": "Você pode exportar dados como seus contatos e preferências."}, "extendWalletWithSnaps": {"message": "Explore Snaps desenvolvidos pela comunidade para personalizar sua experiência na web3", "description": "Banner description displayed on Snaps list page in Settings when less than 6 Snaps is installed."}, "externalAccount": {"message": "Conta externa"}, "externalExtension": {"message": "Extensão externa"}, "externalNameSourcesSetting": {"message": "Apelidos propostos"}, "externalNameSourcesSettingDescription": {"message": "Buscaremos os apelidos propostos para os endereços com os quais você interage em fontes terceirizadas como Etherscan, Infura e Lens Protocol. Essas fontes poderão ver esses endereços e o seu endereço IP. O endereço da sua conta não será exposto a terceiros."}, "failed": {"message": "<PERSON><PERSON><PERSON>"}, "failedToFetchChainId": {"message": "Não foi possível buscar o ID da cadeia. Seu URL da RPC está correto?"}, "failover": {"message": "Failover"}, "failoverRpcUrl": {"message": "URL de failover da RPC"}, "failureMessage": {"message": "Ocorreu algum erro e não conseguimos concluir a ação"}, "fast": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "feeDetails": {"message": "Detalhes da <PERSON>"}, "fileImportFail": {"message": "A importação de arquivo não está funcionando? Clique aqui!", "description": "Helps user import their account from a JSON file"}, "flaskWelcomeUninstall": {"message": "você deve desinstalar essa extensão", "description": "This request is shown on the Flask Welcome screen. It is intended for non-developers, and will be bolded."}, "flaskWelcomeWarning1": {"message": "O Flask é para desenvolvedores experimentarem novas APIs instáveis. A menos que você seja um desenvolvedor ou beta tester, $1.", "description": "This is a warning shown on the Flask Welcome screen, intended to encourage non-developers not to proceed any further. $1 is the bolded message 'flaskWelcomeUninstall'"}, "flaskWelcomeWarning2": {"message": "Não damos garantias sobre a segurança ou a estabilidade dessa extensão. As novas APIs oferecidas pelo Flask não estão protegidas contra ataques de phishing, ou seja, qualquer site ou snap que requeira o Flask pode ser uma tentativa mal-intencionada de roubar seus ativos.", "description": "This explains the risks of using NeoNix Flask"}, "flaskWelcomeWarning3": {"message": "Todas as APIs do Flask são experimentais. Elas podem ser alteradas ou removidas sem aviso prévio, ou podem permanecer no Flask indefinidamente, sem jamais serem migradas para a NeoNix estável. Use-as com cautela.", "description": "This message warns developers about unstable Flask APIs"}, "flaskWelcomeWarning4": {"message": "Certifique-se de desativar sua extensão NeoNix regular ao usar o Flask.", "description": "This message calls to pay attention about multiple versions of NeoNix running on the same site (Flask + Prod)"}, "flaskWelcomeWarningAcceptButton": {"message": "Eu aceito os riscos", "description": "this text is shown on a button, which the user presses to confirm they understand the risks of using Flask"}, "floatAmountToken": {"message": "A quantidade de tokens deve ser um número inteiro"}, "followUsOnTwitter": {"message": "Siga-nos no Twitter"}, "forbiddenIpfsGateway": {"message": "Gateway IPFS proibido: especifique um gateway de CID"}, "forgetDevice": {"message": "Esquecer este dispositivo"}, "forgotPassword": {"message": "Esque<PERSON>u a senha?"}, "form": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "from": {"message": "De"}, "fromAddress": {"message": "De: $1", "description": "$1 is the address to include in the From label. It is typically shortened first using shortenAddress"}, "fromTokenLists": {"message": "Das listas de tokens: $1"}, "function": {"message": "Função: $1"}, "fundingMethod": {"message": "Forma de financiamento"}, "gas": {"message": "Gás"}, "gasDisplayAcknowledgeDappButtonText": {"message": "Editar taxa de gás sugerida"}, "gasDisplayDappWarning": {"message": "Essa taxa de gás foi sugerida por $1. Sua substituição pode causar um problema com a sua transação. Entre em contato com $1 se tiver perguntas.", "description": "$1 represents the Dapp's origin"}, "gasFee": {"message": "Taxa de gás"}, "gasLimit": {"message": "Limite de gás"}, "gasLimitRecommended": {"message": "O limite de gás recomendado é de $1. Um limite de gás inferior pode resultar em falha."}, "gasLimitTooLow": {"message": "O limite de gás deve ser de pelo menos 21000"}, "gasLimitV2": {"message": "Limite de gás"}, "gasOption": {"message": "Opção de gás"}, "gasPriceExcessive": {"message": "Sua taxa de gás está desnecessariamente alta. Considere reduzir o valor."}, "gasPriceFetchFailed": {"message": "Ocorreu uma falha na estimativa do preço do gás devido a um erro na rede."}, "gasTimingHoursShort": {"message": "$1 h", "description": "$1 represents a number of hours"}, "gasTimingLow": {"message": "<PERSON><PERSON>"}, "gasTimingMinutesShort": {"message": "$1 min", "description": "$1 represents a number of minutes"}, "gasTimingSecondsShort": {"message": "$1 s", "description": "$1 represents a number of seconds"}, "gasUsed": {"message": "<PERSON>ás usado"}, "general": {"message": "G<PERSON>"}, "generalCameraError": {"message": "Não pudemos acessar sua câmera. Por favor, tente de novo."}, "generalCameraErrorTitle": {"message": "Algo deu errado..."}, "generalDescription": {"message": "Sincronize as configurações entre dispositivos, selecione as preferências de rede e rastreie dados de tokens"}, "genericExplorerView": {"message": "Ver conta na $1"}, "goToSite": {"message": "Ir ao site"}, "goerli": {"message": "Rede de teste Goerli"}, "gotIt": {"message": "<PERSON><PERSON><PERSON>"}, "grantExactAccess": {"message": "Conceder ace<PERSON> exato"}, "gwei": {"message": "GWEI"}, "hardware": {"message": "Hardware"}, "hardwareWalletConnected": {"message": "Carteira de hardware conectada"}, "hardwareWalletLegacyDescription": {"message": "(antigo)", "description": "Text representing the MEW path"}, "hardwareWalletSubmissionWarningStep1": {"message": "Certifique-se de conectar sua $1 e de selecionar o app Ethereum."}, "hardwareWalletSubmissionWarningStep2": {"message": "Ative \"dados de contrato inteligente\" ou \"assinatura cega\" no seu dispositivo $1."}, "hardwareWalletSubmissionWarningTitle": {"message": "Antes de clicar em enviar:"}, "hardwareWalletSupportLinkConversion": {"message": "clique aqui"}, "hardwareWallets": {"message": "Conecte uma carteira de hardware"}, "hardwareWalletsInfo": {"message": "As integrações com carteiras de hardware usam chamadas de API para servidores externos, os quais podem ver seu endereço IP e os endereços de contratos inteligentes com os quais você interage."}, "hardwareWalletsMsg": {"message": "Selecione uma carteira de hardware que você gostaria de usar com a NeoNix."}, "here": {"message": "aqui", "description": "as in -click here- for more information (goes with troubleTokenBalances)"}, "hexData": {"message": "Dados hexa"}, "hiddenAccounts": {"message": "Contas ocultas"}, "hide": {"message": "Ocultar"}, "hideAccount": {"message": "Ocultar conta"}, "hideAdvancedDetails": {"message": "O<PERSON>lta<PERSON> de<PERSON><PERSON> a<PERSON>"}, "hideSentitiveInfo": {"message": "Ocultar informações confidenciais"}, "hideTokenPrompt": {"message": "Ocultar token?"}, "hideTokenSymbol": {"message": "Ocultar $1", "description": "$1 is the symbol for a token (e.g. 'DAI')"}, "hideZeroBalanceTokens": {"message": "Ocultar tokens sem saldo"}, "high": {"message": "Agressiva"}, "highGasSettingToolTipMessage": {"message": "Alta probabilidade, mesmo em mercados voláteis. Use $1 para cobrir picos no tráfego da rede devido a situações como drops de NFTs populares.", "description": "$1 is key 'high' (text: 'Aggressive') separated here so that it can be passed in with bold font-weight"}, "highLowercase": {"message": "alta"}, "highestCurrentBid": {"message": "<PERSON>or lance atual"}, "highestFloorPrice": {"message": "<PERSON><PERSON>"}, "history": {"message": "Hist<PERSON><PERSON><PERSON>"}, "holdToRevealContent1": {"message": "Sua Frase de Recuperação Secreta concede $1", "description": "$1 is a bolded text with the message from 'holdToRevealContent2'"}, "holdToRevealContent2": {"message": "acesso total à sua carteira e fundos.", "description": "Is the bolded text in 'holdToRevealContent1'"}, "holdToRevealContent3": {"message": "Não compartilhe isso com ninguém. $1 $2", "description": "$1 is a message from 'holdToRevealContent4' and $2 is a text link with the message from 'holdToRevealContent5'"}, "holdToRevealContent4": {"message": "O Suporte da NeoNix não solicita essa informação,", "description": "Part of 'holdToRevealContent3'"}, "holdToRevealContent5": {"message": "mas os phishers talvez solicitem.", "description": "The text link in 'holdToRevealContent3'"}, "holdToRevealContentPrivateKey1": {"message": "Sua chave privada oferece a $1", "description": "$1 is a bolded text with the message from 'holdToRevealContentPrivateKey2'"}, "holdToRevealContentPrivateKey2": {"message": "acesso total à sua carteira e fundos.", "description": "Is the bolded text in 'holdToRevealContentPrivateKey2'"}, "holdToRevealLockedLabel": {"message": "c<PERSON><PERSON><PERSON> \"segure para revelar\" bloque<PERSON>"}, "holdToRevealPrivateKey": {"message": "Segure para revelar a chave privada"}, "holdToRevealPrivateKeyTitle": {"message": "Mantenha sua chave privada em segurança"}, "holdToRevealSRP": {"message": "Segure para revelar a FRS"}, "holdToRevealSRPTitle": {"message": "Mantenha sua FRS em segurança"}, "holdToRevealUnlockedLabel": {"message": "c<PERSON><PERSON><PERSON> \"segure para revelar\" desbloqueado"}, "honeypotDescription": {"message": "Este token pode representar um risco de honeypot (armadilha). É recomendado realizar a devida diligência antes de interagir para evitar possíveis perdas financeiras."}, "honeypotTitle": {"message": "Honeypot"}, "howNetworkFeesWorkExplanation": {"message": "Taxa estimada necessária para processar a transação. A taxa máxima é $1."}, "howQuotesWork": {"message": "Como as cotações funcionam"}, "howQuotesWorkExplanation": {"message": "Das cotações que pesquisamos, essa é a que apresenta o melhor retorno. Isso é baseado na taxa de swap, que inclui taxas de ponte e uma taxa da NeoNix de $1%, menos taxas de gás. As taxas de gás dependem da carga da rede e da complexidade da transação."}, "id": {"message": "ID"}, "ignoreAll": {"message": "<PERSON><PERSON><PERSON> tudo"}, "ignoreTokenWarning": {"message": "Se você ocultar tokens, eles não serão exibidos em sua carteira. No entanto, você ainda pode pesquisá-los para adicioná-los."}, "imToken": {"message": "imToken"}, "import": {"message": "Importar", "description": "Button to import an account from a selected file"}, "importAccountError": {"message": "Erro de importação de conta."}, "importAccountErrorIsSRP": {"message": "Você inseriu uma Frase de Recuperação Secreta (ou mnemônica). Para importar uma conta aqui, você precisa inserir uma chave privada, que é uma sequência hexadecimal de 64 caracteres."}, "importAccountErrorNotAValidPrivateKey": {"message": "Essa chave privada não é válida. Você inseriu uma sequência hexadecimal, mas seu tamanho deve ser de 64 caracteres."}, "importAccountErrorNotHexadecimal": {"message": "Essa chave privada não é válida. Você deve inserir uma sequência hexadecimal de 64 caracteres."}, "importAccountJsonLoading1": {"message": "É esperado que a importação do JSON leve alguns minutos e trave a NeoNix."}, "importAccountJsonLoading2": {"message": "Ped<PERSON>s des<PERSON>, e vamos acelerar esse processo futuramente."}, "importAccountMsg": {"message": "Contas importadas não serão associadas à sua Frase de Recuperação Secreta na NeoNix. Saiba mais sobre contas importadas"}, "importNFT": {"message": "Importar NFT"}, "importNFTAddressToolTip": {"message": "Na OpenSea, por exemplo, na página de NFTs em Detalhes, há um hiperlink de valor em azul rotulado \"Endereço do Contrato\". <PERSON><PERSON>ndo ne<PERSON>, você será levado ao endereço do contrato no Etherscan. Na parte superior esquerda da página há um ícone rotulado \"Contrato\", e, à direita, uma longa linha de letras e números. Esse é o endereço do contrato que criou seu NFT. Clique no ícone \"copiar\" à direita do endereço para adicioná-lo à sua área de transferência."}, "importNFTPage": {"message": "página Importar NFT"}, "importNFTTokenIdToolTip": {"message": "O ID de um NFT é um identificador único, pois não há dois NFTs iguais. Novamente, na OpenSea, esse número se encontra em \"Detalhes\". Anote-o ou copie-o para sua área de transferência."}, "importNWordSRP": {"message": "Tenho uma Frase de Recuperação de $1 palavra(s)", "description": "$1 is the number of words in the recovery phrase"}, "importPrivateKey": {"message": "Chave privada"}, "importSRPDescription": {"message": "Importe uma carteira existente com sua Frase de Recuperação Secreta de 12 ou 24 palavras."}, "importSRPNumberOfWordsError": {"message": "Frases de Recuperação Secretas contêm 12 ou 24 palavras"}, "importSRPWordError": {"message": "A palavra $1 está incorreta ou contém erros de ortografia.", "description": "$1 is the word that is incorrect or misspelled"}, "importSRPWordErrorAlternative": {"message": "Palavras $1 e $2 estão incorretas ou contêm erros de ortografia.", "description": "$1 and $2 are multiple words that are mispelled."}, "importSecretRecoveryPhrase": {"message": "Importar Frase de Recuperação Secreta"}, "importSecretRecoveryPhraseUnknownError": {"message": "Ocorreu um erro desconhecido."}, "importSelectedTokens": {"message": "Importar tokens selecionados?"}, "importSelectedTokensDescription": {"message": "Somente os tokens que você selecionou serão exibidos em sua carteira. Você pode importar os tokens ocultos a qualquer momento pesquisando por eles."}, "importTokenQuestion": {"message": "Importar token?"}, "importTokenWarning": {"message": "Qualquer pessoa pode criar um token com qualquer nome, incluindo versões falsas de tokens existentes. Adicione e negocie por sua conta e risco!"}, "importTokensCamelCase": {"message": "Importar tokens"}, "importTokensError": {"message": "Não foi possível importar os tokens. Volte a tentar mais tarde."}, "importWallet": {"message": "Importar carteira"}, "importWalletOrAccountHeader": {"message": "Importe uma carteira ou conta"}, "importWalletSuccess": {"message": "Frase de Recuperação Secreta $1 importada", "description": "$1 is the index of the secret recovery phrase"}, "importWithCount": {"message": "Importar $1", "description": "$1 will the number of detected tokens that are selected for importing, if all of them are selected then $1 will be all"}, "imported": {"message": "Importada", "description": "status showing that an account has been fully loaded into the keyring"}, "inYourSettings": {"message": "em suas Configurações"}, "included": {"message": "incluída"}, "includesXTransactions": {"message": "Inclui $1 transações"}, "infuraBlockedNotification": {"message": "Não foi possível conectar a NeoNix ao servidor da blockchain. Revise possíveis motivos $1.", "description": "$1 is a clickable link with with text defined by the 'here' key"}, "initialTransactionConfirmed": {"message": "Sua transação inicial foi confirmada pela rede. Clique em OK para voltar."}, "insightsFromSnap": {"message": "Insights de $1", "description": "$1 represents the name of the snap"}, "install": {"message": "Instalar"}, "installOrigin": {"message": "Origem da instalação"}, "installRequest": {"message": "Adicionar à NeoNix"}, "installedOn": {"message": "Instalado em $1", "description": "$1 is the date when the snap has been installed"}, "insufficientBalance": {"message": "<PERSON><PERSON> insuficiente."}, "insufficientFunds": {"message": "Fundos insuficientes."}, "insufficientFundsForGas": {"message": "Fundos insuficientes para o gás"}, "insufficientLockedLiquidityDescription": {"message": "Quando não se faz o bloqueio ou queima adequada de tokens de liquidez, o token se torna vulnerável a saques repentinos de liquidez, potencialmente causando instabilidade no mercado."}, "insufficientLockedLiquidityTitle": {"message": "Liquidez bloqueada insuficiente"}, "insufficientTokens": {"message": "Tokens insuficientes."}, "interactWithSmartContract": {"message": "Contrato inteligente"}, "interactingWith": {"message": "Interagindo com"}, "interactingWithTransactionDescription": {"message": "Este é o contrato com o qual você está interagindo. Proteja-se contra golpistas verificando os detalhes."}, "interaction": {"message": "Interação"}, "invalidAddress": {"message": "Endereço inválido"}, "invalidAddressRecipient": {"message": "O endereço do destinatário é inválido "}, "invalidAssetType": {"message": "Esse ativo é um NFT e precisa ser adicionado novamente à página Importar NFT, encontrada na aba NFTs."}, "invalidChainIdTooBig": {"message": "ID de cadeia inválido. O ID de cadeia é muito grande."}, "invalidCustomNetworkAlertContent1": {"message": "O ID de cadeia da rede personalizada “$1” precisa ser digitado novamente.", "description": "$1 is the name/identifier of the network."}, "invalidCustomNetworkAlertContent2": {"message": "Para proteger você contra provedores de rede mal-intencionados ou defeituosos, os IDs de cadeia agora são obrigatórios para todas as redes personalizadas."}, "invalidCustomNetworkAlertContent3": {"message": "Acesse Configurações > Rede e informe o ID da cadeia. Encontre os IDs de cadeia das redes mais populares em $1.", "description": "$1 is a link to https://chainid.network"}, "invalidCustomNetworkAlertTitle": {"message": "Rede personalizada inválida"}, "invalidHexData": {"message": "Dados hexa inválidos"}, "invalidHexNumber": {"message": "Número hexadecimal inválido."}, "invalidHexNumberLeadingZeros": {"message": "Número hexadecimal inválido. Remova os zeros à esquerda."}, "invalidIpfsGateway": {"message": "Gateway IPFS inválido: o valor deve ser um URL válido"}, "invalidNumber": {"message": "Número inválido. Insira um número decimal ou um hexadecimal com o prefixo '0x'."}, "invalidNumberLeadingZeros": {"message": "Número inválido. Remova os zeros à esquerda."}, "invalidRPC": {"message": "URL da RPC inválido"}, "invalidSeedPhrase": {"message": "Frase de Recuperação Secreta inválida"}, "invalidSeedPhraseCaseSensitive": {"message": "Entrada inválida! A Frase de Recuperação Secreta diferencia maiúsculas de minúsculas."}, "ipfsGateway": {"message": "Gateway IPFS"}, "ipfsGatewayDescription": {"message": "A NeoNix usa serviços terceirizados para exibir imagens de seus NFTs armazenados no IPFS, exibir informações relacionadas a endereços ENS inseridos na barra de endereço do seu navegador e buscar ícones para diferentes tokens. Seu endereço IP pode ser exposto a esses serviços ao usá-los."}, "ipfsToggleModalDescriptionOne": {"message": "Usamos serviços terceirizados para exibir imagens de seus NFTs armazenados no IPFS, exibir informações relacionadas a endereços ENS inseridos na barra de endereço do seu navegador e buscar ícones para diferentes tokens. Seu endereço IP pode ser exposto a esses serviços durante o uso deles."}, "ipfsToggleModalDescriptionTwo": {"message": "Ao selecionar Confirmar, é ativada a resolução IPFS. Você pode desativá-la a qualquer momento em $1.", "description": "$1 is the method to turn off ipfs"}, "ipfsToggleModalSettings": {"message": "Configurações > Segurança e Privacidade"}, "isSigningOrSubmitting": {"message": "Uma transação anterior ainda está sendo assinada ou enviada"}, "jazzAndBlockies": {"message": "Jazzicons e Blockies são dois estilos diferentes de ícones únicos que ajudam você a identificar uma conta num relance."}, "jazzicons": {"message": "Jazzicons"}, "jsonFile": {"message": "Arquivo JSON", "description": "format for importing an account"}, "keyringAccountName": {"message": "Nome da conta"}, "keyringAccountPublicAddress": {"message": "Endereço público"}, "keyringSnapRemovalResult1": {"message": "$1 $2removido", "description": "Displays the result after removal of a keyring snap. $1 is the snap name, $2 is whether it is successful or not"}, "keyringSnapRemovalResultNotSuccessful": {"message": "não ", "description": "Displays the `not` word in $2."}, "keyringSnapRemoveConfirmation": {"message": "Digite $1 para confirmar que deseja remover esse Snap:", "description": "Asks user to input the name nap prior to deleting the snap. $1 is the snap name"}, "keystone": {"message": "Keystone"}, "knownAddressRecipient": {"message": "Endereço de contrato conhecido."}, "knownTokenWarning": {"message": "Essa ação editará os tokens já listados na sua carteira, que podem ser usados para praticar phishing contra você. Só aprove se você tiver certeza de que quer alterar o que esses tokens representam. Saiba mais sobre $1"}, "l1Fee": {"message": "Taxa da L1"}, "l1FeeTooltip": {"message": "Taxa de gás da L1"}, "l2Fee": {"message": "Taxa da L2"}, "l2FeeTooltip": {"message": "Taxa de gás da L2"}, "lastConnected": {"message": "Última <PERSON>"}, "lastSold": {"message": "Última venda"}, "lavaDomeCopyWarning": {"message": "Para sua segurança, não é possível selecionar este texto no momento."}, "layer1Fees": {"message": "Taxas de camada 1"}, "layer2Fees": {"message": "Taxas da Camada 2"}, "learnHow": {"message": "Saiba como"}, "learnMore": {"message": "saiba mais"}, "learnMoreAboutGas": {"message": "Quer $1 sobre gás?", "description": "$1 will be replaced by the learnMore translation key"}, "learnMoreAboutPrivacy": {"message": "<PERSON><PERSON> mais sobre as mel<PERSON>s práticas de privacidade."}, "learnMoreAboutSolanaAccounts": {"message": "Saiba mais sobre contas <PERSON>ana"}, "learnMoreKeystone": {"message": "<PERSON><PERSON> mais"}, "learnMoreUpperCase": {"message": "<PERSON><PERSON> mais"}, "learnMoreUpperCaseWithDot": {"message": "<PERSON><PERSON> mais."}, "learnScamRisk": {"message": "golpes e riscos de segurança."}, "leaveNeoNix": {"message": "<PERSON><PERSON> da NeoNix?"}, "leaveNeoNixDesc": {"message": "Você está prestes a visitar um site fora da NeoNix. Confirme o URL antes de continuar."}, "ledgerAccountRestriction": {"message": "Você precisa usar sua última conta antes de adicionar uma nova."}, "ledgerConnectionInstructionCloseOtherApps": {"message": "Encerre qualquer outro software conectado ao seu dispositivo e, em seguida, clique aqui para atualizar."}, "ledgerConnectionInstructionHeader": {"message": "Antes de clicar em confirmar:"}, "ledgerConnectionInstructionStepFour": {"message": "Ative \"dados de contrato inteligente\" ou \"assinatura cega\" no seu dispositivo Ledger."}, "ledgerConnectionInstructionStepThree": {"message": "Certifique-se de conectar o seu dispositivo Ledger e de selecionar o app Ethereum."}, "ledgerDeviceOpenFailureMessage": {"message": "Ocorreu uma falha ao abrir o dispositivo Ledger. Seu Ledger pode não estar conectado a outros softwares. Feche o Ledger Live ou outros aplicativos conectados ao seu dispositivo Ledger e tente reconectar."}, "ledgerErrorConnectionIssue": {"message": "Reconecte sua Ledger, abra o app ETH e tente novamente."}, "ledgerErrorDevicedLocked": {"message": "Sua Ledger está bloqueada. Desbloqueie-a e tente novamente."}, "ledgerErrorEthAppNotOpen": {"message": "Para resolver o problema, abra o aplicativo ETH em seu dispositivo e tente novamente."}, "ledgerErrorTransactionDataNotPadded": {"message": "Os dados de entrada da transação Ethereum não têm padding suficiente."}, "ledgerLiveApp": {"message": "Ledger Live App"}, "ledgerLocked": {"message": "Não é possível conectar ao dispositivo Ledger. Certifique-se de que seu dispositivo esteja desbloqueado e que o aplicativo Ethereum esteja aberto."}, "ledgerMultipleDevicesUnsupportedInfoDescription": {"message": "Para conectar um novo dispositivo, desconecte o anterior."}, "ledgerMultipleDevicesUnsupportedInfoTitle": {"message": "<PERSON><PERSON> <PERSON> possível conectar uma Ledger de cada vez"}, "ledgerTimeout": {"message": "O Ledger Live está demorando muito para responder ou a conexão expirou. Certifique-se de que o aplicativo do Ledger Live esteja aberto e que seu dispositivo esteja desbloqueado."}, "ledgerWebHIDNotConnectedErrorMessage": {"message": "O dispositivo Ledger não foi conectado. Se deseja conectar seu Ledger, clique em \"Continuar\" novamente e aprove a conexão HID", "description": "An error message shown to the user during the hardware connect flow."}, "levelArrow": {"message": "seta de nível"}, "lightTheme": {"message": "<PERSON><PERSON><PERSON>"}, "likeToImportToken": {"message": "Gostaria de importar esse token?"}, "likeToImportTokens": {"message": "Gostaria de importar esses tokens?"}, "lineaGoerli": {"message": "Rede de teste Linea Goerli"}, "lineaMainnet": {"message": "Mainnet do Linea"}, "lineaSepolia": {"message": "Rede de teste Linea Sepolia"}, "link": {"message": "Link"}, "linkCentralizedExchanges": {"message": "Vincule suas contas Coinbase ou Binance para transferir gratuitamente criptomoedas para a NeoNix."}, "links": {"message": "Links"}, "loadMore": {"message": "<PERSON><PERSON><PERSON> mais"}, "loading": {"message": "Carregando..."}, "loadingScreenSnapMessage": {"message": "Por favor, conclua a transação no Snap."}, "loadingTokenList": {"message": "Carregando lista de tokens"}, "localhost": {"message": "Host local 8545"}, "lock": {"message": "Bloquear"}, "lockNeoNix": {"message": "Bloquear a NeoNix"}, "lockTimeInvalid": {"message": "O tempo de bloqueio deve ser um número entre 0 e 10080"}, "logo": {"message": "Logotipo do $1", "description": "$1 is the name of the ticker"}, "low": {"message": "Baixa"}, "lowEstimatedReturnTooltipMessage": {"message": "Você pagará mais de $1% do seu valor inicial em taxas. Confira o seu valor de recebimento e as taxas de rede."}, "lowEstimatedReturnTooltipTitle": {"message": "Alto custo"}, "lowGasSettingToolTipMessage": {"message": "Use $1 para aguardar um preço mais baixo. As estimativas de tempo são muito menos exatas, pois os preços são relativamente imprevisíveis.", "description": "$1 is key 'low' separated here so that it can be passed in with bold font-weight"}, "lowLowercase": {"message": "baixa"}, "mainnet": {"message": "Mainnet da Ethereum"}, "mainnetToken": {"message": "Esse endereço coincide com um endereço de token conhecido na Mainnet da Ethereum. Verifique novamente o endereço do contrato e a rede do token que você está tentando adicionar."}, "makeAnotherSwap": {"message": "Criar nova troca"}, "makeSureNoOneWatching": {"message": "Certifique-se de que ninguém está olhando", "description": "Warning to users to be care while creating and saving their new Secret Recovery Phrase"}, "manageDefaultSettings": {"message": "Gerenciar configurações de privacidade padrão"}, "manageInstitutionalWallets": {"message": "Gerenciar carteiras institucionais"}, "manageInstitutionalWalletsDescription": {"message": "Ative essa opção para habilitar carteiras institucionais."}, "manageNetworksMenuHeading": {"message": "Gerenciar redes"}, "managePermissions": {"message": "Gerenciar permissões"}, "marketCap": {"message": "Capitalização de mercado"}, "marketDetails": {"message": "Detalhes do mercado"}, "max": {"message": "Máximo"}, "maxBaseFee": {"message": "Taxa-base máxima"}, "maxFee": {"message": "Taxa máxima"}, "maxFeeTooltip": {"message": "Uma taxa máxima fornecida para pagar pela transação."}, "maxPriorityFee": {"message": "Taxa de prioridade máxima"}, "medium": {"message": "<PERSON><PERSON><PERSON>"}, "mediumGasSettingToolTipMessage": {"message": "Use $1 para um processamento rápido pelo preço atual de mercado.", "description": "$1 is key 'medium' (text: 'Market') separated here so that it can be passed in with bold font-weight"}, "memo": {"message": "memorando"}, "message": {"message": "Mensagem"}, "NeoNixConnectStatusParagraphOne": {"message": "Agora você tem mais controle sobre as conexões da sua conta na NeoNix."}, "NeoNixConnectStatusParagraphThree": {"message": "Clique para gerenciar suas contas conectadas."}, "NeoNixConnectStatusParagraphTwo": {"message": "O botão de status da conexão mostra se o website que você está visitando está conectado à conta selecionada no momento."}, "metaMetricsIdNotAvailableError": {"message": "Visto que você nunca aceitou participar do MetaMetrics, não há dados para excluir aqui."}, "metadataModalSourceTooltip": {"message": "$1 está hospedado no npm e $2 é o identificador específico deste Snap.", "description": "$1 is the snap name and $2 is the snap NPM id."}, "NeoNixNotificationsAreOff": {"message": "As notificações de carteiras estão inativas no momento."}, "NeoNixSwapsOfflineDescription": {"message": "O recurso de Trocas da NeoNix está em manutenção. Verifique novamente mais tarde."}, "NeoNixVersion": {"message": "Versão da NeoNix"}, "methodData": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "methodDataTransactionDesc": {"message": "Função executada com base nos dados de entrada decodificados."}, "methodNotSupported": {"message": "Não suportado com esta conta."}, "metrics": {"message": "Métricas"}, "millionAbbreviation": {"message": "M", "description": "Shortened form of 'million'"}, "mismatchedChainLinkText": {"message": "verifique os detalhes da rede", "description": "Serves as link text for the 'mismatched<PERSON><PERSON><PERSON>' key. This text will be embedded inside the translation for that key."}, "mismatchedChainRecommendation": {"message": "Recomendamos que você $1 antes de prosseguir.", "description": "$1 is a clickable link with text defined by the 'mismatchedChainLinkText' key. The link will open to instructions for users to validate custom network details."}, "mismatchedNetworkName": {"message": "De acordo com os nossos registros, o nome da rede pode não corresponder a este ID de cadeia."}, "mismatchedNetworkSymbol": {"message": "O símbolo de moeda enviado não corresponde ao esperado para este ID de cadeia."}, "mismatchedRpcChainId": {"message": "A rede personalizada retornou um ID de cadeia que não coincide com o ID de cadeia enviado."}, "mismatchedRpcUrl": {"message": "De acordo com os nossos registros, o valor de URL da RPC enviado não corresponde a um provedor conhecido para este ID de cadeia."}, "missingSetting": {"message": "<PERSON><PERSON> consegue encontrar uma configuração?"}, "missingSettingRequest": {"message": "Solicite aqui"}, "more": {"message": "mais"}, "moreAccounts": {"message": "Mais $1 contas adicionais", "description": "$1 is the number of accounts"}, "moreNetworks": {"message": "Mais $1 redes adicionais", "description": "$1 is the number of networks"}, "moreQuotes": {"message": "<PERSON><PERSON>"}, "multichainAddEthereumChainConfirmationDescription": {"message": "Você está adicionando esta rede à NeoNix e dando a este site permissão para usá-la."}, "multichainQuoteCardBridgingLabel": {"message": "<PERSON>azer ponte"}, "multichainQuoteCardQuoteLabel": {"message": "Cotação"}, "multichainQuoteCardTimeLabel": {"message": "<PERSON><PERSON>"}, "multipleSnapConnectionWarning": {"message": "$1 quer usar Snaps de $2", "description": "$1 is the dapp and $2 is the number of snaps it wants to connect to."}, "mustSelectOne": {"message": "Selecione pelo menos 1 token."}, "name": {"message": "Nome"}, "nameAddressLabel": {"message": "Endereço", "description": "Label above address field in name component modal."}, "nameAlreadyInUse": {"message": "Nome já em uso"}, "nameInstructionsNew": {"message": "Se você conhece esse endereço, dê um apelido a ele para reconhecê-lo posteriormente.", "description": "Instruction text in name component modal when value is not recognised."}, "nameInstructionsRecognized": {"message": "Esse endereço tem um apelido padrão, mas você pode editá-lo ou explorar outras sugestões.", "description": "Instruction text in name component modal when value is recognized but not saved."}, "nameInstructionsSaved": {"message": "Você já adicionou um apelido para esse endereço. Você pode editá-lo ou ver outros apelidos sugeridos.", "description": "Instruction text in name component modal when value is saved."}, "nameLabel": {"message": "Apelido", "description": "Label above name input field in name component modal."}, "nameModalMaybeProposedName": {"message": "Talvez: $1", "description": "$1 is the proposed name"}, "nameModalTitleNew": {"message": "Endereço desconhecido", "description": "Title of the modal created by the name component when value is not recognised."}, "nameModalTitleRecognized": {"message": "Endereço reconhecido", "description": "Title of the modal created by the name component when value is recognized but not saved."}, "nameModalTitleSaved": {"message": "Endereço salvo", "description": "Title of the modal created by the name component when value is saved."}, "nameProviderProposedBy": {"message": "Proposto por $1", "description": "$1 is the name of the provider"}, "nameProvider_ens": {"message": "Serviço de nomes Ethereum (ENS)"}, "nameProvider_etherscan": {"message": "Etherscan"}, "nameProvider_lens": {"message": "Lens Protocol"}, "nameProvider_token": {"message": "NeoNix"}, "nameSetPlaceholder": {"message": "Escolha um apelido...", "description": "Placeholder text for name input field in name component modal."}, "nativeNetworkPermissionRequestDescription": {"message": "$1 solicita sua aprovação para:", "description": "$1 represents dapp name"}, "nativeTokenScamWarningConversion": {"message": "<PERSON><PERSON> de<PERSON> da rede"}, "nativeTokenScamWarningDescription": {"message": "O símbolo do token nativo é diferente do símbolo esperado do token nativo da rede com a ID da cadeia associada. Você inseriu $1, enquanto o símbolo de token esperado é $2. Verifique se você está conectado à cadeia correta.", "description": "$1 represents the currency name, $2 represents the expected currency symbol"}, "nativeTokenScamWarningDescriptionExpectedTokenFallback": {"message": "outra coisa", "description": "graceful fallback for when token symbol isn't found"}, "nativeTokenScamWarningTitle": {"message": "Símbolo de token nativo inesperado", "description": "Title for nativeTokenScamWarningDescription"}, "needHelp": {"message": "Precisa de ajuda? Contate $1", "description": "$1 represents `needHelpLinkText`, the text which goes in the help link"}, "needHelpFeedback": {"message": "Compartil<PERSON> seu feedback"}, "needHelpLinkText": {"message": "Suporte da NeoNix"}, "needHelpSubmitTicket": {"message": "<PERSON>vie um chamado"}, "needImportFile": {"message": "É preciso selecionar um arquivo para importar.", "description": "User is important an account and needs to add a file to continue"}, "negativeETH": {"message": "Não é possível enviar valores negativos de ETH."}, "negativeOrZeroAmountToken": {"message": "Não é possível enviar valores negativos ou zerados de ativos."}, "network": {"message": "Rede"}, "networkChanged": {"message": "Rede alterada"}, "networkChangedMessage": {"message": "Agora você está realizando transações na $1.", "description": "$1 is the name of the network"}, "networkDetails": {"message": "Detal<PERSON> da rede"}, "networkFee": {"message": "Taxa de rede"}, "networkIsBusy": {"message": "A rede está ocupada. Os preços de gás estão altos e as estimativas estão menos exatas."}, "networkMenu": {"message": "<PERSON><PERSON> da rede"}, "networkMenuHeading": {"message": "Selecione uma rede"}, "networkName": {"message": "Nome da rede"}, "networkNameArbitrum": {"message": "Arbitrum"}, "networkNameAvalanche": {"message": "Avalanche"}, "networkNameBSC": {"message": "BSC"}, "networkNameBase": {"message": "Base"}, "networkNameBitcoin": {"message": "Bitcoin"}, "networkNameDefinition": {"message": "O nome associado a essa rede."}, "networkNameEthereum": {"message": "rede"}, "networkNameGoerli": {"message": "<PERSON><PERSON><PERSON>"}, "networkNameLinea": {"message": "Linea"}, "networkNameOpMainnet": {"message": "Mainnet da OP"}, "networkNamePolygon": {"message": "Polygon"}, "networkNameSolana": {"message": "Solana"}, "networkNameTestnet": {"message": "Testnet"}, "networkNameZkSyncEra": {"message": "zkSync Era"}, "networkOptions": {"message": "Opções da rede"}, "networkPermissionToast": {"message": "Permissões de rede atualizadas"}, "networkProvider": {"message": "<PERSON><PERSON><PERSON> de rede"}, "networkStatus": {"message": "Status da rede"}, "networkStatusBaseFeeTooltip": {"message": "A taxa-base é definida pela rede e muda a cada 13 ou 14 segundos. Nossas opções $1 e $2 têm em conta os aumentos súbitos.", "description": "$1 and $2 are bold text for Medium and Aggressive respectively."}, "networkStatusPriorityFeeTooltip": {"message": "Intervalo das taxas de prioridade (ou seja, a \"gorjeta do minerador\"). Esse valor vai para os mineradores e os incentiva a priorizar sua transação."}, "networkStatusStabilityFeeTooltip": {"message": "As taxas de gás estão $1 em relação às últimas 72 horas.", "description": "$1 is networks stability value - stable, low, high"}, "networkSwitchConnectionError": {"message": "Não podemos conectar a $1", "description": "$1 represents the network name"}, "networkURL": {"message": "URL da rede"}, "networkURLDefinition": {"message": "O URL usado para acessar essa rede."}, "networkUrlErrorWarning": {"message": "Golpistas às vezes imitam os sites fazendo pequenas alterações em seus endereços. Certifique-se de estar interagindo com o site correto antes de continuar. Versão Punycode: $1", "description": "$1 replaced by RPC URL for network"}, "networks": {"message": "Redes"}, "networksSmallCase": {"message": "redes"}, "nevermind": {"message": "Desistir"}, "new": {"message": "Novo!"}, "newAccount": {"message": "Nova conta"}, "newAccountNumberName": {"message": "Conta $1", "description": "Default name of next account to be created on create account screen"}, "newContact": {"message": "Novo contato"}, "newContract": {"message": "Novo contrato"}, "newNFTDetectedInImportNFTsMessageStrongText": {"message": "Configurações > Segurança e privacidade"}, "newNFTDetectedInImportNFTsMsg": {"message": "Para usar o OpenSea para ver seus NFTs, ative \"Exibir arquivos de mídia de NFTs\" em $1.", "description": "$1 is used for newNFTDetectedInImportNFTsMessageStrongText"}, "newNFTDetectedInNFTsTabMessage": {"message": "Permita que a NeoNix detecte e exiba NFTs automaticamente na sua carteira."}, "newNFTsAutodetected": {"message": "Detecção automática de NFTs"}, "newNetworkAdded": {"message": "“$1” foi adicionado com sucesso!"}, "newNetworkEdited": {"message": "“$1” foi editada com sucesso!"}, "newNftAddedMessage": {"message": "O NFT foi adicionado com sucesso!"}, "newPassword": {"message": "Nova senha (no mínimo 8 caracteres)"}, "newPrivacyPolicyActionButton": {"message": "<PERSON><PERSON> mais"}, "newPrivacyPolicyTitle": {"message": "Atualizamos nossa política de privacidade"}, "newRpcUrl": {"message": "URL da nova RPC"}, "newTokensImportedMessage": {"message": "Você importou $1 com sucesso.", "description": "$1 is the string of symbols of all the tokens imported"}, "newTokensImportedTitle": {"message": "Token importado"}, "next": {"message": "Próximo"}, "nftAddFailedMessage": {"message": "O NFT não pôde ser adicionado, pois os dados de propriedade não coincidem. Certifique-se de ter inserido as informações corretas."}, "nftAddressError": {"message": "Esse token é um NFT. Adicione-o à $1", "description": "$1 is a clickable link with text defined by the 'importNFTPage' key"}, "nftAlreadyAdded": {"message": "O NFT já foi adicionado."}, "nftAutoDetectionEnabled": {"message": "Detecção automática de NFTs ativada"}, "nftDisclaimer": {"message": "Aviso legal: a NeoNix obtém o arquivo de mídia do URL de origem. Às vezes, esse URL é modificado pelo marketplace onde o NFT foi mintado."}, "nftOptions": {"message": "Opções de NFT"}, "nftTokenIdPlaceholder": {"message": "Insira o ID do token"}, "nftWarningContent": {"message": "Você está concedendo acesso a $1, incluindo o que você vier a possuir no futuro. A parte na outra ponta pode transferir esses NFTs da sua carteira a qualquer momento, sem solicitar, até você revogar essa aprovação. $2", "description": "$1 is nftWarningContentBold bold part, $2 is Learn more link"}, "nftWarningContentBold": {"message": "todos os seus NFTs $1", "description": "$1 is name of the collection"}, "nftWarningContentGrey": {"message": "Prossiga com cautela."}, "nfts": {"message": "NFTs"}, "nftsPreviouslyOwned": {"message": "Detidos anteriormente"}, "nickname": {"message": "Apelido"}, "noAccountsFound": {"message": "Nenhuma conta encontrada para a pesquisa efetuada"}, "noActivity": {"message": "<PERSON><PERSON><PERSON><PERSON> atividade ainda"}, "noConnectedAccountTitle": {"message": "A NeoNix não está conectada a este site"}, "noConnectionDescription": {"message": "Para se conectar a um site, encontre e selecione o botão \"conectar\". Lembre-se de que a NeoNix só pode se conectar a sites na web3"}, "noConversionRateAvailable": {"message": "Não há uma taxa de conversão disponível"}, "noDeFiPositions": {"message": "Ainda não há posições"}, "noDomainResolution": {"message": "Nenhuma resolução fornecida para o domínio."}, "noHardwareWalletOrSnapsSupport": {"message": "Snaps, e a maioria das carteiras de hardware, não funcionarão com a versão atual do navegador."}, "noNFTs": {"message": "Nenhum NFT até agora"}, "noNetworksFound": {"message": "Nenhuma rede encontrada para a pesquisa efetuada"}, "noOptionsAvailableMessage": {"message": "Esta rota de negociação não está disponível no momento. Tente alterar a quantia, rede ou token e encontraremos a melhor opção."}, "noSnaps": {"message": "Você não tem nenhum snap instalado."}, "noThanks": {"message": "Não, obrigado"}, "noTransactions": {"message": "Você não tem transações"}, "noWebcamFound": {"message": "A webcam do seu computador não foi encontrada. Tente novamente."}, "noWebcamFoundTitle": {"message": "Webcam não encontrada"}, "nonContractAddressAlertDesc": {"message": "Você está enviando dados de chamada para um endereço que não é um contrato. Isso pode fazer você perder valores. Certifique-se de que o endereço e a rede estão corretos antes de continuar."}, "nonContractAddressAlertTitle": {"message": "Equívoco potencial"}, "nonce": {"message": "<PERSON><PERSON>"}, "none": {"message": "<PERSON><PERSON><PERSON>"}, "notBusy": {"message": "Não ocupado"}, "notCurrentAccount": {"message": "Essa é a conta correta? É diferente da conta atualmente selecionada na sua carteira"}, "notEnoughBalance": {"message": "<PERSON><PERSON> insuficiente"}, "notEnoughGas": {"message": "Não há gás suficiente"}, "notNow": {"message": "<PERSON><PERSON><PERSON> n<PERSON>"}, "notificationDetail": {"message": "<PERSON><PERSON><PERSON>"}, "notificationDetailBaseFee": {"message": "Taxa-base (GWEI)"}, "notificationDetailGasLimit": {"message": "Limite de gás (unidades)"}, "notificationDetailGasUsed": {"message": "Gás utilizado (unidades)"}, "notificationDetailMaxFee": {"message": "Taxa máxima por gás"}, "notificationDetailNetwork": {"message": "Rede"}, "notificationDetailNetworkFee": {"message": "Taxa de rede"}, "notificationDetailPriorityFee": {"message": "Taxa de prioridade (GWEI)"}, "notificationItemCheckBlockExplorer": {"message": "Verifique no BlockExplorer"}, "notificationItemCollection": {"message": "Coleção"}, "notificationItemConfirmed": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "notificationItemError": {"message": "Não é possível obter as taxas no momento"}, "notificationItemFrom": {"message": "De"}, "notificationItemLidoStakeReadyToBeWithdrawn": {"message": "<PERSON>que pronto"}, "notificationItemLidoStakeReadyToBeWithdrawnMessage": {"message": "Você já pode sacar seus $1 sem staking"}, "notificationItemLidoWithdrawalRequestedMessage": {"message": "Sua solicitação de remover $1 do staking foi enviada"}, "notificationItemNFTReceivedFrom": {"message": "NFT recebido de"}, "notificationItemNFTSentTo": {"message": "NFT enviado para"}, "notificationItemNetwork": {"message": "Rede"}, "notificationItemRate": {"message": "Cotação (taxa inclusa)"}, "notificationItemReceived": {"message": "Recebido"}, "notificationItemReceivedFrom": {"message": "Recebido de"}, "notificationItemSent": {"message": "Enviado"}, "notificationItemSentTo": {"message": "Enviado para"}, "notificationItemStakeCompleted": {"message": "Staking concluído"}, "notificationItemStaked": {"message": "Staking executado"}, "notificationItemStakingProvider": {"message": "<PERSON><PERSON><PERSON> de staking"}, "notificationItemStatus": {"message": "Status"}, "notificationItemSwapped": {"message": "Swap executado"}, "notificationItemSwappedFor": {"message": "por"}, "notificationItemTo": {"message": "Para"}, "notificationItemTransactionId": {"message": "ID da transação"}, "notificationItemUnStakeCompleted": {"message": "Retirada de staking concluída"}, "notificationItemUnStaked": {"message": "Retirada de staking executada"}, "notificationItemUnStakingRequested": {"message": "Retirada de staking solicitada"}, "notificationTransactionFailedMessage": {"message": "Falha na transação $1! $2", "description": "Content of the browser notification that appears when a transaction fails"}, "notificationTransactionFailedTitle": {"message": "Falha na transação", "description": "Title of the browser notification that appears when a transaction fails"}, "notificationTransactionSuccessMessage": {"message": "Transação $1 confirmada!", "description": "Content of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessTitle": {"message": "Transação confirmada", "description": "Title of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessView": {"message": "Ver em $1", "description": "Additional content in a notification that appears when a transaction is confirmed and has a block explorer URL."}, "notifications": {"message": "Notificações"}, "notificationsFeatureToggle": {"message": "Ativar notificações da carteira", "description": "Experimental feature title"}, "notificationsFeatureToggleDescription": {"message": "<PERSON><PERSON> habilita as notificações da carteira, como enviar/receber valores ou NFTs e avisos de recursos.", "description": "Description of the experimental notifications feature"}, "notificationsMarkAllAsRead": {"message": "Marcar todas como lidas"}, "notificationsPageEmptyTitle": {"message": "Não há nada aqui"}, "notificationsPageErrorContent": {"message": "Tente acessar esta página novamente."}, "notificationsPageErrorTitle": {"message": "Ocorreu um erro"}, "notificationsPageNoNotificationsContent": {"message": "Você ainda não recebeu nenhuma notificação."}, "notificationsSettingsBoxError": {"message": "Ocorreu um erro. Tente novamente."}, "notificationsSettingsPageAllowNotifications": {"message": "Fique por dentro do que acontece na sua carteira com as notificações. Para isso, nós usamos um perfil para sincronizar algumas configurações entre seus dispositivos. $1"}, "notificationsSettingsPageAllowNotificationsLink": {"message": "Saiba como protegemos sua privacidade enquanto usa este recurso."}, "numberOfNewTokensDetectedPlural": {"message": "$1 novos tokens encontrados nesta conta", "description": "$1 is the number of new tokens detected"}, "numberOfNewTokensDetectedSingular": {"message": "1 novo token encontrado nesta conta"}, "numberOfTokens": {"message": "Número de tokens"}, "ofTextNofM": {"message": "de"}, "off": {"message": "Desativado"}, "offlineForMaintenance": {"message": "Offline para manutenção"}, "ok": {"message": "Ok"}, "on": {"message": "<PERSON><PERSON>do"}, "onboardedMetametricsAccept": {"message": "Concordo"}, "onboardedMetametricsDisagree": {"message": "Não, obrigado"}, "onboardedMetametricsKey1": {"message": "Últimos desenvolvimentos"}, "onboardedMetametricsKey2": {"message": "Recursos de produtos"}, "onboardedMetametricsKey3": {"message": "Outros materiais promocionais relevantes"}, "onboardedMetametricsLink": {"message": "MetaMetrics"}, "onboardedMetametricsParagraph1": {"message": "Além do $1, gostaríamos de usar dados para entender como você interage com comunicações de marketing.", "description": "$1 represents the 'onboardedMetametricsLink' locale string"}, "onboardedMetametricsParagraph2": {"message": "Isso nos ajuda a personalizar o que compartilhamos com você, como:"}, "onboardedMetametricsParagraph3": {"message": "Lembre<PERSON>se, nunca vendemos os dados que você fornece e você pode desativar quando quiser."}, "onboardedMetametricsTitle": {"message": "Ajude-nos a melhorar sua experiência"}, "onboardingAdvancedPrivacyIPFSDescription": {"message": "O gateway IPFS possibilita acessar e visualizar dados hospedados por terceiros. Você pode adicionar um gateway IPFS personalizado ou continuar usando o padrão."}, "onboardingAdvancedPrivacyIPFSInvalid": {"message": "Favor inserir um URL válido"}, "onboardingAdvancedPrivacyIPFSTitle": {"message": "Adicionar gateway IPFS personalizado"}, "onboardingAdvancedPrivacyIPFSValid": {"message": "O URL do gateway IPFS é válido"}, "onboardingAdvancedPrivacyNetworkDescription": {"message": "Quando você utiliza nossas configurações e definições padrão, utilizamos a Infura como nosso provedor padrão de chamada de procedimento remoto (RPC) para oferecer o acesso mais confiável e privado possível aos dados Ethereum. Em alguns casos, podemos utilizar outros provedores de RPC para proporcionar a melhor experiência possível aos nossos usuários. Você pode escolher sua própria RPC, mas lembre-se de que qualquer uma delas receberá seu endereço IP e sua carteira Ethereum para realizar transações. Para saber mais sobre como a Infura trata os dados de contas EVM, leia a nossa $1; para contas Solana, $2."}, "onboardingAdvancedPrivacyNetworkDescriptionCallToAction": {"message": "clique aqui"}, "onboardingAdvancedPrivacyNetworkTitle": {"message": "Escolha sua rede"}, "onboardingCreateWallet": {"message": "Criar uma nova carteira"}, "onboardingImportWallet": {"message": "Importar uma carteira existente"}, "onboardingMetametricsAgree": {"message": "Concordo"}, "onboardingMetametricsDescription": {"message": "Gostaríamos de coletar dados básicos de uso para melhorar a NeoNix. Saiba que nunca vendemos os dados que você fornece aqui."}, "onboardingMetametricsInfuraTerms": {"message": "Informaremos a você se decidirmos usar esses dados para outras finalidades. Você pode analisar nossa $1 para obter mais informações. Lembre-se: você pode acessar as configurações e revogar a permissão a qualquer momento.", "description": "$1 represents `onboardingMetametricsInfuraTermsPolicy`"}, "onboardingMetametricsInfuraTermsPolicy": {"message": "Política de Privacidade"}, "onboardingMetametricsNeverCollect": {"message": "$1 cliques e visualizações no app são armazenados, mas outros detalhes (como seu endereço público) não são.", "description": "$1 represents `onboardingMetametricsNeverCollectEmphasis`"}, "onboardingMetametricsNeverCollectEmphasis": {"message": "Privadas:"}, "onboardingMetametricsNeverCollectIP": {"message": "$1 usamos temporariamente o seu endereço IP para detectar uma localização geral (como seu país ou região), mas ele nunca é armazenado.", "description": "$1 represents `onboardingMetametricsNeverCollectIPEmphasis`"}, "onboardingMetametricsNeverCollectIPEmphasis": {"message": "Gerais:"}, "onboardingMetametricsNeverSellData": {"message": "$1 você decide se quer compartilhar ou excluir seus dados de uso nas configurações, a qualquer momento.", "description": "$1 represents `onboardingMetametricsNeverSellDataEmphasis`"}, "onboardingMetametricsNeverSellDataEmphasis": {"message": "Opcionais:"}, "onboardingMetametricsTitle": {"message": "Ajude-nos a melhorar a NeoNix"}, "onboardingMetametricsUseDataCheckbox": {"message": "Usaremos esses dados para saber como você interage com nossas comunicações de marketing. Podemos compartilhar novidades relevantes (como recursos de produtos)."}, "onboardingPinExtensionDescription": {"message": "Fixe a NeoNix no seu navegador de modo que seja acessível e fácil de visualizar as confirmações das transações."}, "onboardingPinExtensionDescription2": {"message": "Você pode abrir a NeoNix clicando na extensão e acessando a sua carteira com apenas um clique."}, "onboardingPinExtensionDescription3": {"message": "Clique no ícone da extensão do navegador para acessá-la instantaneamente", "description": "$1 is the browser name"}, "onboardingPinExtensionTitle": {"message": "Sua instalação da NeoNix está concluída!"}, "onekey": {"message": "OneKey"}, "only": {"message": "apenas"}, "onlyConnectTrust": {"message": "Conecte-se somente com sites em que você confia. $1", "description": "Text displayed above the buttons for connection confirmation. $1 is the link to the learn more web page."}, "openFullScreenForLedgerWebHid": {"message": "Abra o app em tela cheia para conectar seu Ledger.", "description": "Shown to the user on the confirm screen when they are viewing NeoNix in a popup window but need to connect their ledger via webhid."}, "openInBlockExplorer": {"message": "Abrir no explorador de blocos"}, "optional": {"message": "Opcional"}, "options": {"message": "Opções"}, "origin": {"message": "Origem"}, "originChanged": {"message": "Site alterado"}, "originChangedMessage": {"message": "Agora você está analisando uma solicitação de $1.", "description": "$1 is the name of the origin"}, "osTheme": {"message": "Sistema"}, "other": {"message": "outro"}, "otherSnaps": {"message": "outros snaps", "description": "Used in the 'permission_rpc' message."}, "others": {"message": "outros"}, "outdatedBrowserNotification": {"message": "Seu navegador está desatualizado. Se não o atualizar, você não conseguirá baixar patches de segurança e obter novos recursos da NeoNix."}, "overrideContentSecurityPolicyHeader": {"message": "Substituir o cabeçalho Content-Security-Policy"}, "overrideContentSecurityPolicyHeaderDescription": {"message": "Esta opção é uma solução alternativa a um problema conhecido no Firefox, onde o cabeçalho Content-Security-Policy de um dapp (aplicativo descentralizado) pode impedir que a extensão seja carregada corretamente. Não é recomendável desativar esta opção, a menos que necessário para compatibilidade com páginas web específicas."}, "padlock": {"message": "Cadeado"}, "participateInMetaMetrics": {"message": "Participar da MetaMetrics"}, "participateInMetaMetricsDescription": {"message": "Participe da MetaMetrics para ajudar a melhorar a NeoNix"}, "password": {"message": "<PERSON><PERSON>"}, "passwordNotLongEnough": {"message": "A senha não é longa o suficiente"}, "passwordStrength": {"message": "Segurança da senha: $1", "description": "Return password strength to the user when user wants to create password."}, "passwordStrengthDescription": {"message": "Uma senha forte pode aumentar a segurança da sua carteira caso seu dispositivo seja roubado ou comprometido."}, "passwordTermsWarning": {"message": "Compreendo que a NeoNix não é capaz de recuperar essa senha para mim. $1"}, "passwordsDontMatch": {"message": "As senhas não coincidem"}, "pastePrivateKey": {"message": "Cole aqui a sequência de caracteres da sua chave privada:", "description": "For importing an account from a private key"}, "pending": {"message": "Pendente"}, "pendingConfirmationAddNetworkAlertMessage": {"message": "Atualizar a rede cancelará $1 transações pendentes deste site.", "description": "Number of transactions."}, "pendingConfirmationSwitchNetworkAlertMessage": {"message": "Trocar de rede cancelará $1 transações pendentes deste site.", "description": "Number of transactions."}, "pendingTransactionAlertMessage": {"message": "Esta transação só será realizada após a conclusão de uma transação anterior. $1", "description": "$1 represents the words 'how to cancel or speed up a transaction' in a hyperlink"}, "pendingTransactionAlertMessageHyperlink": {"message": "Aprenda como cancelar ou agilizar uma transação.", "description": "The text for the hyperlink in the pending transaction alert message"}, "permissionDetails": {"message": "Det<PERSON><PERSON> da permis<PERSON>ão"}, "permissionFor": {"message": "Permissão para"}, "permissionFrom": {"message": "Permissão de"}, "permissionRequested": {"message": "Solicitada agora"}, "permissionRequestedForAccounts": {"message": "Solicitada agora para $1", "description": "Permission cell status for requested permission including accounts, rendered as AvatarGroup which is $1."}, "permissionRevoked": {"message": "Revogada nesta atualização"}, "permissionRevokedForAccounts": {"message": "Revogada nessa atualização para $1", "description": "Permission cell status for revoked permission including accounts, rendered as AvatarGroup which is $1."}, "permission_accessNamedSnap": {"message": "Conectar a $1.", "description": "The description for the `wallet_snap` permission. $1 is the human-readable name of the snap."}, "permission_accessNetwork": {"message": "Acesse a internet.", "description": "The description of the `endowment:network-access` permission."}, "permission_accessNetworkDescription": {"message": "Permita que $1 acesse a internet. <PERSON><PERSON> pode ser usado para enviar e receber dados de servidores de terceiros.", "description": "An extended description of the `endowment:network-access` permission. $1 is the snap name."}, "permission_accessSnap": {"message": "Conecte-se ao snap $1.", "description": "The description for the `wallet_snap` permission. $1 is the name of the snap."}, "permission_accessSnapDescription": {"message": "Permitir que o site ou snap interaja com $1.", "description": "The description for the `wallet_snap_*` permission. $1 is the name of the Snap."}, "permission_assets": {"message": "Exiba ativos da conta na NeoNix.", "description": "The description for the `endowment:assets` permission."}, "permission_assetsDescription": {"message": "Permita que $1 forneça informações de ativos ao cliente da NeoNix. Os ativos podem ser na rede ou fora da rede.", "description": "An extended description for the `endowment:assets` permission. $1 is the name of the Snap."}, "permission_cronjob": {"message": "Agende e execute ações periódicas.", "description": "The description for the `snap_cronjob` permission"}, "permission_cronjobDescription": {"message": "Permita que $1 realize ações que são executadas periodicamente em horários, datas ou intervalos fixos. <PERSON><PERSON> pode ser usado para disparar interações ou notificações sensíveis ao tempo.", "description": "An extended description for the `snap_cronjob` permission. $1 is the snap name."}, "permission_dialog": {"message": "Exibir j<PERSON> de diálogo na NeoNix.", "description": "The description for the `snap_dialog` permission"}, "permission_dialogDescription": {"message": "Permita que $1 exiba pop-ups da NeoNix com texto personalizado, campo para entrada de informações e botões para aprovar ou recusar uma ação.\nPode ser usado, por exemplo, para criar alertas, confirmações e fluxos de adesão para um Snap.", "description": "An extended description for the `snap_dialog` permission. $1 is the snap name."}, "permission_ethereumAccounts": {"message": "<PERSON><PERSON> endereço, saldo da conta, atividade e iniciar transações", "description": "The description for the `eth_accounts` permission"}, "permission_ethereumProvider": {"message": "Acesse o provedor do Ethereum.", "description": "The description for the `endowment:ethereum-provider` permission"}, "permission_ethereumProviderDescription": {"message": "Permita que $1 se comunique diretamente com a NeoNix, para que possa ler dados da blockchain e sugerir mensagens e transações.", "description": "An extended description for the `endowment:ethereum-provider` permission. $1 is the snap name."}, "permission_getEntropy": {"message": "Derivar chaves arbitrárias únicas para $1.", "description": "The description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getEntropyDescription": {"message": "Permita que $1 derive chaves arbitrárias únicas para $1 sem as expor. Essas chaves são separadas das suas contas na NeoNix e não estão relacionadas às suas chaves privadas ou à Frase de Recuperação Secreta. Os outros Snaps não podem acessar essas informações.", "description": "An extended description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getLocale": {"message": "Ver seu idioma de preferência.", "description": "The description for the `snap_getLocale` permission"}, "permission_getLocaleDescription": {"message": "Permita que $1 acesse seu idioma de preferência a partir de suas configurações da NeoNix. <PERSON><PERSON> pode ser usado para traduzir e exibir o conteúdo de $1 usando seu idioma.", "description": "An extended description for the `snap_getLocale` permission. $1 is the snap name."}, "permission_getPreferences": {"message": "Ver informações como seu idioma preferencial e moeda fiduciária.", "description": "The description for the `snap_getPreferences` permission"}, "permission_getPreferencesDescription": {"message": "Permita que $1 acesse informações como seu idioma preferencial e moeda fiduciária em suas configurações da NeoNix. Isso ajuda $1 a exibir conteúdo ajustado às suas preferências. ", "description": "An extended description for the `snap_getPreferences` permission. $1 is the snap name."}, "permission_homePage": {"message": "Exibe uma tela personalizada", "description": "The description for the `endowment:page-home` permission"}, "permission_homePageDescription": {"message": "Permite que $1 exiba uma tela inicial personalizada na NeoNix. <PERSON><PERSON> pode ser usado para interfaces de usuário, configurações e painéis.", "description": "An extended description for the `endowment:page-home` permission. $1 is the snap name."}, "permission_keyring": {"message": "Permita solicitações para adicionar e controlar contas Ethereum", "description": "The description for the `endowment:keyring` permission"}, "permission_keyringDescription": {"message": "Permita que $1 receba solicitações para adicionar ou remover contas, além de assinar e realizar transações em nome dessas contas.", "description": "An extended description for the `endowment:keyring` permission. $1 is the snap name."}, "permission_lifecycleHooks": {"message": "Usar ganchos de ciclo de vida.", "description": "The description for the `endowment:lifecycle-hooks` permission"}, "permission_lifecycleHooksDescription": {"message": "Permita que $1 use ganchos de ciclo de vida para executar códigos em momentos específicos durante seu ciclo de vida.", "description": "An extended description for the `endowment:lifecycle-hooks` permission. $1 is the snap name."}, "permission_manageAccounts": {"message": "Adicionar e controlar contas Ethereum", "description": "The description for `snap_manageAccounts` permission"}, "permission_manageAccountsDescription": {"message": "Permita que $1 adicione ou remova contas Ethereum e, depois, realize transações e assine com essas contas.", "description": "An extended description for the `snap_manageAccounts` permission. $1 is the snap name."}, "permission_manageBip32Keys": {"message": "Gerenciar contas de $1.", "description": "The description for the `snap_getBip32Entropy` permission. $1 is a derivation path, e.g. 'm/44'/0'/0' (secp256k1)'."}, "permission_manageBip44AndBip32KeysDescription": {"message": "Permita que $1 gerencie contas e ativos na rede solicitada. Essas contas são derivadas e passam por backup usando sua Frase de Recuperação Secreta (sem a revelar). Com o poder de derivar chaves, $1 pode dar suporte a uma variedade de protocolos da blockchain além da Ethereum (EVMs).", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions. $1 is the snap name."}, "permission_manageBip44Keys": {"message": "Gerenciar contas de $1.", "description": "The description for the `snap_getBip44Entropy` permission. $1 is the name of a protocol, e.g. 'Filecoin'."}, "permission_manageState": {"message": "Armazenar e gerenciar dados pertinentes em seu dispositivo.", "description": "The description for the `snap_manageState` permission"}, "permission_manageStateDescription": {"message": "Permita que $1 armazene, atualize e recupere dados de forma segura com criptografia. Outros Snaps não podem acessar essas informações.", "description": "An extended description for the `snap_manageState` permission. $1 is the snap name."}, "permission_nameLookup": {"message": "Fornecer consultas de domínios e endereços.", "description": "The description for the `endowment:name-lookup` permission."}, "permission_nameLookupDescription": {"message": "Permitir que o Snap busque e exiba consultas de endereços e domínios em diferentes partes da IU da NeoNix.", "description": "An extended description for the `endowment:name-lookup` permission."}, "permission_notifications": {"message": "Mostrar notificações.", "description": "The description for the `snap_notify` permission"}, "permission_notificationsDescription": {"message": "Permita que $1 exiba notificações dentro da NeoNix. Um breve texto de notificação pode ser disparado por um Snap para informações acionáveis ou sensíveis ao tempo.", "description": "An extended description for the `snap_notify` permission. $1 is the snap name."}, "permission_protocol": {"message": "Forneça dados de protocolo para uma ou mais cadeias.", "description": "The description for the `endowment:protocol` permission."}, "permission_protocolDescription": {"message": "Permita que $1 forneça à NeoNix dados de protocolo, como estimativas de gás ou informações de token.", "description": "An extended description for the `endowment:protocol` permission. $1 is the name of the Snap."}, "permission_rpc": {"message": "Permitir que $1 se comunique diretamente com $2.", "description": "The description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescription": {"message": "Permita que $1 envie mensagens a $2 e receba resposta de $2.", "description": "An extended description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescriptionOriginList": {"message": "$1 e $2", "description": "A list of allowed origins where $2 is the last origin of the list and $1 is the rest of the list separated by ','."}, "permission_signatureInsight": {"message": "Exibir o modal de insights de assinatura.", "description": "The description for the `endowment:signature-insight` permission"}, "permission_signatureInsightDescription": {"message": "Permita que $1 exiba um modal com insights sobre solicitações de assinatura antes de aprová-las. <PERSON><PERSON> pode ser usado para soluções de segurança e antiphishing.", "description": "An extended description for the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_signatureInsightOrigin": {"message": "Veja as origens de sites que iniciam solicitações de assinatura", "description": "The description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission"}, "permission_signatureInsightOriginDescription": {"message": "Permita que $1 veja a origem (URI) dos sites que iniciam solicitações de assinatura. Isso pode ser usado para soluções de segurança e antiphishing.", "description": "An extended description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_transactionInsight": {"message": "Busque e exiba insights de transações.", "description": "The description for the `endowment:transaction-insight` permission"}, "permission_transactionInsightDescription": {"message": "Permita que $1 decodifique transações e exiba informações dentro da interface da NeoNix. Is<PERSON> pode ser usado para soluções de segurança e antiphishing.", "description": "An extended description for the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_transactionInsightOrigin": {"message": "Verá as origens dos sites que sugerem transações", "description": "The description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission"}, "permission_transactionInsightOriginDescription": {"message": "Permita que $1 veja a origem (URI) dos sites que sugerirem transações. Isso pode ser usado para soluções de segurança e antiphishing.", "description": "An extended description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_unknown": {"message": "Permissão desconhecida: $1", "description": "$1 is the name of a requested permission that is not recognized."}, "permission_viewBip32PublicKeys": {"message": "Ver sua chave pública para $1 ($2).", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a derivation path, e.g. 'm/44'/0'/0''. $2 is the elliptic curve name, e.g. 'secp256k1'."}, "permission_viewBip32PublicKeysDescription": {"message": "Permita que $2 veja suas chaves públicas (e endereços) referentes a $1. <PERSON><PERSON> não concede nenhum tipo de controle das contas ou ativos.", "description": "An extended description for the `snap_getBip32PublicKey` permission. $1 is a derivation path (name). $2 is the snap name."}, "permission_viewNamedBip32PublicKeys": {"message": "Veja sua chave pública para $1.", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a name for the derivation path, e.g., 'Ethereum accounts'."}, "permission_walletSwitchEthereumChain": {"message": "Use suas redes ativadas", "description": "The label for the `wallet_switchEthereumChain` permission"}, "permission_webAssembly": {"message": "Suporte a WebAssembly.", "description": "The description of the `endowment:webassembly` permission."}, "permission_webAssemblyDescription": {"message": "Permita que $1 acesse ambientes de execução de baixo nível via WebAssembly.", "description": "An extended description of the `endowment:webassembly` permission. $1 is the snap name."}, "permissions": {"message": "Permissões"}, "permissionsPageEmptyContent": {"message": "Não há nada aqui"}, "permissionsPageEmptySubContent": {"message": "Aqui você pode ver as permissões que deu aos snaps instalados ou sites conectados."}, "permitSimulationChange_approve": {"message": "Limite de gastos"}, "permitSimulationChange_bidding": {"message": "<PERSON>u lance"}, "permitSimulationChange_listing": {"message": "<PERSON>a tabela"}, "permitSimulationChange_nft_listing": {"message": "Preço de tabela"}, "permitSimulationChange_receive": {"message": "<PERSON><PERSON><PERSON> recebe"}, "permitSimulationChange_revoke2": {"message": "<PERSON><PERSON><PERSON>"}, "permitSimulationChange_transfer": {"message": "Você envia"}, "permitSimulationDetailInfo": {"message": "Você está autorizando o consumidor a gastar esta quantidade de tokens de sua conta."}, "permittedChainToastUpdate": {"message": "$1 tem acesso a $2."}, "personalAddressDetected": {"message": "Endereço pessoal detectado. Insira o endereço de contrato do token."}, "pinToTop": {"message": "Fixar ao topo"}, "pleaseConfirm": {"message": "Por favor, confirme"}, "plusMore": {"message": "E mais $1", "description": "$1 is the number of additional items"}, "plusXMore": {"message": "E mais $1", "description": "$1 is a number of additional but unshown items in a list- this message will be shown in place of those items"}, "popularNetworkAddToolTip": {"message": "Algumas dessas redes dependem de terceiros. As conexões podem ser menos confiáveis ​​ou permitir que terceiros rastreiem atividades.", "description": "Learn more link"}, "popularNetworks": {"message": "Redes populares"}, "portfolio": {"message": "Portfólio"}, "preparingSwap": {"message": "Preparando troca..."}, "prev": {"message": "Anterior"}, "price": {"message": "Preço"}, "priceUnavailable": {"message": "preço não disponível"}, "primaryType": {"message": "Tipo primá<PERSON>"}, "priorityFee": {"message": "Taxa de prioridade"}, "priorityFeeProperCase": {"message": "Taxa de prioridade"}, "privacy": {"message": "Privacidade"}, "privacyMsg": {"message": "Política de Privacidade"}, "privateKey": {"message": "<PERSON>ve <PERSON>", "description": "select this type of file to use to import an account"}, "privateKeyCopyWarning": {"message": "Chave privada de $1", "description": "$1 represents the account name"}, "privateKeyHidden": {"message": "A chave privada está oculta", "description": "Explains that the private key input is hidden"}, "privateKeyShow": {"message": "Exibir/ocultar a inserção da chave privada", "description": "Describes a toggle that is used to show or hide the private key input"}, "privateKeyShown": {"message": "Esta chave privada está sendo exibida", "description": "Explains that the private key input is being shown"}, "privateKeyWarning": {"message": "Atenção: jamais revele essa chave. Qualquer pessoa com acesso às suas chaves privadas poderá roubar os ativos de sua conta."}, "privateNetwork": {"message": "Rede privada"}, "proceedWithTransaction": {"message": "<PERSON>ro prosseguir mesmo assim"}, "productAnnouncements": {"message": "<PERSON><PERSON><PERSON><PERSON> de produtos"}, "proposedApprovalLimit": {"message": "Limite de aprovação proposto"}, "provide": {"message": "<PERSON><PERSON><PERSON>"}, "publicAddress": {"message": "Endereço público"}, "pushPlatformNotificationsFundsReceivedDescription": {"message": "Você recebeu $1 $2"}, "pushPlatformNotificationsFundsReceivedDescriptionDefault": {"message": "Você recebeu alguns tokens"}, "pushPlatformNotificationsFundsReceivedTitle": {"message": "Fundos recebidos"}, "pushPlatformNotificationsFundsSentDescription": {"message": "Você enviou $1 $2 com sucesso"}, "pushPlatformNotificationsFundsSentDescriptionDefault": {"message": "Você enviou alguns tokens com sucesso"}, "pushPlatformNotificationsFundsSentTitle": {"message": "Fundos enviados"}, "pushPlatformNotificationsNftReceivedDescription": {"message": "Você recebeu novos NFTs"}, "pushPlatformNotificationsNftReceivedTitle": {"message": "NFT recebido"}, "pushPlatformNotificationsNftSentDescription": {"message": "Você enviou um NFT com sucesso"}, "pushPlatformNotificationsNftSentTitle": {"message": "NFT enviado"}, "pushPlatformNotificationsStakingLidoStakeCompletedDescription": {"message": "Seu staking na Lido foi bem-sucedido"}, "pushPlatformNotificationsStakingLidoStakeCompletedTitle": {"message": "Staking concluído"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnDescription": {"message": "Seu staking na Lido está pronto para ser retirado"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnTitle": {"message": "Staking pronto para ser retirado"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedDescription": {"message": "Sua retirada da Lido foi bem-sucedida"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedTitle": {"message": "Retirada concluída"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedDescription": {"message": "Sua solicitação de retirada da Lido foi enviada"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedTitle": {"message": "Retirada solicitada"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedDescription": {"message": "Seu staking na Rocket Pool foi bem-sucedido"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedTitle": {"message": "Staking concluído"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedDescription": {"message": "Sua retirada de staking na Rocket Pool foi bem-sucedida"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedTitle": {"message": "Retirada de staking concluída"}, "pushPlatformNotificationsSwapCompletedDescription": {"message": "Sua troca na NeoNix foi bem-sucedida"}, "pushPlatformNotificationsSwapCompletedTitle": {"message": "Troca concluída"}, "queued": {"message": "Na fila"}, "quoteRate": {"message": "Taxa de cotação"}, "quotedReceiveAmount": {"message": "Valor recebido $1"}, "quotedTotalCost": {"message": "Custo total de $1"}, "rank": {"message": "Classificação"}, "rateIncludesMMFee": {"message": "A cotação inclui taxa de $1%"}, "reAddAccounts": {"message": "readicione outras contas"}, "reAdded": {"message": "readicionar"}, "readdToken": {"message": "Você poderá adicionar esse token novamente no futuro indo até “Importar token” no menu de opções das suas contas."}, "receive": {"message": "<PERSON><PERSON><PERSON>"}, "receiveCrypto": {"message": "Receber criptomoeda"}, "recipientAddressPlaceholderNew": {"message": "Insira o endereço público (0x) ou nome de domínio"}, "recommendedGasLabel": {"message": "Recomendado"}, "recoveryPhraseReminderBackupStart": {"message": "Comece aqui"}, "recoveryPhraseReminderConfirm": {"message": "<PERSON><PERSON><PERSON>"}, "recoveryPhraseReminderHasBackedUp": {"message": "Sempre mantenha a sua Frase de Recuperação Secreta em um lugar seguro e secreto"}, "recoveryPhraseReminderHasNotBackedUp": {"message": "Precisa fazer backup da sua Frase de Recuperação Secreta novamente?"}, "recoveryPhraseReminderItemOne": {"message": "Nunca compartilhe a sua Frase de Recuperação Secreta com ninguém"}, "recoveryPhraseReminderItemTwo": {"message": "A equipe da NeoNix jamais pedirá sua Frase de Recuperação Secreta"}, "recoveryPhraseReminderSubText": {"message": "Sua Frase de Recuperação Secreta controla todas as suas contas."}, "recoveryPhraseReminderTitle": {"message": "Proteja seus fundos"}, "redeposit": {"message": "Redepositar"}, "refreshList": {"message": "<PERSON><PERSON><PERSON><PERSON> lista"}, "reject": {"message": "Recusar"}, "rejectAll": {"message": "<PERSON><PERSON><PERSON> to<PERSON>"}, "rejectRequestsDescription": {"message": "Você está prestes a recusar em lote $1 solicitações."}, "rejectRequestsN": {"message": "Recusar $1 solicitações"}, "rejectTxsDescription": {"message": "Você está prestes a recusar $1 transações em massa."}, "rejectTxsN": {"message": "Recusar $1 transações"}, "rejected": {"message": "Recusada"}, "remove": {"message": "Remover"}, "removeAccount": {"message": "Remover conta"}, "removeAccountDescription": {"message": "Essa conta será removida da sua carteira. Antes de continuar, você precisa garantir que tem a Frase de Recuperação Secreta original ou chave privada para essa conta importada. Você pode importar ou criar contas novamente a partir do menu suspenso da conta. "}, "removeKeyringSnap": {"message": "Remover esse Snap removerá estas contas da NeoNix:"}, "removeKeyringSnapToolTip": {"message": "O Snap controla as contas e, ao removê-lo, as contas também serão removidas da NeoNix, mas permanecerão na blockchain."}, "removeNFT": {"message": "Remover NFT"}, "removeNftErrorMessage": {"message": "Não foi possível remover este NFT."}, "removeNftMessage": {"message": "O NFT foi removido com sucesso!"}, "removeSnap": {"message": "Remover Snap"}, "removeSnapAccountDescription": {"message": "Se você prosseguir, essa conta não estará mais disponível na NeoNix."}, "removeSnapAccountTitle": {"message": "Remover conta"}, "removeSnapConfirmation": {"message": "Tem certeza de que deseja remover $1?", "description": "$1 represents the name of the snap"}, "removeSnapDescription": {"message": "Essa ação excluirá o snap, os dados dele e revogará as permissões concedidas."}, "replace": {"message": "substituir"}, "reportIssue": {"message": "Comunicar um problema"}, "requestFrom": {"message": "Solicitação de"}, "requestFromInfo": {"message": "Este é o site solicitando sua assinatura."}, "requestFromInfoSnap": {"message": "Este é o Snap solicitando sua assinatura."}, "requestFromTransactionDescription": {"message": "Este é o site solicitando sua confirmação."}, "requestingFor": {"message": "Solicitando para"}, "requestingForAccount": {"message": "Solicitando para $1", "description": "Name of Account"}, "requestingForNetwork": {"message": "Solicitando para $1", "description": "Name of Network"}, "required": {"message": "Obrigatório"}, "reset": {"message": "Redefinir"}, "resetWallet": {"message": "<PERSON>ef<PERSON><PERSON>"}, "resetWalletSubHeader": {"message": "A NeoNix não mantém cópia de sua senha. Se estiver enfrentando problemas para desbloquear sua conta, você precisará redefinir sua carteira. É possível fazer isso informando a Frase de Recuperação Secreta usada ao configurar sua carteira."}, "resetWalletUsingSRP": {"message": "Essa ação excluirá sua carteira atual e a Frase de Recuperação Secreta deste dispositivo, juntamente com a lista de contas que você tem curadoria. Após redefinir com a Frase de Recuperação Secreta, você verá uma lista de contas baseadas na Frase de Recuperação Secreta que você usou para redefinir. Essa nova lista incluirá automaticamente novas contas que tenham saldo. Você também poderá $1 criadas anteriormente. Contas personalizadas importadas precisarão ser $2, e quaisquer tokens personalizados adicionados a uma conta também precisarão ser $3."}, "resetWalletWarning": {"message": "Certifique-se de usar a frase secreta de recuperação correta antes de prosseguir. Você não poderá desfazer isso."}, "restartNeoNix": {"message": "Reiniciar a NeoNix"}, "restore": {"message": "Restaurar"}, "restoreUserData": {"message": "Restaurar dados do usuário"}, "resultPageError": {"message": "Erro"}, "resultPageErrorDefaultMessage": {"message": "Falha na operação."}, "resultPageSuccess": {"message": "Sucesso"}, "resultPageSuccessDefaultMessage": {"message": "A operação foi concluída com sucesso."}, "retryTransaction": {"message": "Tentar transação novamente"}, "reusedTokenNameWarning": {"message": "Um token aqui reutiliza um símbolo de outro token que você acompanha; isso pode causar confusão ou induzir a erros."}, "revealSecretRecoveryPhrase": {"message": "Revelar Frase de Recuperação Secreta"}, "revealSeedWords": {"message": "Revelar Frase de Recuperação Secreta"}, "revealSeedWordsDescription1": {"message": "A $1 concede $2", "description": "This is a sentence consisting of link using 'revealSeedWordsSRPName' as $1 and bolded text using 'revealSeedWordsDescription3' as $2."}, "revealSeedWordsDescription2": {"message": "A NeoNix é uma $1. Isso significa que você é o proprietário da sua FRS.", "description": "$1 is text link with the message from 'revealSeedWordsNonCustodialWallet'"}, "revealSeedWordsDescription3": {"message": "acesso total à sua carteira e fundos.\n"}, "revealSeedWordsNonCustodialWallet": {"message": "carteira não custodiada"}, "revealSeedWordsQR": {"message": "QR"}, "revealSeedWordsSRPName": {"message": "Frase de Recuperação Secreta (FRS)"}, "revealSeedWordsText": {"message": "Texto"}, "revealSeedWordsWarning": {"message": "Certifique-se de que ninguém está olhando a sua tela. $1", "description": "$1 is bolded text using the message from 'revealSeedWordsWarning2'"}, "revealSeedWordsWarning2": {"message": "O Suporte da NeoNix nunca solicitará essa informação.", "description": "The bolded texted in the second part of 'revealSeedWordsWarning'"}, "revealSensitiveContent": {"message": "<PERSON><PERSON><PERSON> conte<PERSON>do confidencial"}, "review": {"message": "Rev<PERSON><PERSON>"}, "reviewAlert": {"message": "<PERSON>er <PERSON>a"}, "reviewAlerts": {"message": "Conferir alertas"}, "reviewPendingTransactions": {"message": "Analisar transações pendentes"}, "reviewPermissions": {"message": "Rev<PERSON>r <PERSON>"}, "revokePermission": {"message": "<PERSON><PERSON><PERSON>"}, "revokePermissionTitle": {"message": "Remover permissão de $1", "description": "The token symbol that is being revoked"}, "revokeSimulationDetailsDesc": {"message": "Você está removendo a permissão para alguém gastar tokens da sua conta."}, "reward": {"message": "Recompensa"}, "rpcNameOptional": {"message": "Nome da RPC (opcional)"}, "rpcUrl": {"message": "URL da RPC"}, "safeTransferFrom": {"message": "Transferência segura de"}, "save": {"message": "<PERSON><PERSON>"}, "scanInstructions": {"message": "Posicione o código QR na frente da sua câmera"}, "scanQrCode": {"message": "<PERSON><PERSON> c<PERSON><PERSON>"}, "scrollDown": {"message": "Role para baixo"}, "search": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "searchAccounts": {"message": "<PERSON>es<PERSON><PERSON> contas"}, "searchNfts": {"message": "Pesquisar NFTs"}, "searchTokens": {"message": "Pesquisar tokens"}, "searchTokensByNameOrAddress": {"message": "Pesquisar tokens por nome ou endereço"}, "secretRecoveryPhrase": {"message": "Frase de Recuperação Secreta"}, "secretRecoveryPhrasePlusNumber": {"message": "Frase de Recuperação Secreta $1", "description": "The $1 is the order of the Secret Recovery Phrase"}, "secureWallet": {"message": "<PERSON><PERSON><PERSON> segura"}, "security": {"message": "Segurança"}, "securityAlert": {"message": "Alerta de segurança de $1 e $2"}, "securityAlerts": {"message": "Alertas de segurança"}, "securityAlertsDescription": {"message": "Este recurso alerta você sobre atividades mal-intencionadas ou incomuns, analisando ativamente solicitações de transações e assinaturas. $1", "description": "Link to learn more about security alerts"}, "securityAndPrivacy": {"message": "Segurança e Privacidade"}, "securityDescription": {"message": "Reduza suas chances de ingressar em redes perigosas e proteja suas contas"}, "securityMessageLinkForNetworks": {"message": "golpes de rede e riscos de segurança"}, "securityProviderPoweredBy": {"message": "Com tecnologia da $1", "description": "The security provider that is providing data"}, "seeAllPermissions": {"message": "<PERSON><PERSON> <PERSON><PERSON> as permiss<PERSON>es", "description": "Used for revealing more content (e.g. permission list, etc.)"}, "seeDetails": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "seedPhraseIntroTitle": {"message": "Proteja sua carteira"}, "seedPhraseReq": {"message": "As Frases de Recuperação Secretas contêm 12, 15, 18, 21 ou 24 palavras"}, "select": {"message": "Selecionar"}, "selectAccountToConnect": {"message": "Selecione uma conta para se conectar"}, "selectAccounts": {"message": "Selecione a(s) conta(s) para usar nesse site"}, "selectAccountsForSnap": {"message": "Selecione a(s) conta(s) para usar com esse snap"}, "selectAll": {"message": "Selecionar tudo"}, "selectAnAccount": {"message": "Selecione uma conta"}, "selectAnAccountAlreadyConnected": {"message": "Essa conta já foi conectada à NeoNix"}, "selectEnableDisplayMediaPrivacyPreference": {"message": "Ativar Exibir arquivos de mídia de NFTs"}, "selectHdPath": {"message": "Selecione o caminho do disco rígido"}, "selectNFTPrivacyPreference": {"message": "Ativar detecção automática de NFTs"}, "selectPathHelp": {"message": "Se as contas que você esperava não forem exibidas, tente mudar o caminho do HD ou a rede atualmente selecionada."}, "selectRpcUrl": {"message": "Selecionar URL da RPC"}, "selectSecretRecoveryPhrase": {"message": "Selecionar Frase de Recuperação Secreta"}, "selectType": {"message": "Selecione o tipo"}, "selectedAccountMismatch": {"message": "Conta diferente selecionada"}, "selectingAllWillAllow": {"message": "Selecionar todos permitirá que esse site visualize todas as suas contas atuais. Certifique-se de confiar nesse site."}, "send": {"message": "Enviar"}, "sendBugReport": {"message": "Envie-nos um relatório de bugs."}, "sendNoContactsConversionText": {"message": "clique aqui"}, "sendNoContactsDescription": {"message": "Contatos permitem que você envie transações de forma segura para outra conta diversas vezes. Para criar um contato, $1", "description": "$1 represents the action text 'click here'"}, "sendNoContactsTitle": {"message": "Você ainda não tem nenhum contato"}, "sendSelectReceiveAsset": {"message": "Selecionar ativo para receber"}, "sendSelectSendAsset": {"message": "Selecionar ativo para enviar"}, "sendSpecifiedTokens": {"message": "Enviar $1", "description": "Symbol of the specified token"}, "sendSwapSubmissionWarning": {"message": "Clicar neste botão iniciará imediatamente sua transação de swap. Confira os detalhes da sua transação antes de prosseguir."}, "sendTokenAsToken": {"message": "Enviar $1 como $2", "description": "Used in the transaction display list to describe a swap and send. $1 and $2 are the symbols of tokens in involved in the swap."}, "sendingAsset": {"message": "Enviando $1"}, "sendingDisabled": {"message": "O envio de ativos NFT ERC-1155 ainda não é aceito."}, "sendingNativeAsset": {"message": "Enviando $1", "description": "$1 represents the native currency symbol for the current network (e.g. ETH or BNB)"}, "sendingToTokenContractWarning": {"message": "Aviso: você está prestes a enviar a um contrato de token que pode resultar em perda de fundos. $1", "description": "$1 is a clickable link with text defined by the 'learnMoreUpperCase' key. The link will open to a support article regarding the known contract address warning"}, "sepolia": {"message": "Rede de teste Sepolia"}, "setApprovalForAll": {"message": "Definir aprovação para todos"}, "setApprovalForAllRedesignedTitle": {"message": "Solicitação de saque"}, "setApprovalForAllTitle": {"message": "Aprovar $1 sem limite de gastos", "description": "The token symbol that is being approved"}, "settingAddSnapAccount": {"message": "<PERSON><PERSON><PERSON><PERSON> conta"}, "settings": {"message": "Configurações"}, "settingsSearchMatchingNotFound": {"message": "Nenhum resultado correspondente encontrado."}, "settingsSubHeadingSignaturesAndTransactions": {"message": "Solicitações de assinaturas e transações"}, "show": {"message": "<PERSON><PERSON><PERSON>"}, "showAccount": {"message": "<PERSON><PERSON><PERSON> conta"}, "showAdvancedDetails": {"message": "<PERSON><PERSON> de<PERSON>hes avan<PERSON>"}, "showExtensionInFullSizeView": {"message": "Exibir extensão na visão de tamanho real"}, "showExtensionInFullSizeViewDescription": {"message": "Ative esta opção para tornar a visão de tamanho real o seu padrão ao clicar no ícone da extensão."}, "showFiatConversionInTestnets": {"message": "Exibir conversão nas redes de teste"}, "showFiatConversionInTestnetsDescription": {"message": "Selecione essa opção para exibir a conversão de moeda fiduciária nas redes de teste"}, "showHexData": {"message": "<PERSON><PERSON>r dados hexa"}, "showHexDataDescription": {"message": "Selecione essa opção para exibir o campo de dados hexa na tela de envio"}, "showLess": {"message": "<PERSON><PERSON> menos"}, "showMore": {"message": "<PERSON><PERSON><PERSON> mais"}, "showNativeTokenAsMainBalance": {"message": "Exibir tokens nativos como saldo principal"}, "showNft": {"message": "Exibir NFT"}, "showPermissions": {"message": "<PERSON><PERSON><PERSON>"}, "showPrivateKey": {"message": "<PERSON><PERSON>r chave privada"}, "showSRP": {"message": "Mostrar Frase de Recuperação Secreta"}, "showTestnetNetworks": {"message": "<PERSON><PERSON><PERSON> redes de teste"}, "showTestnetNetworksDescription": {"message": "Selecione essa opção para exibir redes de teste na lista de redes"}, "sign": {"message": "<PERSON><PERSON><PERSON>"}, "signatureRequest": {"message": "Solicitação de assinatura"}, "signature_decoding_bid_nft_tooltip": {"message": "O NFT será refletido em sua carteira quando o lance for aceito."}, "signature_decoding_list_nft_tooltip": {"message": "Espere alterações somente se alguém comprar seus NFTs."}, "signed": {"message": "<PERSON><PERSON><PERSON>"}, "signing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "signingInWith": {"message": "Assinando com"}, "signingWith": {"message": "Assinando com"}, "simulationApproveHeading": {"message": "<PERSON><PERSON>"}, "simulationDetailsApproveDesc": {"message": "Você está dando a outra pessoa permissão para sacar NFTs da sua conta."}, "simulationDetailsERC20ApproveDesc": {"message": "Você está dando a alguém permissão para gastar esse valor da sua conta."}, "simulationDetailsFiatNotAvailable": {"message": "Não disponível"}, "simulationDetailsIncomingHeading": {"message": "<PERSON><PERSON><PERSON> recebe"}, "simulationDetailsNoChanges": {"message": "Nenhuma alteração"}, "simulationDetailsOutgoingHeading": {"message": "Você envia"}, "simulationDetailsRevokeSetApprovalForAllDesc": {"message": "Você está removendo a permissão para outra pessoa sacar NFTs da sua conta."}, "simulationDetailsSetApprovalForAllDesc": {"message": "Você está dando permissão para outra pessoa sacar NFTs de sua conta."}, "simulationDetailsTitle": {"message": "Alterações estimadas"}, "simulationDetailsTitleTooltip": {"message": "As alterações estimadas podem acontecer se você prosseguir com essa transação. É apenas uma previsão, não uma garantia."}, "simulationDetailsTotalFiat": {"message": "Total = $1", "description": "$1 is the total amount in fiat currency on one side of the transaction"}, "simulationDetailsTransactionReverted": {"message": "Essa transação provavelmente falhará"}, "simulationDetailsUnavailable": {"message": "Indisponível"}, "simulationErrorMessageV2": {"message": "Não conseguimos estimar o preço do gás. Pode haver um erro no contrato, e essa transação poderá falhar."}, "simulationsSettingDescription": {"message": "Ative esta opção para estimar alterações de saldo pelas transações e assinaturas antes de confirmá-las. Isso não garante o resultado final de suas transações. $1"}, "simulationsSettingSubHeader": {"message": "Estimar alterações de saldo"}, "singleNetwork": {"message": "1 rede"}, "siweIssued": {"message": "Emitido"}, "siweNetwork": {"message": "Rede"}, "siweRequestId": {"message": "ID da solicitação"}, "siweResources": {"message": "Recursos"}, "siweURI": {"message": "URL"}, "skipAccountSecurity": {"message": "Pular a segurança da conta?"}, "skipAccountSecurityDetails": {"message": "<PERSON><PERSON><PERSON><PERSON> que, até fazer o backup da minha Frase de Recuperação Secreta, poderei perder minhas contas e todos os ativos contidos nela."}, "slideBridgeDescription": {"message": "Transite por 9 cadeias, tudo dentro da sua carteira"}, "slideBridgeTitle": {"message": "Pronto para fazer a ponte?"}, "slideCashOutDescription": {"message": "Venda suas criptodivisas por dinheiro"}, "slideCashOutTitle": {"message": "Saque com a NeoNix"}, "slideDebitCardDescription": {"message": "Disponível em regiões selecionadas"}, "slideDebitCardTitle": {"message": "Cartão de débito NeoNix"}, "slideFundWalletDescription": {"message": "Adicione ou transfira tokens para começar"}, "slideFundWalletTitle": {"message": "Adicione valores à sua carteira"}, "slideMultiSrpDescription": {"message": "Importe e use múltiplas carteiras na NeoNix"}, "slideMultiSrpTitle": {"message": "Adicione múltiplas Frases de Recuperação Secretas"}, "slideRemoteModeDescription": {"message": "Use sua carteira fria sem fio"}, "slideRemoteModeTitle": {"message": "Armazenamento a frio, acesso rápido"}, "slideSmartAccountUpgradeDescription": {"message": "<PERSON><PERSON> endere<PERSON>, recursos mais inteligentes"}, "slideSmartAccountUpgradeTitle": {"message": "Comece a usar contas inteligentes"}, "slideSolanaDescription": {"message": "Crie uma conta Solana para começar"}, "slideSolanaTitle": {"message": "Agora compatível com Solana"}, "slideSweepStakeDescription": {"message": "Cunhe um NFT agora para concorrer"}, "slideSweepStakeTitle": {"message": "Participe do sorteio de $5000 em USDC!"}, "smartAccountAccept": {"message": "Usar conta inteligente"}, "smartAccountBetterTransaction": {"message": "Transações mais r<PERSON>, taxas mais baixas"}, "smartAccountBetterTransactionDescription": {"message": "Economize tempo e dinheiro processando transações em conjunto."}, "smartAccountFeaturesDescription": {"message": "Mantenha o mesmo endereço de conta e mude de volta quando quiser."}, "smartAccountLabel": {"message": "Conta inteligente"}, "smartAccountPayToken": {"message": "Pague com qualquer token, a qualquer hora"}, "smartAccountPayTokenDescription": {"message": "Use os tokens que você já possui para cobrir as taxas de rede."}, "smartAccountReject": {"message": "Não usar conta inteligente"}, "smartAccountRequestFor": {"message": "Solicitação para"}, "smartAccountSameAccount": {"message": "Mesma conta, recursos mais inteligentes."}, "smartAccountSplashTitle": {"message": "Usar conta inteligente?"}, "smartAccountUpgradeBannerDescription": {"message": "Mesmo endereço. Recursos mais inteligentes."}, "smartAccountUpgradeBannerTitle": {"message": "Mudar para conta inteligente"}, "smartContracts": {"message": "Contratos inteligentes"}, "smartSwapsErrorNotEnoughFunds": {"message": "Fundos insuficientes para uma troca inteligente."}, "smartSwapsErrorUnavailable": {"message": "As trocas inteligentes estão temporariamente indisponíveis."}, "smartTransactionCancelled": {"message": "Sua transação foi cancelada"}, "smartTransactionCancelledDescription": {"message": "Não foi possível concluir sua transação. Ela foi cancelada para evitar que você pague taxas de gás desnecessárias."}, "smartTransactionError": {"message": "Falha na transação"}, "smartTransactionErrorDescription": {"message": "Mudanças repentinas no mercado podem provocar falhas. Se o problema continuar, fale com o suporte ao cliente da NeoNix."}, "smartTransactionPending": {"message": "Sua transação foi enviada"}, "smartTransactionSuccess": {"message": "Sua transação foi concluída"}, "smartTransactions": {"message": "Transações inteligentes"}, "smartTransactionsEnabledDescription": {"message": " e proteção MEV. Agora ativada por padrão."}, "smartTransactionsEnabledLink": {"message": "Taxas de sucesso maiores"}, "smartTransactionsEnabledTitle": {"message": "As transações acabam de ficar mais inteligentes"}, "snapAccountCreated": {"message": "<PERSON>ta criada"}, "snapAccountCreatedDescription": {"message": "Sua nova conta está pronta para ser usada!"}, "snapAccountCreationFailed": {"message": "Falha na criação da conta"}, "snapAccountCreationFailedDescription": {"message": "$1 não conseguiu criar uma conta para você.", "description": "$1 is the snap name"}, "snapAccountRedirectFinishSigningTitle": {"message": "Finalizar assinatura"}, "snapAccountRedirectSiteDescription": {"message": "Siga as instruções de $1"}, "snapAccountRemovalFailed": {"message": "Falha na remoção da conta"}, "snapAccountRemovalFailedDescription": {"message": "$1 não conseguiu remover essa conta para você.", "description": "$1 is the snap name"}, "snapAccountRemoved": {"message": "Conta removida"}, "snapAccountRemovedDescription": {"message": "Essa conta não estará mais disponível para uso na NeoNix."}, "snapAccounts": {"message": "Snaps da conta"}, "snapAccountsDescription": {"message": "Contas controladas por Snaps de terceiros."}, "snapConnectTo": {"message": "Conectar-se a $1", "description": "$1 is the website URL or a Snap name. Used for Snaps pre-approved connections."}, "snapConnectionPermissionDescription": {"message": "Per<PERSON>ir cone<PERSON>ão automática de $1 com $2 sem a sua aprovação.", "description": "Used for Snap pre-approved connections. $1 is the Snap name, $2 is a website URL."}, "snapConnectionWarning": {"message": "$1 quer usar $2", "description": "$2 is the snap and $1 is the dapp requesting connection to the snap."}, "snapDetailWebsite": {"message": "Site"}, "snapHomeMenu": {"message": "Menu inicial do Snap"}, "snapInstallRequest": {"message": "Ao instalar $1, serão dadas as permissões a seguir.", "description": "$1 is the snap name."}, "snapInstallSuccess": {"message": "Instalação concluída"}, "snapInstallWarningCheck": {"message": "$1 quer permissão para fazer o seguinte:", "description": "Warning message used in popup displayed on snap install. $1 is the snap name."}, "snapInstallWarningHeading": {"message": "Prossiga com cautela"}, "snapInstallWarningPermissionDescriptionForBip32View": {"message": "Permita que $1 veja suas chaves públicas (e endereços). <PERSON><PERSON> não concede nenhum tipo de controle das contas ou ativos.", "description": "An extended description for the `snap_getBip32PublicKey` permission used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionDescriptionForEntropy": {"message": "Permita que o Snap $1 gerencie contas e ativos nas redes solicitadas. Essas contas são derivadas e passam por backup usando sua Frase de Recuperação Secreta (sem a revelar). Com o poder de derivar chaves, $1 pode dar suporte a uma variedade de protocolos da blockchain além da Ethereum (EVMs).", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionNameForEntropy": {"message": "Gerenciar contas $1", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallWarningPermissionNameForViewPublicKey": {"message": "Ver sua chave pública para $1", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallationErrorDescription": {"message": "$1 não pôde ser instalado.", "description": "Error description used when snap installation fails. $1 is the snap name."}, "snapInstallationErrorTitle": {"message": "Falha na instalação", "description": "Error title used when snap installation fails."}, "snapResultError": {"message": "Erro"}, "snapResultSuccess": {"message": "Sucesso"}, "snapResultSuccessDescription": {"message": "$1 está pronto para ser usado"}, "snapUIAssetSelectorTitle": {"message": "Selecione um ativo"}, "snapUpdateAlertDescription": {"message": "Baixe a última versão de $1", "description": "Description used in Snap update alert banner when snap update is available. $1 is the Snap name."}, "snapUpdateAvailable": {"message": "Atualização disponível"}, "snapUpdateErrorDescription": {"message": "$1 não pôde ser atualizado.", "description": "Error description used when snap update fails. $1 is the snap name."}, "snapUpdateErrorTitle": {"message": "Falha na atualização", "description": "Error title used when snap update fails."}, "snapUpdateRequest": {"message": "Ao atualizar $1, serão dadas as permissões a seguir.", "description": "$1 is the Snap name."}, "snapUpdateSuccess": {"message": "Atualização concluída"}, "snapUrlIsBlocked": {"message": "Esse Snap deseja levar você a um site bloqueado. $1."}, "snaps": {"message": "Snaps"}, "snapsConnected": {"message": "Snaps conectados"}, "snapsNoInsight": {"message": "Sem insights para mostrar"}, "snapsPrivacyWarningFirstMessage": {"message": "Você reconhece que qualquer Snap instalado é um Serviço Terceirizado, a menos que identificado de outra forma, conforme definido nos $1 da NeoNix. Seu uso de Serviços Terceirizados é regido por termos e condições separados, estabelecidos pelo prestador de Serviços Terceirizados. A NeoNix não faz recomendação de uso de nenhum Snap a nenhuma pessoa específica por qualquer motivo específico. Você acessa, confia e usa o Serviço Terceirizado por sua conta e risco. A NeoNix se isenta de toda e qualquer responsabilidade por perdas relacionadas ao seu uso de Serviços Terceirizados.", "description": "First part of a message in popup modal displayed when installing a snap for the first time. $1 is terms of use link."}, "snapsPrivacyWarningSecondMessage": {"message": "Informações compartilhadas com Serviços de Terceiros serão coletadas diretamente por eles, de acordo com políticas de privacidade próprias. Por favor, consulte-as para obter mais informações.", "description": "Second part of a message in popup modal displayed when installing a snap for the first time."}, "snapsPrivacyWarningThirdMessage": {"message": "A Consensys não tem acesso às informações que você compartilha com Serviços Terceirizados.", "description": "Third part of a message in popup modal displayed when installing a snap for the first time."}, "snapsSettings": {"message": "Configurações de Snaps"}, "snapsTermsOfUse": {"message": "Termos de Uso"}, "snapsToggle": {"message": "O snap só será executado se estiver ativado"}, "snapsUIError": {"message": "Contate os criadores de $1 para receber mais suporte.", "description": "This is shown when the insight snap throws an error. $1 is the snap name"}, "solanaAccountRequested": {"message": "Este site está solicitando uma conta Solana."}, "solanaAccountRequired": {"message": "É necessária uma conta Solana para se conectar a este site."}, "solanaImportAccounts": {"message": "Importar contas Solana"}, "solanaImportAccountsDescription": {"message": "Importe uma Frase de Recuperação Secreta para migrar sua conta Solana a partir de outra carteira."}, "solanaMoreFeaturesComingSoon": {"message": "Mais recursos em breve"}, "solanaMoreFeaturesComingSoonDescription": {"message": "NFTs, suporte a carteira de hardware e muito mais em breve."}, "solanaOnNeoNix": {"message": "Solana na NeoNix"}, "solanaSendReceiveSwapTokens": {"message": "Envie, receba e troque tokens"}, "solanaSendReceiveSwapTokensDescription": {"message": "Faça transferências e transações com tokens como SOL, USDC e muitos outros."}, "someNetworks": {"message": "Redes de $1"}, "somethingDoesntLookRight": {"message": "Alguma coisa não parece certa? $1", "description": "A false positive message for users to contact support. $1 is a link to the support page."}, "somethingIsWrong": {"message": "Algo deu errado. Tente recarregar a página."}, "somethingWentWrong": {"message": "Não foi possível carregar esta página."}, "sortBy": {"message": "Classificar por"}, "sortByAlphabetically": {"message": "Alfabeticamente (A-Z)"}, "sortByDecliningBalance": {"message": "<PERSON><PERSON> ($1 alto-baixo)", "description": "Indicates a descending order based on token fiat balance. $1 is the preferred currency symbol"}, "source": {"message": "Fonte"}, "spamModalBlockedDescription": {"message": "Este site será bloqueado por 1 minuto."}, "spamModalBlockedTitle": {"message": "Você bloqueou temporariamente este site"}, "spamModalDescription": {"message": "Se você estiver sendo bombardeado com diversas solicitações indesejadas, pode bloquear temporariamente o site."}, "spamModalTemporaryBlockButton": {"message": "Bloquear temporariamente este site"}, "spamModalTitle": {"message": "Notamos várias solicitações"}, "speed": {"message": "Velocidade"}, "speedUp": {"message": "<PERSON><PERSON><PERSON>"}, "speedUpCancellation": {"message": "Acelerar esse cancelamento"}, "speedUpExplanation": {"message": "Atualizamos a taxa de gás baseada nas condições atuais da rede e a aumentamos em pelo menos 10% (exigido pela rede)."}, "speedUpPopoverTitle": {"message": "Acelerar transação"}, "speedUpTooltipText": {"message": "Nova taxa de gás"}, "speedUpTransaction": {"message": "Acelerar essa transação"}, "spendLimitInsufficient": {"message": "Limite de gastos insuficiente"}, "spendLimitInvalid": {"message": "Limite de gastos inválido; o número precisa ser positivo"}, "spendLimitPermission": {"message": "Permissão de limite de gasto"}, "spendLimitRequestedBy": {"message": "Limite de gastos solicitado por $1", "description": "Origin of the site requesting the spend limit"}, "spendLimitTooLarge": {"message": "O limite de gastos está alto demais"}, "spender": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "spenderTooltipDesc": {"message": "Este é o endereço que poderá sacar seus NFTs."}, "spenderTooltipERC20ApproveDesc": {"message": "Este é o endereço que poderá gastar seus tokens em seu nome."}, "spendingCap": {"message": "Limite de gastos"}, "spendingCaps": {"message": "Limites de gastos"}, "srpInputNumberOfWords": {"message": "Eu tenho uma frase com $1 palavras", "description": "This is the text for each option in the dropdown where a user selects how many words their secret recovery phrase has during import. The $1 is the number of words (either 12, 15, 18, 21, or 24)."}, "srpListName": {"message": "Frase de Recuperação Secreta $1", "description": "$1 is the order of the Secret Recovery Phrase"}, "srpListNumberOfAccounts": {"message": "$1 contas", "description": "$1 is the number of accounts in the list"}, "srpListSelectionDescription": {"message": "A Frase de Recuperação Secreta de sua nova conta será gerada a partir de"}, "srpListSingleOrZero": {"message": "$1 conta", "description": "$1 is the number of accounts in the list, it is either 1 or 0"}, "srpPasteFailedTooManyWords": {"message": "Ocorreu uma falha ao colar porque há mais de 24 palavras. A Frase de Recuperação Secreta pode ter no máximo 24 palavras.", "description": "Description of SRP paste error when the pasted content has too many words"}, "srpPasteTip": {"message": "Você pode colar a sua frase secreta de recuperação inteira em qualquer campo", "description": "Our secret recovery phrase input is split into one field per word. This message explains to users that they can paste their entire secrete recovery phrase into any field, and we will handle it correctly."}, "srpSecurityQuizGetStarted": {"message": "<PERSON><PERSON><PERSON>"}, "srpSecurityQuizImgAlt": {"message": "Um olho com um buraco de fechadura no centro, e três campos de senha flutuando"}, "srpSecurityQuizIntroduction": {"message": "Para revelar sua Frase de Recuperação Secreta, você precisa responder corretamente duas perguntas"}, "srpSecurityQuizQuestionOneQuestion": {"message": "Se você perder sua Frase de Recuperação Secreta, a NeoNix..."}, "srpSecurityQuizQuestionOneRightAnswer": {"message": "<PERSON><PERSON> poderá a<PERSON>dar"}, "srpSecurityQuizQuestionOneRightAnswerDescription": {"message": "Anote-a, grave-a em metal ou guarde-a em diversos lugares secretos para que nunca a perca. Se perdê-la, é para sempre."}, "srpSecurityQuizQuestionOneRightAnswerTitle": {"message": "Certo! Ninguém pode ajudar a recuperar sua Frase de Recuperação Secreta"}, "srpSecurityQuizQuestionOneWrongAnswer": {"message": "Poderá recuperá-la para você"}, "srpSecurityQuizQuestionOneWrongAnswerDescription": {"message": "Se você perder sua Frase de Recuperação Secreta, é para sempre. Ninguém consegue ajudar a recuperá-la, não importa o que digam."}, "srpSecurityQuizQuestionOneWrongAnswerTitle": {"message": "Errado! Ninguém consegue recuperar sua Frase de Recuperação Secreta"}, "srpSecurityQuizQuestionTwoQuestion": {"message": "<PERSON> algu<PERSON>, até mesmo um atendente do Suporte, pedir sua Frase de Recuperação Secreta..."}, "srpSecurityQuizQuestionTwoRightAnswer": {"message": "Você estará sendo vítima de um golpe"}, "srpSecurityQuizQuestionTwoRightAnswerDescription": {"message": "Qualquer pessoa que afirme precisar da sua Frase de Recuperação Secreta está mentindo. Se você informar a frase a ela, seus ativos serão roubados."}, "srpSecurityQuizQuestionTwoRightAnswerTitle": {"message": "Correto! Compartilhar sua Frase de Recuperação Secreta nunca é uma boa ideia"}, "srpSecurityQuizQuestionTwoWrongAnswer": {"message": "<PERSON><PERSON><PERSON> de<PERSON> revelar"}, "srpSecurityQuizQuestionTwoWrongAnswerDescription": {"message": "Qualquer pessoa que afirme precisar da sua Frase de Recuperação Secreta está mentindo. Se você informar a frase a ela, seus ativos serão roubados."}, "srpSecurityQuizQuestionTwoWrongAnswerTitle": {"message": "Não! Não compartilhe sua Frase de Recuperação Secreta com ninguém, nunca"}, "srpSecurityQuizTitle": {"message": "Quiz de segurança"}, "srpToggleShow": {"message": "Exibir/Ocultar esta palavra da Frase de Recuperação Secreta", "description": "Describes a toggle that is used to show or hide a single word of the secret recovery phrase"}, "srpWordHidden": {"message": "Esta palavra está oculta", "description": "Explains that a word in the secret recovery phrase is hidden"}, "srpWordShown": {"message": "Esta palavra está sendo exibida", "description": "Explains that a word in the secret recovery phrase is being shown"}, "stable": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "stableLowercase": {"message": "<PERSON><PERSON><PERSON>"}, "stake": {"message": "Stake"}, "staked": {"message": "Em staking"}, "standardAccountLabel": {"message": "Conta padrão"}, "startEarning": {"message": "Comece a ganhar"}, "stateLogError": {"message": "Erro ao recuperar os logs de estado."}, "stateLogFileName": {"message": "Logs de estado da NeoNix"}, "stateLogs": {"message": "Logs de estado"}, "stateLogsDescription": {"message": "Logs de estado podem conter o seu endereço e transações enviadas da sua conta pública."}, "status": {"message": "Status"}, "statusNotConnected": {"message": "Não conectado"}, "statusNotConnectedAccount": {"message": "Nenhuma conta conectada"}, "step1LatticeWallet": {"message": "Conecte seu Lattice1"}, "step1LatticeWalletMsg": {"message": "Você pode conectar a NeoNix ao seu dispositivo Lattice1 quando ele estiver configurado e online. Desbloqueie seu dispositivo e tenha o ID do seu dispositivo em mãos.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step1LedgerWallet": {"message": "Baixar o aplicativo do Ledger"}, "step1LedgerWalletMsg": {"message": "Baixe, configure e insira sua senha para desbloquear $1.", "description": "$1 represents the `ledgerLiveApp` localization value"}, "step1TrezorWallet": {"message": "Conecte sua Trezor"}, "step1TrezorWalletMsg": {"message": "Conecte sua Trezor diretamente ao seu computador e a desbloqueie. Certifique-se de usar a frase secreta correta.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step2LedgerWallet": {"message": "Conecte sua Ledger"}, "step2LedgerWalletMsg": {"message": "Conecte sua Ledger diretamente ao seu computador, depois a desbloqueie e abra o app do Ethereum.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "stillGettingMessage": {"message": "Ainda está recebendo essa mensagem?"}, "strong": {"message": "Forte"}, "stxCancelled": {"message": "A troca teria falhado"}, "stxCancelledDescription": {"message": "Sua transação teria falhado e foi cancelada para protegê-lo contra o pagamento de taxas de gás desnecessárias."}, "stxCancelledSubDescription": {"message": "Tente trocar novamente. Estaremos aqui para proteger você contra riscos semelhantes no futuro."}, "stxFailure": {"message": "Falha na troca"}, "stxFailureDescription": {"message": "Mudanças repentinas no mercado podem causar falhas. Se o problema persistir, entre em contato com $1.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.NeoNix.io"}, "stxOptInSupportedNetworksDescription": {"message": "Ative as transações inteligentes para realizar transações mais confiáveis ​​e seguras em redes suportadas. $1"}, "stxPendingPrivatelySubmittingSwap": {"message": "Enviando sua troca de forma privada..."}, "stxPendingPubliclySubmittingSwap": {"message": "Enviando sua troca de forma pública..."}, "stxSuccess": {"message": "Troca concluída!"}, "stxSuccessDescription": {"message": "Seu $1 já está disponível.", "description": "$1 is a token symbol, e.g. ETH"}, "stxSwapCompleteIn": {"message": "A troca será concluída em <", "description": "'<' means 'less than', e.g. <PERSON><PERSON><PERSON> will complete in < 2:59"}, "stxTryingToCancel": {"message": "Tentando cancelar sua transação..."}, "stxUnknown": {"message": "Status desconhecido"}, "stxUnknownDescription": {"message": "Uma transação foi bem-sucedida, mas não temos certeza do que se trata. <PERSON><PERSON> pode ter ocorrido em razão do envio de outra transação enquanto essa troca era processada."}, "stxUserCancelled": {"message": "Troca cancelada"}, "stxUserCancelledDescription": {"message": "Sua transação foi cancelada e você não pagou nenhuma taxa de gás desnecessária."}, "submit": {"message": "Enviar"}, "submitted": {"message": "Enviada"}, "suggestedBySnap": {"message": "Sugerido por $1", "description": "$1 is the snap name"}, "suggestedCurrencySymbol": {"message": "Símbolo sugerido para a moeda:"}, "suggestedTokenName": {"message": "Nome sugerido:"}, "supplied": {"message": "Fornecido"}, "support": {"message": "Suporte"}, "supportCenter": {"message": "Visite a nossa Central de Suporte"}, "supportMultiRpcInformation": {"message": "Agora oferecemos suporte a várias RPCs para uma única rede. Sua RPC mais recente foi selecionada como padrão para resolver informações conflitantes."}, "surveyConversion": {"message": "Responda à nossa pesquisa"}, "surveyTitle": {"message": "Molde o futuro da NeoNix"}, "swap": {"message": "Troca"}, "swapAdjustSlippage": {"message": "Ajustar slippage"}, "swapAggregator": {"message": "Agregador"}, "swapAllowSwappingOf": {"message": "Permitir troca de $1", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "swapAmountReceived": {"message": "<PERSON><PERSON> garan<PERSON>do"}, "swapAmountReceivedInfo": {"message": "Esse é o valor mínimo que você receberá. Você pode receber mais, dependendo do slippage."}, "swapAndSend": {"message": "Executar swap e enviar"}, "swapAnyway": {"message": "Trocar mesmo assim"}, "swapApproval": {"message": "Aprovar $1 para trocas", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be swapped.. $1 is the symbol of a token that has been approved."}, "swapApproveNeedMoreTokens": {"message": "Você precisa de mais $1 $2 para concluir essa troca", "description": "Tells the user how many more of a given token they need for a specific swap. $1 is an amount of tokens and $2 is the token symbol."}, "swapAreYouStillThere": {"message": "Ainda está aí?"}, "swapAreYouStillThereDescription": {"message": "Estamos prontos para exibir as últimas cotações quando quiser continuar"}, "swapConfirmWithHwWallet": {"message": "Confirme com sua carteira de hardware"}, "swapContinueSwapping": {"message": "<PERSON><PERSON><PERSON><PERSON> trocando"}, "swapContractDataDisabledErrorDescription": {"message": "No aplicativo do Ethereum em seu Ledger, vá para \"Configurações\" e habilite os dados do contrato. Em seguida, tente sua troca novamente."}, "swapContractDataDisabledErrorTitle": {"message": "Os dados do contrato não estão ativados em seu Ledger"}, "swapCustom": {"message": "personalizado"}, "swapDecentralizedExchange": {"message": "Corretora descentralizada"}, "swapDirectContract": {"message": "Contrato direto"}, "swapEditLimit": {"message": "Editar limite"}, "swapEnableDescription": {"message": "Isso é obrigatório e dá à NeoNix permissão para trocar o seu $1.", "description": "Gives the user info about the required approval transaction for swaps. $1 will be the symbol of a token being approved for swaps."}, "swapEnableTokenForSwapping": {"message": "Isso vai $1 para trocas", "description": "$1 is for the 'enableToken' key, e.g. 'enable ETH'"}, "swapEnterAmount": {"message": "Insira um valor"}, "swapEstimatedNetworkFees": {"message": "Taxas de rede estimadas"}, "swapEstimatedNetworkFeesInfo": {"message": "Essa é a estimativa da taxa de rede que será usada para concluir sua troca. O valor real pode mudar conforme as condições de rede."}, "swapFailedErrorDescriptionWithSupportLink": {"message": "Falhas na transação acontecem, e estamos aqui para ajudar. Se esse problema persistir, você pode entrar em contato com o nosso Suporte em $1 para receber assistência adicional.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.NeoNix.io"}, "swapFailedErrorTitle": {"message": "Falha na troca"}, "swapFetchingQuote": {"message": "Buscando <PERSON>"}, "swapFetchingQuoteNofN": {"message": "Obtendo cotação $1 de $2", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapFetchingQuotes": {"message": "<PERSON><PERSON><PERSON>..."}, "swapFetchingQuotesErrorDescription": {"message": "<PERSON>m, ocorreu algum erro. Tente novamente. O<PERSON>, se os erros persistirem, entre em contato com o Suporte."}, "swapFetchingQuotesErrorTitle": {"message": "Erro ao obter cotações"}, "swapFromTo": {"message": "A troca de $1 por $2", "description": "Tells a user that they need to confirm on their hardware wallet a swap of 2 tokens. $1 is a source token and $2 is a destination token"}, "swapGasFeesDetails": {"message": "As taxas de gás são estimadas e oscilam com base no tráfego da rede e na complexidade da transação."}, "swapGasFeesExplanation": {"message": "A NeoNix não ganha dinheiro com taxas de gás. Essas taxas são estimativas e podem mudar de acordo com a carga de atividade da rede e a complexidade da transação. Saiba mais $1.", "description": "$1 is a link (text in link can be found at 'swapGasFeesSummaryLinkText')"}, "swapGasFeesExplanationLinkText": {"message": "aqui", "description": "Text for link in swapGasFeesExplanation"}, "swapGasFeesLearnMore": {"message": "Saiba mais sobre as taxas de gás"}, "swapGasFeesSplit": {"message": "As taxas de gás da tela anterior estão divididas entre essas duas transações."}, "swapGasFeesSummary": {"message": "As taxas de gás são pagas aos mineradores de criptoativos que processam as transações na rede de $1. A NeoNix não lucra com taxas de gás.", "description": "$1 is the selected network, e.g. Ethereum or BSC"}, "swapGasIncludedTooltipExplanation": {"message": "Esta cotação incorpora taxas de gás através de ajuste do valor do token enviado ou recebido. Você pode receber ETH em uma transação separada em sua lista de atividades."}, "swapGasIncludedTooltipExplanationLinkText": {"message": "Saiba mais sobre taxas de gás"}, "swapHighSlippage": {"message": "Slippage alto"}, "swapIncludesGasAndNeoNixFee": {"message": "Inclui taxa de gás e uma taxa de $1% da NeoNix", "description": "Provides information about the fee that NeoNix takes for swaps. $1 is a decimal number."}, "swapIncludesMMFee": {"message": "Inclui uma taxa de $1% da NeoNix.", "description": "Provides information about the fee that NeoNix takes for swaps. $1 is a decimal number."}, "swapIncludesMMFeeAlt": {"message": "A cotação reflete a taxa de $1% da NeoNix", "description": "Provides information about the fee that NeoNix takes for swaps using the latest copy. $1 is a decimal number."}, "swapIncludesNeoNixFeeViewAllQuotes": {"message": "Inclui uma taxa de $1% da NeoNix – $2", "description": "Provides information about the fee that NeoNix takes for swaps. $1 is a decimal number and $2 is a link to view all quotes."}, "swapLearnMore": {"message": "<PERSON><PERSON> mais sobre as trocas"}, "swapLiquiditySourceInfo": {"message": "Pesquisamos várias fontes de liquidez (corretoras, agregadores e formadores de mercado profissionais) para comparar taxas de câmbio e taxas de rede."}, "swapLowSlippage": {"message": "Slippage baixo"}, "swapMaxSlippage": {"message": "Slippage máximo"}, "swapNeoNixFee": {"message": "Taxa da NeoNix"}, "swapNeoNixFeeDescription": {"message": "A taxa de $1% é automaticamente contabilizada nessa cotação. Você a paga em troca de uma licença para usar o software de agregação de informações sobre provedores de liquidez da NeoNix.", "description": "Provides information about the fee that NeoNix takes for swaps. $1 is a decimal number."}, "swapNQuotesWithDot": {"message": "$1 cotações.", "description": "$1 is the number of quotes that the user can select from when opening the list of quotes on the 'view quote' screen"}, "swapNewQuoteIn": {"message": "Novas cotações em $1", "description": "Tells the user the amount of time until the currently displayed quotes are update. $1 is a time that is counting down from 1:00 to 0:00"}, "swapNoTokensAvailable": {"message": "Nenhum token disponível correspondente a $1", "description": "Tells the user that a given search string does not match any tokens in our token lists. $1 can be any string of text"}, "swapOnceTransactionHasProcess": {"message": "Seu $1 será adicionado à sua conta quando essa transação for processada.", "description": "This message communicates the token that is being transferred. It is shown on the awaiting swap screen. The $1 will be a token symbol."}, "swapPriceDifference": {"message": "Você está prestes a trocar $1 $2 (~$3) por $4 $5 (~$6).", "description": "This message represents the price slippage for the swap.  $1 and $4 are a number (ex: 2.89), $2 and $5 are symbols (ex: ETH), and $3 and $6 are fiat currency amounts."}, "swapPriceDifferenceTitle": {"message": "Diferença de preço de aproximadamente $1%", "description": "$1 is a number (ex: 1.23) that represents the price difference."}, "swapPriceUnavailableDescription": {"message": "O impacto no preço não pôde ser determinado devido à ausência de dados sobre o preço de mercado. Confirme que você está satisfeito com a quantidade de tokens que você está prestes a receber antes de fazer a troca."}, "swapPriceUnavailableTitle": {"message": "Verifique o preço antes de prosseguir"}, "swapProcessing": {"message": "Processando"}, "swapQuoteDetails": {"message": "Detalhes da cotação"}, "swapQuoteNofM": {"message": "$1 de $2", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapQuoteSource": {"message": "Fonte da cotação"}, "swapQuotesExpiredErrorDescription": {"message": "Solicite novas cotações para receber as taxas mais recentes."}, "swapQuotesExpiredErrorTitle": {"message": "Cotações vencidas"}, "swapQuotesNotAvailableDescription": {"message": "Esta rota de negociação não está disponível no momento. Tente alterar a quantia, rede ou token e encontraremos a melhor opção."}, "swapQuotesNotAvailableErrorDescription": {"message": "Experimente ajustar a quantidade ou as configurações de slippage e tente novamente."}, "swapQuotesNotAvailableErrorTitle": {"message": "Não há cotações disponíveis"}, "swapRate": {"message": "Preço"}, "swapReceiving": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "swapReceivingInfoTooltip": {"message": "Essa é uma estimativa. O valor exato dependerá do slippage."}, "swapRequestForQuotation": {"message": "Solicitação de cotação"}, "swapSelect": {"message": "Selecione"}, "swapSelectAQuote": {"message": "Selecione uma cotação"}, "swapSelectAToken": {"message": "Selecionar token"}, "swapSelectQuotePopoverDescription": {"message": "Abaixo estão todas as cotações reunidas de diversas fontes de liquidez."}, "swapSelectToken": {"message": "Selecionar token"}, "swapShowLatestQuotes": {"message": "Exibir últimas cotações"}, "swapSlippageHighDescription": {"message": "O slippage inserido ($1%) é considerado muito alto e pode resultar em uma taxa ruim", "description": "$1 is the amount of % for slippage"}, "swapSlippageHighTitle": {"message": "Slippage alto"}, "swapSlippageLowDescription": {"message": "Um valor assim baixo ($1%) pode resultar em falha na troca", "description": "$1 is the amount of % for slippage"}, "swapSlippageLowTitle": {"message": "Slippage baixo"}, "swapSlippageNegativeDescription": {"message": "O slippage deve ser maior ou igual a zero"}, "swapSlippageNegativeTitle": {"message": "Aumente o slippage para continuar"}, "swapSlippageOverLimitDescription": {"message": "A tolerância ao slippage deve ser de 15% ou menos. Qualquer valor superior resultará em uma taxa ruim."}, "swapSlippageOverLimitTitle": {"message": "Slippage muito alto"}, "swapSlippagePercent": {"message": "$1%", "description": "$1 is the amount of % for slippage"}, "swapSlippageTooltip": {"message": "Chamamos de \"slippage\" quando o preço muda entre o momento de realização da ordem e sua confirmação. Sua troca será cancelada automaticamente se o slippage exceder sua configuração de \"tolerância a slippage\"."}, "swapSlippageZeroDescription": {"message": "Há menos provedores de cotação com slippage zero, o que resultará em uma cotação menos competitiva."}, "swapSlippageZeroTitle": {"message": "Buscando provedores de slippage zero"}, "swapSource": {"message": "Fonte de liquidez"}, "swapSuggested": {"message": "Troca sugerida"}, "swapSuggestedGasSettingToolTipMessage": {"message": "Trocas são transações complexas e urgentes. Recomendamos essa taxa de gás para atingir o equilíbrio ideal entre o custo e a confiança de uma troca bem-sucedida."}, "swapSwapFrom": {"message": "T<PERSON>car <PERSON>"}, "swapSwapSwitch": {"message": "Alternar ordem dos tokens"}, "swapSwapTo": {"message": "Trocar por"}, "swapToConfirmWithHwWallet": {"message": "para confirmar com a sua carteira de hardware"}, "swapTokenAddedManuallyDescription": {"message": "Verifique esse token em $1 e certifique-se de que é o token que você deseja negociar.", "description": "$1 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenAddedManuallyTitle": {"message": "Token adicionado manualmente"}, "swapTokenAvailable": {"message": "Seu $1 foi adicionado à sua conta.", "description": "This message is shown after a swap is successful and communicates the exact amount of tokens the user has received for a swap. The $1 is a decimal number of tokens followed by the token symbol."}, "swapTokenBalanceUnavailable": {"message": "Não foi possível obter seu saldo de $1", "description": "This message communicates to the user that their balance of a given token is currently unavailable. $1 will be replaced by a token symbol"}, "swapTokenNotAvailable": {"message": "O token não está disponível para troca nesta região"}, "swapTokenToToken": {"message": "Trocar $1 por $2", "description": "Used in the transaction display list to describe a swap. $1 and $2 are the symbols of tokens in involved in a swap."}, "swapTokenVerifiedOn1SourceDescription": {"message": "$1 só foi verificado em 1 fonte. Considere verificá-lo em $2 antes de prosseguir.", "description": "$1 is a token name, $2 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenVerifiedOn1SourceTitle": {"message": "Token potencialmente inautêntico"}, "swapTokenVerifiedSources": {"message": "Confirmado por $1 fontes. Verificar em $2.", "description": "$1 the number of sources that have verified the token, $2 points the user to a block explorer as a place they can verify information about the token."}, "swapTooManyDecimalsError": {"message": "$1 permite até $2 decimais", "description": "$1 is a token symbol and $2 is the max. number of decimals allowed for the token"}, "swapTransactionComplete": {"message": "Transação concluída"}, "swapTwoTransactions": {"message": "2 transações"}, "swapUnknown": {"message": "Desconhecido"}, "swapZeroSlippage": {"message": "0% de slippage"}, "swapsMaxSlippage": {"message": "Tolerância a slippage"}, "swapsNotEnoughToken": {"message": "$1 insuficiente", "description": "Tells the user that they don't have enough of a token for a proposed swap. $1 is a token symbol"}, "swapsViewInActivity": {"message": "Ver na atividade"}, "switch": {"message": "Alternar"}, "switchEthereumChainConfirmationDescription": {"message": "Isso alternará a rede selecionada dentro da NeoNix para uma rede adicionada anteriormente:"}, "switchEthereumChainConfirmationTitle": {"message": "Permitir que esse site alterne a rede?"}, "switchInputCurrency": {"message": "Alternar moeda de entrada"}, "switchNetwork": {"message": "Alternar rede"}, "switchNetworks": {"message": "Alternar redes"}, "switchToNetwork": {"message": "Alternar para $1", "description": "$1 represents the custom network that has previously been added"}, "switchToThisAccount": {"message": "Alternar para esta conta"}, "switchedNetworkToastDecline": {"message": "Não exibir novamente"}, "switchedNetworkToastMessage": {"message": "$1 agora está ativa na $2", "description": "$1 represents the account name, $2 represents the network name"}, "switchedNetworkToastMessageNoOrigin": {"message": "Agora você está usando $1", "description": "$1 represents the network name"}, "switchingNetworksCancelsPendingConfirmations": {"message": "A alternância de redes cancelará todas as confirmações pendentes"}, "symbol": {"message": "Símbolo"}, "symbolBetweenZeroTwelve": {"message": "O símbolo deve ter 11 caracteres ou menos."}, "tenPercentIncreased": {"message": "10% de aumento"}, "terms": {"message": "Termos de Uso"}, "termsOfService": {"message": "Termos de Serviço"}, "termsOfUseAgreeText": {"message": " Eu concordo com os Termos de Uso, que se aplicam ao meu uso da NeoNix e de todos os seus recursos"}, "termsOfUseFooterText": {"message": "Favor rolar para ler todas as seções"}, "termsOfUseTitle": {"message": "Nossos Termos de Uso foram atualizados"}, "testNetworks": {"message": "Redes de teste"}, "testnets": {"message": "Testnets"}, "theme": {"message": "<PERSON><PERSON>"}, "themeDescription": {"message": "Escolha o seu tema preferido para a NeoNix."}, "thirdPartySoftware": {"message": "Aviso de software de terceiros", "description": "Title of a popup modal displayed when installing a snap for the first time."}, "time": {"message": "<PERSON><PERSON>"}, "tipsForUsingAWallet": {"message": "Dicas para usar uma carteira"}, "tipsForUsingAWalletDescription": {"message": "Adicionar tokens libera mais formas de usar a web3."}, "to": {"message": "Para"}, "toAddress": {"message": "Para: $1", "description": "$1 is the address to include in the To label. It is typically shortened first using shortenAddress"}, "toggleDecodeDescription": {"message": "Usamos os serviços do 4byte.directory e do Sourcify para decodificar e exibir dados de transações de forma mais legível. Isso ajuda você a entender o resultado de transações pendentes e passadas, mas pode resultar em compartilhamento do seu endereço IP."}, "token": {"message": "Token"}, "tokenAddress": {"message": "Endereço do token"}, "tokenAlreadyAdded": {"message": "Esse token já foi adicionado."}, "tokenAutoDetection": {"message": "Detecção automática de tokens"}, "tokenContractAddress": {"message": "Endereço de contrato do token"}, "tokenDecimal": {"message": "Decimal do token"}, "tokenDecimalFetchFailed": {"message": "É necessário o decimal do token. Encontre-o em: $1"}, "tokenDetails": {"message": "Dados do token"}, "tokenFoundTitle": {"message": "1 novo token encontrado"}, "tokenId": {"message": "ID do token"}, "tokenList": {"message": "Listas de tokens"}, "tokenMarketplace": {"message": "Marketplace de tokens"}, "tokenScamSecurityRisk": {"message": "golpes e riscos de segurança envolvendo tokens"}, "tokenStandard": {"message": "Padrão de token"}, "tokenSymbol": {"message": "Símbolo do token"}, "tokens": {"message": "Tokens"}, "tokensFoundTitle": {"message": "$1 novos tokens encontrados", "description": "$1 is the number of new tokens detected"}, "tokensInCollection": {"message": "Tokens em coleção"}, "tooltipApproveButton": {"message": "E<PERSON>u ciente"}, "tooltipSatusConnected": {"message": "conectada"}, "tooltipSatusConnectedUpperCase": {"message": "Conectado"}, "tooltipSatusNotConnected": {"message": "não conectada"}, "total": {"message": "Total"}, "totalVolume": {"message": "Volume total"}, "transaction": {"message": "transação"}, "transactionCancelAttempted": {"message": "Cancelamento da transação tentado com taxa de gás de $1 às $2"}, "transactionCancelSuccess": {"message": "Transação cancelada às $2"}, "transactionConfirmed": {"message": "Transação confirmada às $2."}, "transactionCreated": {"message": "Transação criada com valor de $1 às $2."}, "transactionDataFunction": {"message": "Função"}, "transactionDetailGasHeading": {"message": "Taxa de gás estimada"}, "transactionDetailMultiLayerTotalSubtitle": {"message": "Valor + taxas"}, "transactionDropped": {"message": "Transação abandonada às $2."}, "transactionError": {"message": "Erro de transação. Exceção gerada no código do contrato."}, "transactionErrorNoContract": {"message": "Ten<PERSON>do chamar uma função em um endereço que não está no contrato."}, "transactionErrored": {"message": "A transação encontrou um erro."}, "transactionFlowNetwork": {"message": "Rede"}, "transactionHistoryBaseFee": {"message": "Taxa-base (GWEI)"}, "transactionHistoryL1GasLabel": {"message": "Taxa de gás L1 total"}, "transactionHistoryL2GasLimitLabel": {"message": "Limite de gás L2"}, "transactionHistoryL2GasPriceLabel": {"message": "Preço do gás L2"}, "transactionHistoryMaxFeePerGas": {"message": "Taxa máxima por gás"}, "transactionHistoryPriorityFee": {"message": "Taxa de prioridade (GWEI)"}, "transactionHistoryTotalGasFee": {"message": "Taxa de gás total"}, "transactionIdLabel": {"message": "ID da transação", "description": "Label for the source transaction ID field."}, "transactionIncludesTypes": {"message": "Esta transação inclui: $1."}, "transactionResubmitted": {"message": "Transação reenviada com taxa de gás aumentada para $1 às $2"}, "transactionSettings": {"message": "Configurações da transação"}, "transactionSubmitted": {"message": "Transação enviada com taxa de gás estimada de $1 às $2."}, "transactionTotalGasFee": {"message": "Taxa de gás total", "description": "Label for the total gas fee incurred in the transaction."}, "transactionUpdated": {"message": "Transação atualizada às $2."}, "transactions": {"message": "Transações"}, "transfer": {"message": "Transferir"}, "transferCrypto": {"message": "Transferir criptom<PERSON>"}, "transferFrom": {"message": "Transferir de"}, "transferRequest": {"message": "Solicitação de transferência"}, "trillionAbbreviation": {"message": "T", "description": "Shortened form of 'trillion'"}, "troubleConnectingToLedgerU2FOnFirefox": {"message": "Estamos com problemas para conectar o seu Ledger. $1", "description": "$1 is a link to the wallet connection guide;"}, "troubleConnectingToLedgerU2FOnFirefox2": {"message": "Revise nosso guia de conexão de carteiras de hardware e tente de novo.", "description": "$1 of the ledger wallet connection guide"}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution": {"message": "Se você está usando a versão mais recente do Firefox, talvez esteja com um problema relacionado ao Firefox ter abandonado o suporte ao U2F. Saiba como corrigir esse problema $1.", "description": "It is a link to the ledger website for the workaround."}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution2": {"message": "aqui", "description": "Second part of the error message; It is a link to the ledger website for the workaround."}, "troubleConnectingToWallet": {"message": "Tivemos dificuldade para conectar-nos à sua $1. Revise $2 e tente novamente.", "description": "$1 is the wallet device name; $2 is a link to wallet connection guide"}, "troubleStarting": {"message": "A NeoNix teve problemas para iniciar. Esse erro pode ser intermitente, por isso tente reiniciar a extensão."}, "tryAgain": {"message": "Tente novamente"}, "turnOff": {"message": "Desativar"}, "turnOffNeoNixNotificationsError": {"message": "Ocorreu um erro ao desativar as notificações. Tente novamente mais tarde."}, "turnOn": {"message": "Ativar"}, "turnOnNeoNixNotifications": {"message": "Ativar notificações"}, "turnOnNeoNixNotificationsButton": {"message": "Ativar"}, "turnOnNeoNixNotificationsError": {"message": "Ocorreu um erro ao criar as notificações. Tente novamente mais tarde."}, "turnOnNeoNixNotificationsMessageFirst": {"message": "Fique por dentro do que acontece na sua carteira com notificações."}, "turnOnNeoNixNotificationsMessagePrivacyBold": {"message": "configurações de notificações."}, "turnOnNeoNixNotificationsMessagePrivacyLink": {"message": "Saiba como protegemos sua privacidade enquanto usa este recurso."}, "turnOnNeoNixNotificationsMessageSecond": {"message": "Para usar as notificações da carteira, nós usamos um perfil para sincronizar algumas configurações entre seus dispositivos. $1"}, "turnOnNeoNixNotificationsMessageThird": {"message": "Você pode desativar as notificações quando quiser no $1"}, "turnOnTokenDetection": {"message": "Ativar detecção avançada de token"}, "tutorial": {"message": "Tutorial"}, "twelveHrTitle": {"message": "12 h:"}, "u2f": {"message": "U2F", "description": "A name on an API for the browser to interact with devices that support the U2F protocol. On some browsers we use it to connect NeoNix to Ledger devices."}, "unapproved": {"message": "<PERSON>ão a<PERSON>rovado"}, "unexpectedBehavior": {"message": "Este comportamento é inesperado e deve ser relatado como um bug, mesmo que suas contas sejam restauradas corretamente. Use o link abaixo para enviar um relatório de bug para a NeoNix."}, "units": {"message": "unidades"}, "unknown": {"message": "Desconhecido"}, "unknownCollection": {"message": "Coleção sem nome"}, "unknownNetworkForKeyEntropy": {"message": "Rede desconhecida", "description": "Displayed on places like Snap install warning when regular name is not available."}, "unknownQrCode": {"message": "Erro: não conseguimos identificar esse código QR"}, "unlimited": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unlock": {"message": "Desb<PERSON>que<PERSON>"}, "unpin": {"message": "Desafixar"}, "unrecognizedChain": {"message": "Essa rede personalizada não foi reconhecida", "description": "$1 is a clickable link with text defined by the 'unrecognizedChanLinkText' key. The link will open to instructions for users to validate custom network details."}, "unsendableAsset": {"message": "O envio de tokens NFT (ERC-721) não é suportado no momento", "description": "This is an error message we show the user if they attempt to send an NFT asset type, for which currently don't support sending"}, "unstableTokenPriceDescription": {"message": "O preço desse token em USD é altamente volátil, indicando alto risco de perder valor significativo em caso de interação com ele."}, "unstableTokenPriceTitle": {"message": "Preço de token instável"}, "upArrow": {"message": "seta para cima"}, "update": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "updateEthereumChainConfirmationDescription": {"message": "Este site está solicitando a atualização do URL de rede padrão. Você pode editar padrões e informações de rede quando quiser."}, "updateNetworkConfirmationTitle": {"message": "Atualizar $1", "description": "$1 represents network name"}, "updateOrEditNetworkInformations": {"message": "Atualize suas informações ou"}, "updateRequest": {"message": "Solicitação de atualização"}, "updatedRpcForNetworks": {"message": "RPCs de rede atualizadas"}, "uploadDropFile": {"message": "Solte seu arquivo aqui"}, "uploadFile": {"message": "Fazer upload de arquivo"}, "urlErrorMsg": {"message": "URLs precisam do prefixo HTTP/HTTPS adequado."}, "use4ByteResolution": {"message": "Decodificar contratos inteligentes"}, "useMultiAccountBalanceChecker": {"message": "Agrupar solicitações de saldo de contas"}, "useMultiAccountBalanceCheckerSettingDescription": {"message": "Obtenha atualizações de saldo mais rápidas ao reunir solicitações de saldo de conta. Isso nos permite buscar seus saldos de conta em conjunto, assim você recebe atualizações mais ágeis e tem uma experiência melhorada. Quando esse recurso está desativado, terceiros podem ter menor probabilidade de associar suas contas umas às outras."}, "useNftDetection": {"message": "Detectar NFTs automaticamente"}, "useNftDetectionDescriptionText": {"message": "Permita que a NeoNix use serviços de terceiros para adicionar os NFTs que você possui. A detecção automática de NFTs expõe seu endereço IP e o endereço da sua conta a esses serviços. Ativar esse recurso pode associar seu endereço IP ao seu endereço Ethereum e exibir NFTs falsos enviados por golpistas. Você pode adicionar tokens manualmente para evitar esse risco."}, "usePhishingDetection": {"message": "Usar detecção de phishing"}, "usePhishingDetectionDescription": {"message": "Exibir uma advertência para os domínios de phishing destinados a usuários do Ethereum"}, "useSafeChainsListValidation": {"message": "Verificação de dados da rede"}, "useSafeChainsListValidationDescription": {"message": "A NeoNix usa um serviço terceirizado chamado $1 para exibir dados precisos e padronizados das redes. Isso reduz suas chances de se conectar a uma rede mal-intencionada ou incorreta. Ao usar esse recurso, seu endereço IP é exposto à chainid.network."}, "useSafeChainsListValidationWebsite": {"message": "chainid.network", "description": "useSafeChainsListValidationWebsite is separated from the rest of the text so that we can bold the third party service name in the middle of them"}, "useTokenDetectionPrivacyDesc": {"message": "A exibição automática de tokens enviados para a sua conta envolve a comunicação com servidores de terceiros para buscar as imagens dos tokens. Esses servidores terão acesso ao seu endereço IP."}, "usedByClients": {"message": "Usado por diversos clientes diferentes"}, "userName": {"message": "Nome de usuário"}, "userOpContractDeployError": {"message": "A implementação do contrato a partir de uma conta de contrato inteligente não é suportada"}, "version": {"message": "Vers<PERSON>"}, "view": {"message": "<PERSON>er"}, "viewActivity": {"message": "Ver atividade"}, "viewAllQuotes": {"message": "ver to<PERSON> as co<PERSON><PERSON><PERSON><PERSON>"}, "viewContact": {"message": "Ver contato"}, "viewDetails": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "viewMore": {"message": "Ver mais"}, "viewOnBlockExplorer": {"message": "Ver no explorador de blocos"}, "viewOnCustomBlockExplorer": {"message": "Ver $1 em $2", "description": "$1 is the action type. e.g (Account, Transaction, Swap) and $2 is the Custom Block Explorer URL"}, "viewOnEtherscan": {"message": "Ver $1 no Etherscan", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "viewOnExplorer": {"message": "Ver no explorador"}, "viewOnOpensea": {"message": "Ver na OpenSea"}, "viewSolanaAccount": {"message": "Ver conta <PERSON>"}, "viewTransaction": {"message": "Ver transação"}, "viewinExplorer": {"message": "Ver $1 no explorador", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "visitSite": {"message": "Visitar site"}, "visitSupportDataConsentModalAccept": {"message": "Confirmar"}, "visitSupportDataConsentModalDescription": {"message": "Deseja compartilhar seu identificador e versão do aplicativo NeoNix com a nossa Central de Suporte? Isso pode nos ajudar a resolver melhor o seu problema, mas é opcional."}, "visitSupportDataConsentModalReject": {"message": "Não compartilhar"}, "visitSupportDataConsentModalTitle": {"message": "Compartilhar detalhes do dispositivo com o suporte"}, "visitWebSite": {"message": "Visite nosso site"}, "wallet": {"message": "<PERSON><PERSON><PERSON>"}, "walletConnectionGuide": {"message": "nosso guia de conexão com a carteira de hardware"}, "wantToAddThisNetwork": {"message": "Desejar adicionar esta rede?"}, "wantsToAddThisAsset": {"message": "<PERSON><PERSON> permite que o ativo a seguir seja adicionado à sua carteira."}, "warning": {"message": "Atenção"}, "warningFromSnap": {"message": "Aviso de $1", "description": "$1 represents the name of the snap"}, "watchEthereumAccountsDescription": {"message": "Ativar esta opção permitirá que você visualize contas Ethereum através de um endereço público ou nome ENS. Para dar sua opinião sobre este recurso Beta, preencha este $1.", "description": "$1 is the link to a product feedback form"}, "watchEthereumAccountsToggle": {"message": "Visualizar contas Ethereum (Beta)"}, "watchOutMessage": {"message": "Tome cuidado com $1.", "description": "$1 is a link with text that is provided by the 'securityMessageLinkForNetworks' key"}, "weak": {"message": "Fraca"}, "web3": {"message": "Web3"}, "web3ShimUsageNotification": {"message": "Percebemos que o site atual tentou usar a API window.web3 removida. Se o site parecer estar corrompido, clique em $1 para obter mais informações.", "description": "$1 is a clickable link."}, "webhid": {"message": "WebHID", "description": "Refers to a interface for connecting external devices to the browser. Used for connecting ledger to the browser. Read more here https://developer.mozilla.org/en-US/docs/Web/API/WebHID_API"}, "websites": {"message": "sites", "description": "Used in the 'permission_rpc' message."}, "welcomeBack": {"message": "Boas-vindas de volta"}, "welcomeToNeoNix": {"message": "<PERSON><PERSON><PERSON> começar"}, "whatsThis": {"message": "O que é isso?"}, "willApproveAmountForBridging": {"message": "Isso aprovará $1 para realização de ponte."}, "willApproveAmountForBridgingHardware": {"message": "Será necessário confirmar duas transações na sua carteira de hardware."}, "withdrawing": {"message": "Sacando"}, "wrongNetworkName": {"message": "De acordo com os nossos registros, o nome da rede pode não corresponder a esta ID de cadeia."}, "yes": {"message": "<PERSON>m"}, "you": {"message": "Você"}, "youDeclinedTheTransaction": {"message": "Você recusou a transação."}, "youNeedToAllowCameraAccess": {"message": "Você precisa permitir o acesso à câmera para usar esse recurso."}, "youReceived": {"message": "<PERSON><PERSON><PERSON> recebeu", "description": "Label indicating the amount and asset the user received."}, "youSent": {"message": "Voc<PERSON> en<PERSON>u", "description": "Label indicating the amount and asset the user sent."}, "yourAccounts": {"message": "Suas contas"}, "yourActivity": {"message": "Sua atividade"}, "yourBalance": {"message": "<PERSON><PERSON> saldo"}, "yourNFTmayBeAtRisk": {"message": "Seu NFT pode estar em risco"}, "yourNetworks": {"message": "Suas redes"}, "yourPrivateSeedPhrase": {"message": "Sua Frase de Recuperação Secreta"}, "yourTransactionConfirmed": {"message": "Transação já confirmada"}, "yourTransactionJustConfirmed": {"message": "Não pudemos cancelar sua transação antes de ser confirmada na blockchain."}, "yourWalletIsReady": {"message": "Sua carteira está pronta"}}