{"QRHardwareInvalidTransactionTitle": {"message": "<PERSON><PERSON><PERSON>"}, "QRHardwareMismatchedSignId": {"message": "Data transaksi tidak sesuai. Harap periksa detail transaksi."}, "QRHardwarePubkeyAccountOutOfRange": {"message": "Tidak ada akun lainnya. Jika Anda ingin mengakses akun lain yang tidak terdaftar di bawah ini, harap hubungkan kembali dompet perangkat keras Anda dan pilih."}, "QRHardwareScanInstructions": {"message": "Taruh kode QR di depan kamera Anda. <PERSON><PERSON>, tetapi tidak akan memengaruhi pemba<PERSON>an."}, "QRHardwareSignRequestCancel": {"message": "<PERSON><PERSON>"}, "QRHardwareSignRequestDescription": {"message": "<PERSON><PERSON><PERSON> masuk dengan do<PERSON>et <PERSON>, klik 'Dapatkan Tanda Tangan' untuk menerima tanda tangan"}, "QRHardwareSignRequestGetSignature": {"message": "Dapatkan tanda tangan"}, "QRHardwareSignRequestSubtitle": {"message": "Pindai kode QR dengan dompet Anda"}, "QRHardwareSignRequestTitle": {"message": "<PERSON>a tanda tangan"}, "QRHardwareUnknownQRCodeTitle": {"message": "<PERSON><PERSON><PERSON>"}, "QRHardwareUnknownWalletQRCode": {"message": "Kode QR tidak valid. Harap pindai kode QR sinkronisasi dari dompet perangkat keras."}, "QRHardwareWalletImporterTitle": {"message": "Pindai kode QR"}, "QRHardwareWalletSteps1Description": {"message": "<PERSON>a dapat memilih dari daftar mitra pendukung kode QR resmi di bawah ini."}, "QRHardwareWalletSteps1Title": {"message": "Hubungkan dompet perangkat keras QR Anda"}, "QRHardwareWalletSteps2Description": {"message": "<PERSON><PERSON>"}, "SrpListHideAccounts": {"message": "Sembunyikan $1 akun", "description": "$1 is the number of accounts"}, "SrpListHideSingleAccount": {"message": "Sembunyikan 1 akun"}, "SrpListShowAccounts": {"message": "Tampilkan $1 akun", "description": "$1 is the number of accounts"}, "SrpListShowSingleAccount": {"message": "Tampilkan 1 akun"}, "about": {"message": "Tentang"}, "accept": {"message": "Terima"}, "acceptTermsOfUse": {"message": "Saya telah membaca dan menyetujui $1", "description": "$1 is the `terms` message"}, "accessingYourCamera": {"message": "<PERSON><PERSON><PERSON><PERSON> kamera <PERSON>..."}, "account": {"message": "<PERSON><PERSON><PERSON>"}, "accountActivity": {"message": "Aktivitas akun"}, "accountActivityText": {"message": "<PERSON><PERSON><PERSON> akun yang ingin mendapatkan notifikasi:"}, "accountDetails": {"message": "Detail akun"}, "accountIdenticon": {"message": "Identikon akun"}, "accountIsntConnectedToastText": {"message": "$1 tidak terhubung ke $2"}, "accountName": {"message": "<PERSON><PERSON> akun"}, "accountNameDuplicate": {"message": "Nama akun ini sudah digunakan", "description": "This is an error message shown when the user enters a new account name that matches an existing account name"}, "accountNameReserved": {"message": "Nama akun ini dicadangkan", "description": "This is an error message shown when the user enters a new account name that is reserved for future use"}, "accountOptions": {"message": "Opsi akun"}, "accountPermissionToast": {"message": "<PERSON><PERSON> akun <PERSON>ui"}, "accountSelectionRequired": {"message": "Anda harus memilih satu akun!"}, "accountTypeNotSupported": {"message": "<PERSON><PERSON> akun tidak didukung"}, "accounts": {"message": "<PERSON><PERSON><PERSON>"}, "accountsConnected": {"message": "<PERSON>kun ter<PERSON><PERSON>"}, "accountsPermissionsTitle": {"message": "<PERSON><PERSON><PERSON> akun <PERSON>a dan menyarankan transaksi"}, "accountsSmallCase": {"message": "akun"}, "active": {"message": "Aktif"}, "activity": {"message": "Aktivitas"}, "activityLog": {"message": "Log aktivitas"}, "add": {"message": "Tambah"}, "addACustomNetwork": {"message": "Tambahkan jaringan khusus"}, "addANetwork": {"message": "Tambahkan jaringan"}, "addANickname": {"message": "Tambahkan nama panggilan"}, "addAUrl": {"message": "Tambahkan URL"}, "addAccount": {"message": "Tambahkan akun"}, "addAccountFromNetwork": {"message": "Tambahkan akun $1", "description": "$1 is the network name, e.g. Bitcoin or Solana"}, "addAccountToNeoNix": {"message": "Tambahkan akun ke NeoNix"}, "addAcquiredTokens": {"message": "Tambahkan token yang <PERSON>a peroleh menggunakan NeoNix"}, "addAlias": {"message": "<PERSON><PERSON><PERSON> alias"}, "addBitcoinAccountLabel": {"message": "Akun Bitcoin"}, "addBlockExplorer": {"message": "Tambahkan block explorer"}, "addBlockExplorerUrl": {"message": "Tambahkan URL block explorer"}, "addContact": {"message": "Tambah kontak"}, "addCustomNetwork": {"message": "Tambahkan jaringan khusus"}, "addEthereumChainWarningModalHeader": {"message": "Cukup tambahkan penyedia RPC ini jika Anda yakin dapat memercayainya. $1", "description": "$1 is addEthereumChainWarningModalHeaderPartTwo passed separately so that it can be bolded"}, "addEthereumChainWarningModalHeaderPartTwo": {"message": "Penyedia tak bertanggung jawab mungkin berbohong tentang status blockchain dan merekam aktivitas jaringan Anda."}, "addEthereumChainWarningModalListHeader": {"message": "<PERSON><PERSON><PERSON> harus dapa<PERSON>, karena memiliki wewenang untuk:"}, "addEthereumChainWarningModalListPointOne": {"message": "<PERSON>ihat akun dan alama<PERSON>, serta menghubungkan keduanya"}, "addEthereumChainWarningModalListPointThree": {"message": "Tampilkan saldo akun dan status on-chain lainnya"}, "addEthereumChainWarningModalListPointTwo": {"message": "Siarkan transaksi And<PERSON>"}, "addEthereumChainWarningModalTitle": {"message": "<PERSON><PERSON> men<PERSON> penyedia RPC baru untuk Ethereum Mainnet"}, "addEthereumWatchOnlyAccount": {"message": "Pantau akun Ethereum (Beta)"}, "addFriendsAndAddresses": {"message": "Tambahkan teman dan alamat yang <PERSON>a percayai"}, "addHardwareWalletLabel": {"message": "<PERSON><PERSON> perangkat keras"}, "addIPFSGateway": {"message": "Tambahkan gateway IPFS pilihan Anda"}, "addImportAccount": {"message": "Tambahkan akun atau dompet perangkat keras"}, "addMemo": {"message": "Tambahkan memo"}, "addNetwork": {"message": "Tambahkan jaringan"}, "addNetworkConfirmationTitle": {"message": "Tambahkan $1", "description": "$1 represents network name"}, "addNewAccount": {"message": "Tambahkan akun Ethereum baru"}, "addNewEthereumAccountLabel": {"message": "Akun Ethereum"}, "addNewSolanaAccountLabel": {"message": "<PERSON><PERSON><PERSON>"}, "addNft": {"message": "Tambahkan NFT"}, "addNfts": {"message": "Tambahkan NFT"}, "addNonEvmAccount": {"message": "Tambahkan akun $1", "description": "$1 is the non EVM network where the account is going to be created, e.g. Bitcoin or Solana"}, "addNonEvmAccountFromNetworkPicker": {"message": "Untuk mengaktifkan jaringan $1, Anda perlu membuat akun $2.", "description": "$1 is the non EVM network where the account is going to be created, e.g. Solana Mainnet or Solana Devnet. $2 is the account type, e.g. Bitcoin or Solana"}, "addRpcUrl": {"message": "Tambahkan URL RPC"}, "addSnapAccountToggle": {"message": "Aktifkan \"Tambahkan akun <PERSON> (Beta)\""}, "addSnapAccountsDescription": {"message": "Mengaktifkan fitur ini akan memberi opsi untuk menambahkan akun Snap Beta baru langsung dari daftar akun Anda. Jika Anda menginstal akun <PERSON>, harap diingat bahwa ini adalah layanan pihak ketiga."}, "addSuggestedNFTs": {"message": "Tambahkan NFT yang disarankan"}, "addSuggestedTokens": {"message": "Tambahkan token yang disarankan"}, "addToken": {"message": "Tambahkan token"}, "addTokenByContractAddress": {"message": "Tidak dapat menemukan token? Tambahkan token secara manual dengan menempelkan alamatnya. Alamat kontrak token dapat ditemukan di $1", "description": "$1 is a blockchain explorer for a specific network, e.g. Etherscan for Ethereum"}, "addUrl": {"message": "Tambahkan URL"}, "addingAccount": {"message": "Menambahkan akun"}, "addingCustomNetwork": {"message": "Menambah<PERSON>"}, "additionalNetworks": {"message": "<PERSON><PERSON><PERSON>"}, "address": {"message": "<PERSON><PERSON><PERSON>"}, "addressCopied": {"message": "<PERSON><PERSON><PERSON> disalin!"}, "addressMismatch": {"message": "Ketidakcocokan alamat situs"}, "addressMismatchOriginal": {"message": "URL saat ini: $1", "description": "$1 replaced by origin URL in confirmation request"}, "addressMismatchPunycode": {"message": "Versi Punycode: $1", "description": "$1 replaced by punycode version of the URL in confirmation request"}, "advanced": {"message": "Lanjutan"}, "advancedBaseGasFeeToolTip": {"message": "Saat transaksi Anda dimasukkan ke dalam blok, selis<PERSON> antara biaya dasar maks dan biaya dasar aktual akan dikembalikan. Jumlah total dihitung sebagai biaya dasar maks (dalam GWEI) * batas gas."}, "advancedDetailsDataDesc": {"message": "Data"}, "advancedDetailsHexDesc": {"message": "Hex"}, "advancedDetailsNonceDesc": {"message": "<PERSON><PERSON>"}, "advancedDetailsNonceTooltip": {"message": "Ini adalah nomor transaksi suatu akun. Nonce untuk transaksi pertama adalah 0 dan meningkat secara berurutan."}, "advancedGasFeeDefaultOptIn": {"message": "Simpan nilai ini sebagai default saya untuk jaringan $1.", "description": "$1 is the current network name."}, "advancedGasFeeModalTitle": {"message": "Biaya gas lanjutan"}, "advancedGasPriceTitle": {"message": "Harga gas"}, "advancedPriorityFeeToolTip": {"message": "Biaya prioritas (alias “tip penambang”) langsung masuk ke penambang dan memberi insentif kepada mereka untuk memprioritaskan transaksi Anda."}, "airDropPatternDescription": {"message": "Riwayat token on-chain menampilkan kejadian sebelumnya mengenai aktivitas airdrop yang mencurigakan."}, "airDropPatternTitle": {"message": "Pola Airdrop"}, "airgapVault": {"message": "AirGap Vault"}, "alert": {"message": "Peringatan"}, "alertAccountTypeUpgradeMessage": {"message": "Anda memperbarui akun ke akun cerdas. Anda akan tetap menggunakan alamat akun yang sama sambil membuka transaksi yang lebih cepat dan biaya jaringan yang lebih rendah. $1."}, "alertAccountTypeUpgradeTitle": {"message": "<PERSON><PERSON>"}, "alertActionBuyWithNativeCurrency": {"message": "Beli $1"}, "alertActionUpdateGas": {"message": "Perbarui batas gas"}, "alertActionUpdateGasFee": {"message": "<PERSON><PERSON><PERSON>"}, "alertActionUpdateGasFeeLevel": {"message": "Perbarui opsi gas"}, "alertDisableTooltip": {"message": "Ini dapat diubah dalam \"Pengaturan > Peringatan\""}, "alertMessageAddressMismatchWarning": {"message": "Penyerang terkadang meniru situs dengan membuat perubahan kecil pada alamat situs. Pastikan Anda berinteraksi dengan situs yang dituju sebelum melanjutkan."}, "alertMessageChangeInSimulationResults": {"message": "Estimasi perubahan untuk transaksi ini telah diperbarui. <PERSON><PERSON><PERSON> dengan saksama sebelum melanjutkan."}, "alertMessageFirstTimeInteraction": {"message": "<PERSON><PERSON> be<PERSON>ksi dengan alamat ini untuk pertama kalinya. Pastikan alamat tersebut benar sebelum melanjutkan."}, "alertMessageGasEstimateFailed": {"message": "<PERSON>mi tidak dapat memberikan biaya akurat dan estimasi ini mungkin tinggi. <PERSON><PERSON> menyar<PERSON> Anda untuk memasukkan batas gas kustom, tetapi ada risiko transaksi tetap gagal."}, "alertMessageGasFeeLow": {"message": "Saat memilih biaya rendah, per<PERSON><PERSON><PERSON> transaksi lebih lambat dan waktu tunggu lebih lama. Untuk transaksi lebih cepat, pilih opsi biaya Pasar atau Agresif."}, "alertMessageGasTooLow": {"message": "Untuk melanjutkan transaksi ini, Anda perlu meningkatkan batas gas menjadi 21000 atau lebih tinggi."}, "alertMessageInsufficientBalanceWithNativeCurrency": {"message": "Anda tidak memiliki cukup $1 di akun untuk membayar biaya jaringan."}, "alertMessageNetworkBusy": {"message": "Harga gas tinggi dan estimasinya kurang akurat."}, "alertMessageNoGasPrice": {"message": "<PERSON>mi tidak dapat melanjutkan transaksi ini hingga Anda memperbarui biayanya secara manual."}, "alertMessageSignInDomainMismatch": {"message": "Situs yang membuat permintaan bukanlah situs yang Anda masuki. Ini dapat merupakan upaya untuk mencuri kredensial login Anda."}, "alertMessageSignInWrongAccount": {"message": "Situs ini meminta Anda masuk menggunakan akun yang salah."}, "alertModalAcknowledge": {"message": "<PERSON><PERSON> telah men<PERSON>ahui risikonya dan tetap ingin melanjutkan"}, "alertModalDetails": {"message": "Detail <PERSON>"}, "alertModalReviewAllAlerts": {"message": "<PERSON><PERSON><PERSON> semua per<PERSON>"}, "alertReasonChangeInSimulationResults": {"message": "<PERSON><PERSON> te<PERSON> be<PERSON>h"}, "alertReasonFirstTimeInteraction": {"message": "<PERSON><PERSON><PERSON> pertama"}, "alertReasonGasEstimateFailed": {"message": "Biaya tidak akurat"}, "alertReasonGasFeeLow": {"message": "<PERSON>cepa<PERSON> lambat"}, "alertReasonGasTooLow": {"message": "Batas gas rendah"}, "alertReasonInsufficientBalance": {"message": "<PERSON> tidak cukup"}, "alertReasonNetworkBusy": {"message": "<PERSON><PERSON><PERSON> sibuk"}, "alertReasonNoGasPrice": {"message": "Estimasi biaya tidak tersedia"}, "alertReasonPendingTransactions": {"message": "<PERSON><PERSON><PERSON>"}, "alertReasonSignIn": {"message": "Permintaan masuk <PERSON>n"}, "alertReasonWrongAccount": {"message": "<PERSON><PERSON><PERSON> salah"}, "alertSelectedAccountWarning": {"message": "Permintaan ini ditujukan untuk akun yang berbeda dari akun yang dipilih di dompet Anda. Untuk menggunakan akun lain, hubungkan akun tersebut ke situs."}, "alerts": {"message": "Peringatan"}, "all": {"message": "<PERSON><PERSON><PERSON>"}, "allNetworks": {"message": "<PERSON><PERSON><PERSON>"}, "allPermissions": {"message": "<PERSON><PERSON><PERSON>"}, "allTimeHigh": {"message": "<PERSON><PERSON><PERSON> sepanjang waktu"}, "allTimeLow": {"message": "<PERSON><PERSON><PERSON> sepanjang waktu"}, "allowNotifications": {"message": "<PERSON>zin<PERSON> notifikasi"}, "allowWithdrawAndSpend": {"message": "Izinkan $1 untuk menarik dan menggunakan hingga jumlah berikut:", "description": "The url of the site that requested permission to 'withdraw and spend'"}, "amount": {"message": "<PERSON><PERSON><PERSON>"}, "amountReceived": {"message": "<PERSON><PERSON><PERSON> ya<PERSON>"}, "amountSent": {"message": "<PERSON><PERSON><PERSON>"}, "andForListItems": {"message": "$1, dan $2", "description": "$1 is the first item, $2 is the last item in a list of items. Used in Snap Install Warning modal."}, "andForTwoItems": {"message": "$1 dan $2", "description": "$1 is the first item, $2 is the second item. Used in Snap Install Warning modal."}, "appDescription": {"message": "Dompet kripto paling tepercaya di dunia", "description": "The description of the application"}, "appName": {"message": "NeoNix", "description": "The name of the application"}, "appNameBeta": {"message": "NeoNix Beta", "description": "The name of the application (Beta)"}, "appNameFlask": {"message": "NeoNix Flask", "description": "The name of the application (Flask)"}, "apply": {"message": "Terapkan"}, "approve": {"message": "Setujui batas penggunaan"}, "approveButtonText": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "approveIncreaseAllowance": {"message": "Tingkatkan batas penggunaan $1", "description": "The token symbol that is being approved"}, "approveSpendingCap": {"message": "Setujui batas penggunaan $1", "description": "The token symbol that is being approved"}, "approved": {"message": "Disetuju<PERSON>"}, "approvedOn": {"message": "Disetujui pada $1", "description": "$1 is the approval date for a permission"}, "approvedOnForAccounts": {"message": "Disetujui pada $1 untuk $2", "description": "$1 is the approval date for a permission. $2 is the AvatarGroup component displaying account images."}, "areYouSure": {"message": "Anda yakin?"}, "asset": {"message": "<PERSON><PERSON>"}, "assetChartNoHistoricalPrices": {"message": "<PERSON><PERSON> tidak dapat mengambil data historis"}, "assetMultipleNFTsBalance": {"message": "$1 NFT"}, "assetOptions": {"message": "Opsi aset"}, "assetSingleNFTBalance": {"message": "$1 NFT"}, "assets": {"message": "<PERSON><PERSON>"}, "assetsDescription": {"message": "Autodeteksi token di dompet Anda, tampilkan NFT, dan dapatkan pembaruan saldo akun secara batch"}, "attemptToCancelSwapForFree": {"message": "Mencoba membatalkan pertukaran secara gratis"}, "attributes": {"message": "Atribut"}, "attributions": {"message": "Atribusi"}, "auroraRpcDeprecationMessage": {"message": "URL RPC Infura tidak lagi mendukung Aurora."}, "authorizedPermissions": {"message": "<PERSON>a telah mengot<PERSON>asi izin berikut"}, "autoDetectTokens": {"message": "<PERSON>ek<PERSON> otomatis token"}, "autoDetectTokensDescription": {"message": "Kami menggunakan API pihak ketiga untuk mendeteksi dan menampilkan token baru yang dikirim ke dompet Anda. Nonaktifkan jika Anda tidak ingin aplikasi memakai data secara otomatis dari layanan tersebut. $1", "description": "$1 is a link to a support article"}, "autoLockTimeLimit": {"message": "Timer kunci otomati<PERSON> (menit)"}, "autoLockTimeLimitDescription": {"message": "Atur waktu siaga dalam menit sebelum NeoNix terkunci."}, "average": {"message": "<PERSON>a-rata"}, "back": {"message": "Kembali"}, "backupAndSync": {"message": "Pencadangan dan <PERSON>"}, "backupAndSyncBasicFunctionalityNameMention": {"message": "fungsionalitas dasar"}, "backupAndSyncEnable": {"message": "Aktifkan pen<PERSON>dangan dan <PERSON>"}, "backupAndSyncEnableConfirmation": {"message": "Saat Anda mengaktifkan pencadangan dan <PERSON>, <PERSON><PERSON> juga akan mengaktifkan $1. Lanjutkan?", "description": "$1 is backupAndSyncBasicFunctionalityNameMention in bold."}, "backupAndSyncEnableDescription": {"message": "Pencadangan dan sinkronisasi memungkinkan kami menyimpan data terenkripsi untuk pengaturan dan fitur khusus Anda. Ini menjaga pengalaman NeoNix Anda tetap sama di semua perangkat serta memulihkan pengaturan dan fiturnya jika Anda perlu menginstal ulang NeoNix. Ini tidak mencadangkan Frasa Pemulihan Rahasia. $1.", "description": "$1 is link to the backup and sync privacy policy."}, "backupAndSyncEnableDescriptionUpdatePreferences": {"message": "Anda dapat memperbarui preferensi Anda setiap saat di $1", "description": "$1 is a bolded text that highlights the path to the settings page."}, "backupAndSyncEnableDescriptionUpdatePreferencesPath": {"message": "Pengaturan > Pencadangan dan sink<PERSON>."}, "backupAndSyncFeatureAccounts": {"message": "<PERSON><PERSON><PERSON>"}, "backupAndSyncManageWhatYouSync": {"message": "<PERSON><PERSON><PERSON> apa saja yang Anda sink<PERSON>kan"}, "backupAndSyncManageWhatYouSyncDescription": {"message": "Aktifkan apa saja yang disinkronkan di antara perangkat Anda."}, "backupAndSyncPrivacyLink": {"message": "<PERSON><PERSON><PERSON>i cara kami melindungi privasi Anda"}, "backupAndSyncSlideDescription": {"message": "Cadangkan akun Anda dan sinkronkan pengaturan."}, "backupAndSyncSlideTitle": {"message": "Memperkenalkan pencadangan dan <PERSON>"}, "backupApprovalInfo": {"message": "Kode rahasia ini diperlukan untuk memulihkan dompet seandainya perangkat <PERSON> hilang, lupa kata sandi, harus memasang kembali NeoNix, atau ingin mengakses dompet Anda di perangkat lain."}, "backupApprovalNotice": {"message": "Cadangkan Frasa Pemulihan Rahasia untuk menjaga keamanan dompet dan dana <PERSON>."}, "backupKeyringSnapReminder": {"message": "Pastikan Anda dapat mengakses sendiri akun yang dibuat oleh Snap ini sebelum menghapusnya"}, "backupNow": {"message": "Cadang<PERSON> se<PERSON>ng"}, "balance": {"message": "<PERSON><PERSON>"}, "balanceOutdated": {"message": "<PERSON><PERSON> mungkin sudah hilang"}, "baseFee": {"message": "<PERSON><PERSON><PERSON>"}, "basic": {"message": "<PERSON><PERSON>"}, "basicConfigurationBannerTitle": {"message": "Fungsionalitas dasar tidak aktif"}, "basicConfigurationDescription": {"message": "NeoNix menawarkan fitur dasar seperti detail token dan pengaturan gas melalui layanan internet. Alamat IP dibagikan saat menggunakan layanan internet, dalam hal ini kepada NeoNix. Ini sama seperti saat Anda mengunjungi situs web mana pun. NeoNix menggunakan data ini untuk sementara dan tidak akan pernah menjual data Anda. Anda dapat menggunakan VPN atau menonaktifkan layanan ini, akan tetapi hal ini dapat memengaruhi pengalaman dalam menggunakan NeoNix. Untuk selengkapnya, baca $1 kami.", "description": "$1 is to be replaced by the message for privacyMsg, and will link to https://nnxscan.io"}, "basicConfigurationLabel": {"message": "Fungsionalitas dasar"}, "basicConfigurationModalCheckbox": {"message": "<PERSON>a memahami dan ingin melanju<PERSON>kan"}, "basicConfigurationModalDisclaimerOff": {"message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> tidak akan benar-benar mengoptimalkan waktu di NeoNix. <PERSON>tur dasar (seperti detail token, pengaturan gas optimal, dan lainnya) tidak akan tersedia untuk Anda."}, "basicConfigurationModalDisclaimerOffAdditionalText": {"message": "<PERSON><PERSON> menonaktifkan ini, <PERSON><PERSON> juga akan menonaktifkan semua fitur di $1 dan $2.", "description": "$1 and $2 are bold text for basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst and basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast respectively"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst": {"message": "keamanan dan pri<PERSON>i, pencadangan dan <PERSON>"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "basicConfigurationModalDisclaimerOn": {"message": "Untuk mengoptimalkan waktu di NeoNix, <PERSON>a perlu mengaktifkan fitur ini. Fun<PERSON>i dasar (seperti detail token, pengaturan gas optimal, dan lainnya) penting untuk pengalaman web3."}, "basicConfigurationModalHeadingOff": {"message": "Nonaktifkan fungsionalitas dasar"}, "basicConfigurationModalHeadingOn": {"message": "Aktifkan fungsionalitas dasar"}, "bestPrice": {"message": "<PERSON><PERSON> terbaik"}, "beta": {"message": "Beta"}, "betaHeaderText": {"message": "Ini merupakan versi beta. Harap laporkan bug $1"}, "betaNeoNixVersion": {"message": "Versi NeoNix Beta"}, "betaTerms": {"message": "Ketentuan penggunaan Beta"}, "billionAbbreviation": {"message": "M", "description": "Shortened form of 'billion'"}, "blockExplorerAccountAction": {"message": "<PERSON><PERSON><PERSON>", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Account in Explorer"}, "blockExplorerAssetAction": {"message": "<PERSON><PERSON>", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Asset in Explorer"}, "blockExplorerSwapAction": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "This is used with viewOnEtherscan e.g View Swap on Etherscan"}, "blockExplorerUrl": {"message": "URL block explorer"}, "blockExplorerUrlDefinition": {"message": "URL digunakan sebagai block explorer un<PERSON><PERSON> jaringan ini."}, "blockExplorerView": {"message": "Lihat akun di $1", "description": "$1 replaced by URL for custom block explorer"}, "blockaid": {"message": "Blockaid"}, "blockaidAlertDescriptionBlur": {"message": "<PERSON><PERSON>, semua aset yang <PERSON>a daft<PERSON> di Blur bisa berpotensi hilang."}, "blockaidAlertDescriptionMalicious": {"message": "<PERSON>a be<PERSON>i dengan situs berbahaya. <PERSON><PERSON>, <PERSON><PERSON> akan kehilangan aset milik <PERSON>."}, "blockaidAlertDescriptionOpenSea": {"message": "<PERSON><PERSON>, semua aset yang <PERSON>a daft<PERSON>an di OpenSea bisa berpotensi hilang."}, "blockaidAlertDescriptionOthers": {"message": "<PERSON><PERSON> permintaan ini di<PERSON>, <PERSON><PERSON> dapat keh<PERSON>n aset milik <PERSON>. Sebaiknya batalkan permintaan ini."}, "blockaidAlertDescriptionTokenTransfer": {"message": "<PERSON><PERSON> aset milik Anda ke penipu. <PERSON><PERSON>, <PERSON><PERSON> akan kehilangan aset tersebut."}, "blockaidAlertDescriptionWithdraw": {"message": "<PERSON><PERSON> permin<PERSON> ini <PERSON>, <PERSON><PERSON> mengi<PERSON>kan penipu untuk menarik dan menggunakan aset Anda. Anda tidak akan mendapatkannya kembali."}, "blockaidDescriptionApproveFarming": {"message": "Jika Anda menyetujui permintaan ini, pihak ketiga yang terdeteksi melakukan penipuan dapat mengambil semua aset <PERSON>a."}, "blockaidDescriptionBlurFarming": {"message": "Jika Anda menyetujui permintaan ini, seseorang dapat mencuri aset Anda yang terdaftar di Blur."}, "blockaidDescriptionErrored": {"message": "<PERSON><PERSON>, kami tidak dapat memeriksa peringatan keamanan. Lanjutkan hanya jika Anda memercayai setiap alamat yang terlibat."}, "blockaidDescriptionMaliciousDomain": {"message": "Anda berinteraksi dengan domain berbahaya. Jika Anda menyetujui permintaan ini, aset <PERSON>a kemung<PERSON>an akan hilang."}, "blockaidDescriptionMightLoseAssets": {"message": "<PERSON><PERSON> Anda menyetujui permintaan ini, aset <PERSON>a kem<PERSON>an akan hilang."}, "blockaidDescriptionSeaportFarming": {"message": "Jika Anda menyetujui permintaan ini, sese<PERSON>g dapat mencuri aset Anda yang terdaftar di OpenSea."}, "blockaidDescriptionTransferFarming": {"message": "Jika Anda menyetujui permintaan ini, pihak ketiga yang terdeteksi melakukan penipuan akan mengambil semua aset <PERSON>."}, "blockaidMessage": {"message": "Menjaga privasi - tidak ada data yang dibagikan kepada pihak ketiga. Tersedia di Arbitrum, Avalanche, BNB chain, Mainnet Ethereum, Linea, Optimism, Polygon, Base, dan Sep<PERSON>."}, "blockaidTitleDeceptive": {"message": "Ini adalah permintaan tipuan"}, "blockaidTitleMayNotBeSafe": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "blockaidTitleSuspicious": {"message": "Ini adalah permintaan yang mencurigakan"}, "blockies": {"message": "Blockies"}, "borrowed": {"message": "Dipinjam"}, "boughtFor": {"message": "<PERSON><PERSON><PERSON>"}, "bridge": {"message": "Bridge"}, "bridgeAllowSwappingOf": {"message": "Izinkan akses yang tepat ke $1 $2 pada $3 untuk bridge", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "bridgeApproval": {"message": "Setujui bridge $1", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be bridged. $1 is the symbol of a token that has been approved."}, "bridgeApprovalWarning": {"message": "<PERSON>a mengi<PERSON>kan akses ke jumlah yang ditentukan, $1 $2. Kontrak tidak akan mengakses dana tambahan."}, "bridgeApprovalWarningForHardware": {"message": "Anda perlu mengizinkan akses ke $1 $2 untuk bridge, lalu menyetuju<PERSON> bridge ke $2. Ini akan membutuhkan dua konfirmasi terpisah."}, "bridgeBlockExplorerLinkCopied": {"message": "Tautan block explorer disalin!"}, "bridgeCalculatingAmount": {"message": "Menghitung..."}, "bridgeConfirmTwoTransactions": {"message": "Anda perlu mengonfirmasikan 2 transaksi pada dompet perangkat keras:"}, "bridgeCreateSolanaAccount": {"message": "<PERSON><PERSON><PERSON> akun <PERSON>"}, "bridgeCreateSolanaAccountDescription": {"message": "<PERSON>tuk beralih ke jar<PERSON>, <PERSON><PERSON> <PERSON>an akun dan alamat peneri<PERSON>."}, "bridgeCreateSolanaAccountTitle": {"message": "<PERSON><PERSON> me<PERSON>an akun <PERSON> terle<PERSON>h dahulu."}, "bridgeDetailsTitle": {"message": "Detail bridge", "description": "Title for the modal showing details about a bridge transaction."}, "bridgeEnterAmount": {"message": "<PERSON><PERSON><PERSON> j<PERSON>"}, "bridgeEnterAmountAndSelectAccount": {"message": "<PERSON><PERSON><PERSON><PERSON> jumlah dan pilih akun tujuan"}, "bridgeExplorerLinkViewOn": {"message": "Lihat di $1"}, "bridgeFetchNewQuotes": {"message": "Ambil yang baru?"}, "bridgeFrom": {"message": "Bridge dari"}, "bridgeFromTo": {"message": "Bridge $1 $2 ke $3", "description": "Tells a user that they need to confirm on their hardware wallet a bridge. $1 is amount of source token, $2 is the source network, and $3 is the destination network"}, "bridgeGasFeesSplit": {"message": "Biaya jaringan yang dikuotasi di layar sebelumnya mencakup kedua transaksi dan akan dibagi."}, "bridgeNetCost": {"message": "<PERSON><PERSON><PERSON>"}, "bridgeQuoteExpired": {"message": "<PERSON><PERSON><PERSON> kuotasi telah habis."}, "bridgeSelectDestinationAccount": {"message": "<PERSON><PERSON><PERSON> akun tujuan"}, "bridgeSelectNetwork": {"message": "<PERSON><PERSON><PERSON>"}, "bridgeSelectTokenAmountAndAccount": {"message": "<PERSON><PERSON><PERSON> token, jum<PERSON>, dan akun tujuan"}, "bridgeSelectTokenAndAmount": {"message": "<PERSON><PERSON><PERSON> token dan jumlah"}, "bridgeSolanaAccountCreated": {"message": "<PERSON><PERSON><PERSON> telah dibuat"}, "bridgeStatusComplete": {"message": "Se<PERSON><PERSON>", "description": "Status text indicating a bridge transaction has successfully completed."}, "bridgeStatusFailed": {"message": "Gaga<PERSON>", "description": "Status text indicating a bridge transaction has failed."}, "bridgeStatusInProgress": {"message": "Sedang Berlangsung", "description": "Status text indicating a bridge transaction is currently processing."}, "bridgeStepActionBridgeComplete": {"message": "$1 diterima di $2", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionBridgePending": {"message": "Menerima $1 di $2", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionSwapComplete": {"message": "$1 ditukar dengan $2", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeStepActionSwapPending": {"message": "Menukar $1 dengan $2", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeTerms": {"message": "<PERSON><PERSON><PERSON>"}, "bridgeTimingMinutes": {"message": "$1 mnt", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "bridgeTo": {"message": "Bridge ke"}, "bridgeToChain": {"message": "Bridge ke $1"}, "bridgeTokenCannotVerifyDescription": {"message": "<PERSON>ka menambahkan token ini secara manual, pastikan <PERSON>a mengetahui risiko terhadap dana Anda sebelum menggunakan bridge."}, "bridgeTokenCannotVerifyTitle": {"message": "Kami tidak dapat memverifikasi token ini."}, "bridgeTransactionProgress": {"message": "Transaksi $1 dari 2"}, "bridgeTxDetailsBridging": {"message": "Bridge"}, "bridgeTxDetailsDelayedDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> kami"}, "bridgeTxDetailsDelayedDescriptionSupport": {"message": "Dukungan NeoNix"}, "bridgeTxDetailsDelayedTitle": {"message": "A<PERSON><PERSON>h sudah lebih dari 3 jam?"}, "bridgeTxDetailsNonce": {"message": "<PERSON><PERSON>"}, "bridgeTxDetailsStatus": {"message": "Status"}, "bridgeTxDetailsTimestamp": {"message": "Stempel waktu"}, "bridgeTxDetailsTimestampValue": {"message": "Tanggal $1 pukul $2", "description": "$1 is the date, $2 is the time"}, "bridgeTxDetailsTokenAmountOnChain": {"message": "$1 $2 pada", "description": "$1 is the amount of the token, $2 is the ticker symbol of the token"}, "bridgeTxDetailsTotalGasFee": {"message": "Total biaya gas"}, "bridgeTxDetailsYouReceived": {"message": "<PERSON><PERSON>"}, "bridgeTxDetailsYouSent": {"message": "<PERSON><PERSON>"}, "bridgeValidationInsufficientGasMessage": {"message": "Anda tidak memiliki cukup $1 untuk membayar biaya gas untuk bridge ini. Ma<PERSON><PERSON>n jumlah yang lebih kecil atau beli lebih banyak $1."}, "bridgeValidationInsufficientGasTitle": {"message": "Diperlukan lebih banyak $1 untuk gas"}, "bridging": {"message": "Bridge"}, "browserNotSupported": {"message": "Browser <PERSON><PERSON> t<PERSON> didukung..."}, "buildContactList": {"message": "<PERSON><PERSON>t daftar kontak Anda"}, "builtAroundTheWorld": {"message": "NeoNix dirancang dan dibangun di seluruh dunia."}, "bulletpoint": {"message": "·"}, "busy": {"message": "Sibuk"}, "buyAndSell": {"message": "Beli/Jual"}, "buyMoreAsset": {"message": "Beli lebih banyak $1", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "buyNow": {"message": "<PERSON><PERSON>"}, "bytes": {"message": "Byte"}, "canToggleInSettings": {"message": "<PERSON>a dapat mengaktifkan kembali pemberitahuan ini di Pengaturan > Peringatan."}, "cancel": {"message": "<PERSON><PERSON>"}, "cancelPopoverTitle": {"message": "Batalkan transaksi"}, "cancelSpeedUpLabel": {"message": "Biaya gas ini akan $1 yang asli.", "description": "$1 is text 'replace' in bold"}, "cancelSpeedUpTransactionTooltip": {"message": "Untuk $1 suatu transaksi, biaya gas harus dinaikkan minimal 10% agar dapat dikenali oleh jaringan.", "description": "$1 is string 'cancel' or 'speed up'"}, "cancelled": {"message": "Di<PERSON><PERSON><PERSON>"}, "chainId": {"message": "ID chain"}, "chainIdDefinition": {"message": "ID chain digunakan untuk menandatangani transaksi untuk jaringan ini."}, "chainIdExistsErrorMsg": {"message": "ID chain ini saat ini digunakan oleh jaringan $1."}, "chainListReturnedDifferentTickerSymbol": {"message": "Simbol token ini tidak cocok dengan nama jaringan atau ID chain yang dimasukkan. Banyak token populer menggunakan simbol serupa, yang dapat digunakan penipu untuk mengelabui Anda agar mengirimkan token yang lebih berharga sebagai gantinya. Pastikan untuk memverifikasi semuanya sebelum melanjutkan."}, "chooseYourNetwork": {"message": "<PERSON><PERSON><PERSON>"}, "chooseYourNetworkDescription": {"message": "Bila Anda menggunakan pengaturan dan konfigurasi default, kami menggunakan Infura sebagai penyedia panggilan prosedur jarak jauh (RPC) default kami untuk menawarkan akses paling andal dan pribadi ke data Ethereum yang kami bisa. <PERSON><PERSON> kasus terbatas, kami dapat menggunakan penyedia RPC lain untuk memberikan pengalaman terbaik bagi pengguna kami. Anda dapat memilih RPC sendiri, tetapi ingatlah bahwa setiap RPC akan menerima alamat IP dan dompet Ethereum Anda untuk melakukan transaksi. Untuk mempelajari selengkapnya tentang cara Infura menangani data untuk akun EVM, baca $1, dan untuk akun Solana, $2.", "description": "$1 is a link to the privacy policy, $2 is a link to Solana accounts support"}, "chooseYourNetworkDescriptionCallToAction": {"message": "klik di sini"}, "chromeRequiredForHardwareWallets": {"message": "Anda perlu menggunakan NeoNix di Google Chrome untuk terhubung ke Dompet Perangkat Keras Anda."}, "circulatingSupply": {"message": "<PERSON><PERSON><PERSON> yang beredar"}, "clear": {"message": "Hapus"}, "clearActivity": {"message": "Hapus aktivitas dan data nonce"}, "clearActivityButton": {"message": "Hapus data tab aktivitas"}, "clearActivityDescription": {"message": "Tindakan ini mereset nonce akun dan menghapus data dari tab aktivitas di dompet Anda. Hanya akun dan jaringan saat ini yang akan terpengaruh. Saldo dan transaksi masuk Anda tidak akan berubah."}, "click": {"message": "Klik"}, "clickToConnectLedgerViaWebHID": {"message": "Klik di sini untuk menghubungkan Ledger Anda melalui WebHID", "description": "Text that can be clicked to open a browser popup for connecting the ledger device via webhid"}, "close": {"message": "<PERSON><PERSON><PERSON>"}, "closeExtension": {"message": "Tutup <PERSON>"}, "closeWindowAnytime": {"message": "<PERSON>a dapat menutup jendela ini setiap saat."}, "coingecko": {"message": "CoinGecko"}, "collectionName": {"message": "<PERSON><PERSON>"}, "comboNoOptions": {"message": "Opsi tidak ditemukan", "description": "Default text shown in the combo field dropdown if no options."}, "concentratedSupplyDistributionDescription": {"message": "Mayoritas suplai token dipegang oleh pemegang token teratas, se<PERSON>ga menimbulkan risiko manipulasi harga terpusat"}, "concentratedSupplyDistributionTitle": {"message": "Distribusi Suplai Terkonsentrasi"}, "configureSnapPopupDescription": {"message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> akan meninggalkan NeoNix untuk mengonfigurasi snap ini."}, "configureSnapPopupInstallDescription": {"message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> akan meninggalkan NeoNix untuk menginstal snap ini."}, "configureSnapPopupInstallTitle": {"message": "Instal snap"}, "configureSnapPopupLink": {"message": "<PERSON><PERSON> tautan ini untuk melanjutkan:"}, "configureSnapPopupTitle": {"message": "Konfigurasikan snap"}, "confirm": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "confirmAccountTypeSmartContract": {"message": "<PERSON><PERSON><PERSON> cerdas"}, "confirmAccountTypeStandard": {"message": "<PERSON><PERSON><PERSON> standar"}, "confirmAlertModalAcknowledgeMultiple": {"message": "<PERSON>a telah mengetahui peringatannya dan tetap ingin melanjutkan"}, "confirmAlertModalAcknowledgeSingle": {"message": "<PERSON>a telah mengetahui peringatannya dan tetap ingin melanjutkan"}, "confirmFieldPaymaster": {"message": "<PERSON><PERSON>ya <PERSON>"}, "confirmFieldTooltipPaymaster": {"message": "Biaya untuk transaksi ini akan dibayar oleh kontrak cerdas paymaster."}, "confirmGasFeeTokenBalance": {"message": "Saldo:"}, "confirmGasFeeTokenInsufficientBalance": {"message": "<PERSON> tidak cukup"}, "confirmGasFeeTokenNeoNixFee": {"message": "Termasuk biaya $1"}, "confirmGasFeeTokenModalNativeToggleNeoNix": {"message": "NeoNix sedang menambah saldo untuk menyelesaikan transaksi ini."}, "confirmGasFeeTokenModalNativeToggleWallet": {"message": "Bayar biaya jaringan menggunakan saldo di dompet Anda."}, "confirmGasFeeTokenModalPayETH": {"message": "Bayar dengan ETH"}, "confirmGasFeeTokenModalPayToken": {"message": "<PERSON><PERSON> dengan token lainnya"}, "confirmGasFeeTokenModalTitle": {"message": "<PERSON><PERSON><PERSON> token"}, "confirmGasFeeTokenToast": {"message": "Anda membayar biaya jaringan ini dengan $1"}, "confirmGasFeeTokenTooltip": {"message": "Biaya ini dibayarkan ke jaringan untuk memproses transaksi Anda. Biaya ini mencakup biaya NeoNix sebesar $1 untuk token non-ETH atau ETH yang telah didanai sebelumnya."}, "confirmInfoAccountNow": {"message": "<PERSON><PERSON><PERSON>"}, "confirmInfoSwitchingTo": {"message": "<PERSON><PERSON><PERSON>"}, "confirmNestedTransactionTitle": {"message": "Transaksi $1"}, "confirmPassword": {"message": "<PERSON>n<PERSON><PERSON><PERSON><PERSON> kata sandi"}, "confirmRecoveryPhrase": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "confirmSimulationApprove": {"message": "<PERSON><PERSON>"}, "confirmTitleAccountTypeSwitch": {"message": "Pembaruan akun"}, "confirmTitleApproveTransactionNFT": {"message": "Permintaan penarikan"}, "confirmTitleDeployContract": {"message": "Terapkan kontrak"}, "confirmTitleDescApproveTransaction": {"message": "Situs ini meminta izin untuk menarik NFT Anda"}, "confirmTitleDescDelegationRevoke": {"message": "<PERSON>a beralih kembali ke akun standar (EOA)."}, "confirmTitleDescDelegationUpgrade": {"message": "Anda beralih ke akun cerdas"}, "confirmTitleDescDeployContract": {"message": "Situs ini meminta Anda untuk menerapkan kontrak"}, "confirmTitleDescERC20ApproveTransaction": {"message": "Situs ini meminta izin untuk menarik token Anda"}, "confirmTitleDescPermitSignature": {"message": "Situs ini meminta izin untuk menggunakan token Anda."}, "confirmTitleDescSIWESignature": {"message": "Se<PERSON>ah situs ingin Anda masuk untuk membuktikan Anda pemilik akun ini."}, "confirmTitleDescSign": {"message": "Tinjau detail permintaan sebelum <PERSON>."}, "confirmTitlePermitTokens": {"message": "Permintaan batas penggunaan"}, "confirmTitleRevokeApproveTransaction": {"message": "<PERSON><PERSON>"}, "confirmTitleSIWESignature": {"message": "<PERSON><PERSON><PERSON><PERSON> masuk"}, "confirmTitleSetApprovalForAllRevokeTransaction": {"message": "<PERSON><PERSON>"}, "confirmTitleSignature": {"message": "<PERSON><PERSON><PERSON><PERSON> tanda tangan"}, "confirmTitleTransaction": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "confirmationAlertDetails": {"message": "Untuk melindungi aset <PERSON>, seba<PERSON><PERSON> tolak permintaan tersebut."}, "confirmationAlertModalTitleDescription": {"message": "<PERSON><PERSON> <PERSON>a berpotensi hilang"}, "confirmed": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "confusableUnicode": {"message": "'$1' se<PERSON>a dengan '$2'."}, "confusableZeroWidthUnicode": {"message": "Karakter Zero-width ditemukan."}, "confusingEnsDomain": {"message": "<PERSON><PERSON> telah mendeteksi karakter yang membingungkan di nama ENS. Periksa nama ENS untuk menghindari kemungkinan penipuan."}, "connect": {"message": "Hubungkan"}, "connectAccount": {"message": "Hubungkan akun"}, "connectAccountOrCreate": {"message": "Hubungkan akun atau buat baru"}, "connectAccounts": {"message": "Hubungkan akun"}, "connectAnAccountHeader": {"message": "Hubungkan akun"}, "connectManually": {"message": "Hubungkan ke situs saat ini secara manual"}, "connectMoreAccounts": {"message": "<PERSON><PERSON><PERSON><PERSON> akun lain"}, "connectSnap": {"message": "Hubungkan $1", "description": "$1 is the snap for which a connection is being requested."}, "connectWithNeoNix": {"message": "Hubungkan dengan NeoNix"}, "connectedAccounts": {"message": "<PERSON>kun yang terhubung"}, "connectedAccountsDescriptionPlural": {"message": "Anda memiliki $1 akun yang terhubung ke situs ini.", "description": "$1 is the number of accounts"}, "connectedAccountsDescriptionSingular": {"message": "Anda memiliki 1 akun yang terhubung ke situs ini."}, "connectedAccountsEmptyDescription": {"message": "NeoNix tidak terhubung ke situs ini. Untuk terhubung ke situs web3, cari dan klik tombol hubungkan."}, "connectedAccountsListTooltip": {"message": "$1 dapat melihat saldo akun, al<PERSON><PERSON>, aktivitas, dan menyarankan transaksi yang akan disetujui untuk akun terhubung.", "description": "$1 is the origin name"}, "connectedAccountsToast": {"message": "<PERSON>kun yang terhubung diperbarui"}, "connectedSites": {"message": "Situs yang terhubung"}, "connectedSitesAndSnaps": {"message": "Situs dan Snaps yang terhubung"}, "connectedSitesDescription": {"message": "$1 terhubung ke situs ini. <PERSON><PERSON>a dapat melihat alamat akun <PERSON>.", "description": "$1 is the account name"}, "connectedSitesEmptyDescription": {"message": "$1 tidak terhubung ke situs mana pun.", "description": "$1 is the account name"}, "connectedSnapAndNoAccountDescription": {"message": "NeoNix terhubung ke situs ini, tetapi belum ada akun yang terhubung"}, "connectedSnaps": {"message": "Snaps yang <PERSON>"}, "connectedWithAccount": {"message": "$1 akun terhubung", "description": "$1 represents account length"}, "connectedWithAccountName": {"message": "Terhubung dengan $1", "description": "$1 represents account name"}, "connectedWithNetwork": {"message": "$1 jaringan terhubung", "description": "$1 represents network length"}, "connectedWithNetworkName": {"message": "Terhubung dengan $1", "description": "$1 represents network name"}, "connecting": {"message": "Menghubungkan"}, "connectingTo": {"message": "Menghubungkan ke $1"}, "connectingToDeprecatedNetwork": {"message": "'$1' sedang dihentikan dan mungkin tidak dapat beroperasi. Coba jaringan la<PERSON>."}, "connectingToGoerli": {"message": "Menghubungkan ke jaringan uji <PERSON>li"}, "connectingToLineaGoerli": {"message": "Menghubungkan ke jaringan uji Linea Goerli"}, "connectingToLineaMainnet": {"message": "Menghubungkan ke Linea Mainnet"}, "connectingToLineaSepolia": {"message": "Menghubungkan ke jaringan uji Linea Sepolia"}, "connectingToMainnet": {"message": "Menghubungkan ke Ethereum Mainnet"}, "connectingToSepolia": {"message": "Menghubungkan ke jaringan uji <PERSON>"}, "connectionDescription": {"message": "Hubungkan situs web ini dengan NeoNix"}, "connectionFailed": {"message": "<PERSON><PERSON><PERSON><PERSON> gagal"}, "connectionFailedDescription": {"message": "Pengambilan $1 gagal, periksa jaringan Anda dan coba lagi.", "description": "$1 is the name of the snap being fetched."}, "connectionPopoverDescription": {"message": "Untuk terhubung ke situs, pilih tombol hubungkan. NeoNix hanya dapat terhubung ke situs web3."}, "connectionRequest": {"message": "Permin<PERSON>an kone<PERSON>i"}, "contactUs": {"message": "<PERSON><PERSON><PERSON><PERSON> kami"}, "contacts": {"message": "Kontak"}, "contentFromSnap": {"message": "Isi dari $1", "description": "$1 represents the name of the snap"}, "continue": {"message": "Lanjutkan"}, "contract": {"message": "Kontrak"}, "contractAddress": {"message": "<PERSON><PERSON><PERSON> k<PERSON>"}, "contractAddressError": {"message": "<PERSON><PERSON> token ke alamat kontrak token. Token ini berpotensi hilang."}, "contractDeployment": {"message": "Pengalokasian kontrak"}, "contractInteraction": {"message": "Interaksi kontrak"}, "convertTokenToNFTDescription": {"message": "<PERSON><PERSON> men<PERSON> bahwa aset ini merupakan NFT. Kini NeoNix memiliki dukungan asli penuh untuk NFT. Ingin menghapusnya dari daftar token dan menambahkannya sebagai NFT?"}, "convertTokenToNFTExistDescription": {"message": "<PERSON><PERSON> men<PERSON> bahwa aset ini telah ditambahkan sebagai NFT. Anda ingin menghapusnya dari daftar token?"}, "coolWallet": {"message": "CoolWallet"}, "copiedExclamation": {"message": "Disalin!"}, "copyAddress": {"message": "<PERSON><PERSON> alamat ke papan klip"}, "copyAddressShort": {"message": "<PERSON><PERSON>"}, "copyPrivateKey": {"message": "<PERSON><PERSON> kunci pribadi"}, "copyToClipboard": {"message": "<PERSON>in ke papan klip"}, "copyTransactionId": {"message": "Salin ID transaksi"}, "create": {"message": "Buat"}, "createNewAccountHeader": {"message": "Buat akun baru"}, "createPassword": {"message": "<PERSON>uat kata sandi"}, "createSnapAccountDescription": {"message": "$1 ingin menambahkan akun baru ke NeoNix."}, "createSnapAccountTitle": {"message": "<PERSON><PERSON><PERSON> akun"}, "createSolanaAccount": {"message": "<PERSON><PERSON><PERSON> akun <PERSON>"}, "creatorAddress": {"message": "<PERSON><PERSON><PERSON>"}, "crossChainSwapsLink": {"message": "<PERSON><PERSON><PERSON> antar jaringan dengan Portfolio NeoNix"}, "crossChainSwapsLinkNative": {"message": "<PERSON><PERSON><PERSON> antara jaringan dengan <PERSON>"}, "cryptoCompare": {"message": "CryptoCompare"}, "currencyConversion": {"message": "<PERSON>"}, "currencyRateCheckToggle": {"message": "<PERSON><PERSON><PERSON><PERSON> saldo dan pemeriksa harga <PERSON>"}, "currencyRateCheckToggleDescription": {"message": "Kami menggunakan API $1 dan $2 untuk menampilkan saldo serta harga token Anda. $3", "description": "$1 represents Coingecko, $2 represents CryptoCompare and $3 represents Privacy Policy"}, "currencySymbol": {"message": "Simbol mata uang"}, "currencySymbolDefinition": {"message": "Simbol ticker ditampilkan untuk mata uang jaringan ini."}, "currentAccountNotConnected": {"message": "<PERSON>kun Anda saat ini tidak terhubung"}, "currentExtension": {"message": "Halaman ekstensi saat ini"}, "currentLanguage": {"message": "Bahasa saat ini"}, "currentNetwork": {"message": "<PERSON><PERSON>an saat ini", "description": "Speicifies to token network filter to filter by current Network. Will render when network nickname is not available"}, "currentRpcUrlDeprecated": {"message": "URL RPC saat ini untuk jaringan ini tidak digunakan lagi."}, "currentTitle": {"message": "Saat ini:"}, "currentlyUnavailable": {"message": "Tidak tersedia di jaringan ini"}, "curveHighGasEstimate": {"message": "Grafik estimasi gas aktif"}, "curveLowGasEstimate": {"message": "Grafik estimasi gas rendah"}, "curveMediumGasEstimate": {"message": "Grafik estimasi gas pasar"}, "custom": {"message": "Lanjutan"}, "customGasSettingToolTipMessage": {"message": "Gunakan $1 untuk menyesuaikan harga gas. Anda akan bingung jika tidak terbiasa. Berinteraksi dengan risiko Anda sendiri.", "description": "$1 is key 'advanced' (text: 'Advanced') separated here so that it can be passed in with bold font-weight"}, "customSlippage": {"message": "Kustom"}, "customSpendLimit": {"message": "Batas penggunaan kustom"}, "customToken": {"message": "Token kustom"}, "customTokenWarningInNonTokenDetectionNetwork": {"message": "Deteksi token belum tersedia di jaringan ini. Harap impor token secara manual dan pastikan keamanannya. Pelajari seputar $1"}, "customTokenWarningInTokenDetectionNetwork": {"message": "Siapa pun dapat membuat token, termasuk membuat versi palsu dari token yang ada. Pelajari tentang $1"}, "customTokenWarningInTokenDetectionNetworkWithTDOFF": {"message": "Pastikan keamanan token sebelum mengimpornya. Pelajari cara menghindari $1. Anda juga dapat mengaktifkan deteksi token $2."}, "customerSupport": {"message": "dukungan pelanggan"}, "customizeYourNotifications": {"message": "<PERSON><PERSON><PERSON><PERSON> not<PERSON>"}, "customizeYourNotificationsText": {"message": "Aktifkan jenis notifikasi yang ingin diterima:"}, "dappSuggested": {"message": "Situs yang disarankan"}, "dappSuggestedGasSettingToolTipMessage": {"message": "$1 telah menyarankan harga ini.", "description": "$1 is url for the dapp that has suggested gas settings"}, "dappSuggestedHigh": {"message": "Situs yang disarankan"}, "dappSuggestedHighShortLabel": {"message": "Situs (tinggi)"}, "dappSuggestedShortLabel": {"message": "Situs"}, "dappSuggestedTooltip": {"message": "$1 telah mere<PERSON>n harga ini.", "description": "$1 represents the Dapp's origin"}, "darkTheme": {"message": "<PERSON><PERSON><PERSON>"}, "data": {"message": "Data"}, "dataCollectionForMarketing": {"message": "Pengumpulan data untuk pemasaran"}, "dataCollectionForMarketingDescription": {"message": "<PERSON><PERSON> akan menggunakan MetaMetrics untuk mempelajari cara Anda berinteraksi dengan komunikasi pemasaran. <PERSON><PERSON> mungkin akan membagikan berita yang relevan (seperti fitur produk dan materi lainnya)."}, "dataCollectionWarningPopoverButton": {"message": "<PERSON>e"}, "dataCollectionWarningPopoverDescription": {"message": "Anda menonaktifkan pengumpulan data untuk tujuan pemasaran kami. Ini hanya berlaku untuk perangkat ini. Jika Anda menggunakan NeoNix di perangkat lain, pastikan untuk keluar terlebih dahulu."}, "dataUnavailable": {"message": "data tidak tersedia"}, "dateCreated": {"message": "Tanggal dibuat"}, "dcent": {"message": "<PERSON><PERSON><PERSON>nt"}, "debitCreditPurchaseOptions": {"message": "Opsi pembelian kartu debit atau kredit"}, "decimal": {"message": "Desimal token"}, "decimalsMustZerotoTen": {"message": "Desimal minimal 0, dan tidak lebih dari 36."}, "decrypt": {"message": "<PERSON><PERSON><PERSON>"}, "decryptCopy": {"message": "<PERSON><PERSON> pesan yang dienkripsi"}, "decryptInlineError": {"message": "Pesan ini tidak dapat didekripsi karena terjadi kesalahan: $1", "description": "$1 is error message"}, "decryptMessageNotice": {"message": "$1 ingin membaca pesan ini untuk menyelesaikan tindakan Anda", "description": "$1 is the web3 site name"}, "decryptNeoNix": {"message": "<PERSON><PERSON><PERSON> pesan"}, "decryptRequest": {"message": "<PERSON><PERSON><PERSON>"}, "defaultRpcUrl": {"message": "URL RPC Default"}, "defaultSettingsSubTitle": {"message": "NeoNix menggunakan pengaturan default untuk menyeimbangkan keamanan dan kemudahan penggunaan. Ubah pengaturan ini untuk lebih meningkatkan privasi Anda."}, "defaultSettingsTitle": {"message": "Pengaturan privasi default"}, "defi": {"message": "<PERSON><PERSON><PERSON>"}, "defiTabErrorContent": {"message": "<PERSON>ba kunjungi lagi nanti."}, "defiTabErrorTitle": {"message": "Kami tidak dapat memuat halaman ini."}, "delete": {"message": "Hapus"}, "deleteContact": {"message": "<PERSON><PERSON> k<PERSON>"}, "deleteMetaMetricsData": {"message": "Hapus data MetaMetrics"}, "deleteMetaMetricsDataDescription": {"message": "Ini akan menghapus data MetaMetrics historis yang terkait dengan penggunaan Anda pada perangkat ini. Dompet dan akun Anda akan tetap sama seperti sekarang setelah data ini dihapus. Proses ini dapat memakan waktu hingga 30 hari. Lihat $1.", "description": "$1 will have text saying Privacy Policy "}, "deleteMetaMetricsDataErrorDesc": {"message": "Permintaan ini tidak dapat diselesaikan sekarang karena masalah server sistem analitis, coba lagi nanti"}, "deleteMetaMetricsDataErrorTitle": {"message": "Kami tidak dapat menghapus data ini sekarang"}, "deleteMetaMetricsDataModalDesc": {"message": "<PERSON><PERSON> akan menghapus semua data MetaMetrics Anda. Lanjutkan?"}, "deleteMetaMetricsDataModalTitle": {"message": "Hapus data MetaMetrics?"}, "deleteMetaMetricsDataRequestedDescription": {"message": "Anda memulai tindakan ini pada $1. Proses ini dapat memakan waktu hingga 30 hari. Lihat $2", "description": "$1 will be the date on which teh deletion is requested and $2 will have text saying Privacy Policy "}, "deleteNetworkIntro": {"message": "<PERSON><PERSON> menghapus jaringan ini, <PERSON><PERSON> harus menambahkannya lagi untuk melihat aset Anda di jaringan ini"}, "deleteNetworkTitle": {"message": "Hapus jaringan $1?", "description": "$1 represents the name of the network"}, "depositCrypto": {"message": "Mendeposit kripto dari akun lain dengan alamat dompet atau kode QR."}, "deprecatedGoerliNtwrkMsg": {"message": "<PERSON>a pembaruan pada sistem Ethereum, jar<PERSON>n uji <PERSON>li akan segera di<PERSON>."}, "deprecatedNetwork": {"message": "Jaringan ini tidak digunakan lagi"}, "deprecatedNetworkButtonMsg": {"message": "<PERSON><PERSON><PERSON>"}, "deprecatedNetworkDescription": {"message": "<PERSON><PERSON><PERSON> yang Anda coba hubungkan tidak lagi didukung oleh NeoNix. $1"}, "description": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "descriptionFromSnap": {"message": "Deskripsi dari $1", "description": "$1 represents the name of the snap"}, "destinationAccountPickerNoEligible": {"message": "Tidak ditemukan akun yang memenuhi syarat"}, "destinationAccountPickerNoMatching": {"message": "Tidak ditemukan akun yang cocok"}, "destinationAccountPickerReceiveAt": {"message": "Terima di"}, "destinationAccountPickerSearchPlaceholderToMainnet": {"message": "<PERSON><PERSON><PERSON> atau <PERSON>"}, "destinationAccountPickerSearchPlaceholderToSolana": {"message": "<PERSON><PERSON><PERSON>"}, "destinationTransactionIdLabel": {"message": "Tujuan ID Tx", "description": "Label for the destination transaction ID field."}, "details": {"message": "Detail"}, "developerOptions": {"message": "Opsi Pengembang"}, "disabledGasOptionToolTipMessage": {"message": "“$1” dinonaktifkan karena tidak memenuhi kenaikan minimum 10% dari biaya gas asli.", "description": "$1 is gas estimate type which can be market or aggressive"}, "disconnect": {"message": "<PERSON><PERSON><PERSON> kone<PERSON>i"}, "disconnectAllAccounts": {"message": "Putuskan koneksi semua akun"}, "disconnectAllAccountsConfirmationDescription": {"message": "Anda yakin ingin memutus koneksi? Fungsionalitas situs Anda bisa hilang."}, "disconnectAllAccountsText": {"message": "akun"}, "disconnectAllDescriptionText": {"message": "Ji<PERSON> Anda memutuskan koneksi dari situs ini, <PERSON><PERSON> harus menghubungkan kembali akun dan jaringan agar dapat menggunakan situs ini lagi."}, "disconnectAllSnapsText": {"message": "Snap"}, "disconnectMessage": {"message": "Ini akan memutus koneksi dari situs ini"}, "disconnectPrompt": {"message": "Putuskan koneksi $1"}, "disconnectThisAccount": {"message": "Putuskan koneksi akun ini"}, "disconnectedAllAccountsToast": {"message": "Se<PERSON>a akun terputus koneksinya dari $1", "description": "$1 is name of the dapp`"}, "disconnectedSingleAccountToast": {"message": "$1 terputus koneksinya dari $2", "description": "$1 is name of the name and $2 represents the dapp name`"}, "discover": {"message": "Temukan"}, "discoverSnaps": {"message": "<PERSON><PERSON><PERSON>", "description": "Text that links to the Snaps website. Displayed in a banner on Snaps list page in settings."}, "dismiss": {"message": "Lewatkan"}, "dismissReminderDescriptionField": {"message": "Aktifkan ini untuk melewatkan pesan pengingat pencadangan frasa pemulihan. Kami sangat merekomendasikan agar Anda mencadangkan Frasa Pemulihan Rahasia Anda untuk menghindari hilangnya dana"}, "dismissReminderField": {"message": "Lewatkan pengingat pencadangan Frasa Pemulihan Rahasia"}, "dismissSmartAccountSuggestionEnabledDescription": {"message": "Aktifkan ini agar saran \"Beralih ke Akun <PERSON>\" tidak muncul lagi di akun mana pun. Akun cerdas memungkinkan transaksi yang lebih cepat, biaya jaringan yang lebih rendah, serta fleksibilitas pembayaran yang lebih besar untuk akun tersebut."}, "dismissSmartAccountSuggestionEnabledTitle": {"message": "<PERSON><PERSON> saran \"<PERSON><PERSON><PERSON> ke <PERSON>kun <PERSON>\""}, "displayNftMedia": {"message": "Tampilkan media NFT"}, "displayNftMediaDescription": {"message": "Alamat IP dapat diketahui oleh OpenSea atau pihak ketiga lainnya saat menampilkan media dan data NFT. Ini memungkinkan penyerang menghubungkan alamat IP dengan alamat Ethereum Anda. Deteksi otomatis NFT bergantung pada pengaturan ini, dan tidak akan tersedia saat dinonaktifkan."}, "doNotShare": {"message": "<PERSON><PERSON> bagikan ini kepada siapa pun"}, "domain": {"message": "Domain"}, "done": {"message": "Se<PERSON><PERSON>"}, "dontShowThisAgain": {"message": "<PERSON>an tampilkan lagi"}, "downArrow": {"message": "panah turun"}, "downloadGoogleChrome": {"message": "Unduh Google Chrome"}, "downloadNow": {"message": "<PERSON><PERSON><PERSON>"}, "downloadStateLogs": {"message": "Unduh log status"}, "dragAndDropBanner": {"message": "<PERSON>a dapat menyeret jaringan untuk menyusun ulang. "}, "dropped": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "duplicateContactTooltip": {"message": "<PERSON>a kontak ini bertentangan dengan akun atau kontak yang ada"}, "duplicateContactWarning": {"message": "Anda memiliki kontak duplikat"}, "durationSuffixDay": {"message": "H", "description": "Shortened form of 'day'"}, "durationSuffixHour": {"message": "J", "description": "Shortened form of 'hour'"}, "durationSuffixMillisecond": {"message": "MD", "description": "Shortened form of 'millisecond'"}, "durationSuffixMinute": {"message": "M", "description": "Shortened form of 'minute'"}, "durationSuffixMonth": {"message": "B", "description": "Shortened form of 'month'"}, "durationSuffixSecond": {"message": "D", "description": "Shortened form of 'second'"}, "durationSuffixWeek": {"message": "M", "description": "Shortened form of 'week'"}, "durationSuffixYear": {"message": "T", "description": "Shortened form of 'year'"}, "earn": {"message": "Dapatkan"}, "edit": {"message": "Edit"}, "editANickname": {"message": "Edit nama <PERSON>n"}, "editAccounts": {"message": "<PERSON> akun"}, "editAddressNickname": {"message": "Edit nama pan<PERSON>n al<PERSON>t"}, "editCancellationGasFeeModalTitle": {"message": "Edit biaya pembatalan gas"}, "editContact": {"message": "<PERSON>"}, "editGasFeeModalTitle": {"message": "Edit biaya gas"}, "editGasLimitOutOfBounds": {"message": "Batas gas minimum adalah $1"}, "editGasLimitOutOfBoundsV2": {"message": "Batas gas harus lebih besar dari $1 dan kurang dari $2", "description": "$1 is the minimum limit for gas and $2 is the maximum limit"}, "editGasLimitTooltip": {"message": "Batas gas merupakan unit maksimum gas yang ingin Anda gunakan. Unit gas adalah pengganda untuk “Biaya prioritas maks” dan “Biaya maks”."}, "editGasMaxBaseFeeGWEIImbalance": {"message": "Biaya dasar maks tidak boleh lebih rendah dari biaya prioritas"}, "editGasMaxBaseFeeHigh": {"message": "<PERSON><PERSON>ya dasar maks lebih tinggi dari yang diperlukan"}, "editGasMaxBaseFeeLow": {"message": "Biaya dasar maks rendah untuk kondisi jaringan saat ini"}, "editGasMaxFeeHigh": {"message": "Biaya maks lebih tinggi dari yang diperlukan"}, "editGasMaxFeeLow": {"message": "Biaya maks terlalu rendah untuk kondisi jaringan"}, "editGasMaxFeePriorityImbalance": {"message": "Biaya maks tidak boleh lebih rendah dari biaya prioritas maks"}, "editGasMaxPriorityFeeBelowMinimum": {"message": "Biaya prioritas maks harus lebih besar dari 0 GWEI"}, "editGasMaxPriorityFeeBelowMinimumV2": {"message": "Biaya prioritas harus lebih besar dari 0."}, "editGasMaxPriorityFeeHigh": {"message": "Biaya prioritas maks lebih tinggi dari yang diperlukan. <PERSON><PERSON> mungkin akan membayar lebih dari yang dibutuhkan."}, "editGasMaxPriorityFeeHighV2": {"message": "Biaya prioritas lebih tinggi dari yang diperlukan. <PERSON><PERSON> mungkin akan membayar lebih dari yang dibutuhkan"}, "editGasMaxPriorityFeeLow": {"message": "Biaya prioritas maks rendah untuk kondisi jaringan saat ini"}, "editGasMaxPriorityFeeLowV2": {"message": "Biaya prioritas rendah untuk kondisi jaringan saat ini"}, "editGasPriceTooLow": {"message": "Harga gas harus lebih besar dari 0"}, "editGasPriceTooltip": {"message": "Jaringan ini memerlukan kolom “Harga gas” saat mengirimkan transaksi. Harga gas merupakan jumlah yang akan Anda bayar per unit gas."}, "editGasSubTextFeeLabel": {"message": "Biaya maks:"}, "editGasTitle": {"message": "Edit prioritas"}, "editGasTooLow": {"message": "<PERSON><PERSON><PERSON> pem<PERSON>san tak diketahui"}, "editInPortfolio": {"message": "<PERSON> di Portfolio"}, "editNetworkLink": {"message": "edit jaringan asli"}, "editNetworksTitle": {"message": "<PERSON>"}, "editNonceField": {"message": "Edit nonce"}, "editNonceMessage": {"message": "Ini merupakan fitur lan<PERSON>, gunakan dengan hati-hati."}, "editPermission": {"message": "<PERSON> i<PERSON>"}, "editPermissions": {"message": "<PERSON> i<PERSON>"}, "editSpeedUpEditGasFeeModalTitle": {"message": "Edit biaya gas percepatan"}, "editSpendingCap": {"message": "<PERSON> batas pengg<PERSON>an"}, "editSpendingCapAccountBalance": {"message": "Saldo akun: $1 $2"}, "editSpendingCapDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> jumlah yang layak untuk digunakan atas nama Anda."}, "editSpendingCapError": {"message": "Batas penggunaan tidak boleh melebihi $1 digit desimal. Hapus digit desimal untuk melanjutkan."}, "editSpendingCapSpecialCharError": {"message": "<PERSON><PERSON><PERSON>n angka saja"}, "enableAutoDetect": {"message": " Aktifkan deteksi otomatis"}, "enableFromSettings": {"message": " Aktif<PERSON> da<PERSON>."}, "enableSnap": {"message": "Aktifkan"}, "enableToken": {"message": "aktifkan $1", "description": "$1 is a token symbol, e.g. ETH"}, "enabled": {"message": "Diaktifkan"}, "enabledNetworks": {"message": "<PERSON><PERSON><PERSON> yang <PERSON>"}, "encryptionPublicKeyNotice": {"message": "$1 menging<PERSON>an kunci enkripsi publik Anda. <PERSON><PERSON>, situs ini akan dapat membuat pesan terenkripsi untuk Anda.", "description": "$1 is the web3 site name"}, "encryptionPublicKeyRequest": {"message": "Minta kunci publik enkripsi"}, "endpointReturnedDifferentChainId": {"message": "URL RPC yang Anda masukkan kembali ke ID chain yang berbeda ($1).", "description": "$1 is the return value of eth_chainId from an RPC endpoint"}, "enhancedTokenDetectionAlertMessage": {"message": "Saat ini, deteksi token lanjutan tersedia di $1. $2"}, "ensDomainsSettingDescriptionIntroduction": {"message": "NeoNix memungkinkan Anda melihat domain ENS pada bilah alamat browser Anda. Berikut cara kerjanya:"}, "ensDomainsSettingDescriptionOutroduction": {"message": "Ingatlah bahwa alamat IP akan diketahui oleh layanan pihak ketiga IPFS saat menggunakan fitur ini."}, "ensDomainsSettingDescriptionPart1": {"message": "NeoNix memeriksa kontrak ENS Ethereum untuk menemukan kode yang terhubung ke nama ENS."}, "ensDomainsSettingDescriptionPart2": {"message": "Jika kode terhubung ke IPFS, Anda dapat melihat konten yang dikaitkan dengannya (umumnya situs web)."}, "ensDomainsSettingTitle": {"message": "Tampilkan domain ENS di bilah alamat"}, "ensUnknownError": {"message": "<PERSON><PERSON><PERSON> gagal."}, "enterANameToIdentifyTheUrl": {"message": "Ma<PERSON>kkan nama untuk mengidentifikasi URL"}, "enterChainId": {"message": "Masukkan ID Chain"}, "enterMaxSpendLimit": {"message": "<PERSON><PERSON><PERSON><PERSON> batas penggunaan maksimum"}, "enterNetworkName": {"message": "<PERSON><PERSON><PERSON><PERSON> nama jaringan"}, "enterOptionalPassword": {"message": "<PERSON><PERSON><PERSON>n kata sandi opsional"}, "enterPasswordContinue": {"message": "<PERSON><PERSON>kkan kata sandi untuk melanjutkan"}, "enterRpcUrl": {"message": "Masukkan URL RPC"}, "enterSymbol": {"message": "Masukkan simbol"}, "enterTokenNameOrAddress": {"message": "<PERSON><PERSON><PERSON><PERSON> nama token atau tempel alamat"}, "enterYourPassword": {"message": "<PERSON><PERSON><PERSON><PERSON> kata sandi"}, "errorCode": {"message": "Kode: $1", "description": "Displayed error code for debugging purposes. $1 is the error code"}, "errorGettingSafeChainList": {"message": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat mendapatkan daftar rantai aman, lan<PERSON><PERSON><PERSON> dengan hati-hati."}, "errorMessage": {"message": "Pesan: $1", "description": "Displayed error message for debugging purposes. $1 is the error message"}, "errorName": {"message": "Kode: $1", "description": "Displayed error name for debugging purposes. $1 is the error name"}, "errorPageContactSupport": {"message": "Hubungi dukungan", "description": "Button for contact MM support"}, "errorPageDescribeUsWhatHappened": {"message": "<PERSON><PERSON><PERSON> yang terjadi", "description": "<PERSON><PERSON> for submitting report to sentry"}, "errorPageInfo": {"message": "Informasi Anda tidak dapat ditampilkan. <PERSON><PERSON>, dompet dan dana <PERSON>a aman.", "description": "Information banner shown in the error page"}, "errorPageMessageTitle": {"message": "<PERSON><PERSON> k<PERSON>han", "description": "Title for description, which is displayed for debugging purposes"}, "errorPageSentryFormTitle": {"message": "<PERSON><PERSON><PERSON> yang terjadi", "description": "In sentry feedback form, The title at the top of the feedback form."}, "errorPageSentryMessagePlaceholder": {"message": "Berbagi detail seperti cara kami dapat mereproduksi bug akan membantu kami memperbaiki masalah tersebut.", "description": "In sentry feedback form, The placeholder for the feedback description input field."}, "errorPageSentrySuccessMessageText": {"message": "Terima kasih! Kami akan segera memerik<PERSON>ya.", "description": "In sentry feedback form, The message displayed after a successful feedback submission."}, "errorPageTitle": {"message": "NeoNix mengalami kes<PERSON>han", "description": "Title of generic error page"}, "errorPageTryAgain": {"message": "Coba lagi", "description": "<PERSON><PERSON> for try again"}, "errorStack": {"message": "Tumpukan:", "description": "Title for error stack, which is displayed for debugging purposes"}, "errorWhileConnectingToRPC": {"message": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat menghub<PERSON>kan ke jaringan khusus."}, "errorWithSnap": {"message": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han pada $1", "description": "$1 represents the name of the snap"}, "estimatedFee": {"message": "Estimasi biaya"}, "estimatedFeeTooltip": {"message": "<PERSON><PERSON><PERSON> yang di<PERSON>an untuk memproses transaksi di jaringan."}, "ethGasPriceFetchWarning": {"message": "Biaya gas cadangan diberikan karena layanan estimasi gas utama saat ini tidak tersedia."}, "ethereumProviderAccess": {"message": "Berikan penyedia Ethereum akses ke $1", "description": "The parameter is the name of the requesting origin"}, "ethereumPublicAddress": {"message": "Alamat publik Ethereum"}, "etherscan": {"message": "Etherscan"}, "etherscanView": {"message": "<PERSON>hat akun di Etherscan"}, "etherscanViewOn": {"message": "Lihat di Etherscan"}, "existingChainId": {"message": "Informasi yang Anda masukkan terhubung dengan ID chain yang ada."}, "expandView": {"message": "<PERSON><PERSON><PERSON>"}, "experimental": {"message": "Eksperimental"}, "exploreweb3": {"message": "Jelajahi web3"}, "exportYourData": {"message": "Ekspor data Anda"}, "exportYourDataButton": {"message": "<PERSON><PERSON><PERSON>"}, "exportYourDataDescription": {"message": "Anda dapat mengekspor data seperti kontak dan preferensi Anda."}, "extendWalletWithSnaps": {"message": "<PERSON><PERSON><PERSON><PERSON>nap yang dibuat oleh komunitas untuk menyesuaikan pengalaman web3 Anda", "description": "Banner description displayed on Snaps list page in Settings when less than 6 Snaps is installed."}, "externalAccount": {"message": "<PERSON><PERSON><PERSON>"}, "externalExtension": {"message": "Ekstensi eksternal"}, "externalNameSourcesSetting": {"message": "<PERSON>a panggilan yang di<PERSON>ulkan"}, "externalNameSourcesSettingDescription": {"message": "<PERSON><PERSON> akan mengambil nama panggilan yang diusulkan untuk alamat yang berinteraksi dengan Anda dari sumber pihak ketiga seperti Etherscan, Infura, dan Lens Protocol. Sumber-sumber ini dapat melihat alamat-alamat tersebut dan alamat IP Anda. Alamat akun Anda tidak akan diketahui oleh pihak ketiga."}, "failed": {"message": "Gaga<PERSON>"}, "failedToFetchChainId": {"message": "Tidak dapat mengambil ID chain. Apakah URL RPC Anda benar?"}, "failover": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "failoverRpcUrl": {"message": "Pengalihan URL RPC"}, "failureMessage": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han dan kami tidak dapat menyelesaikan tindakan"}, "fast": {"message": "Cepat"}, "feeDetails": {"message": "Detail biaya"}, "fileImportFail": {"message": "Impor file tidak bekerja? Klik di sini!", "description": "Helps user import their account from a JSON file"}, "flaskWelcomeUninstall": {"message": "Anda harus menghapus ekstensi ini", "description": "This request is shown on the Flask Welcome screen. It is intended for non-developers, and will be bolded."}, "flaskWelcomeWarning1": {"message": "Flask ditujukan bagi para pengembang untuk bereksperimen dengan API baru yang tidak stabil. Kecuali Anda seorang pengembang atau penguji beta, $1.", "description": "This is a warning shown on the Flask Welcome screen, intended to encourage non-developers not to proceed any further. $1 is the bolded message 'flaskWelcomeUninstall'"}, "flaskWelcomeWarning2": {"message": "<PERSON><PERSON> tidak menjamin keamanan atau stabilitas ekstensi ini. API baru yang ditawarkan oleh Flask tidak diperkuat untuk menghadapi serangan pengelabuan, artin<PERSON> setiap situs atau snap yang memerlukan Flask mungkin merupakan upaya jahat untuk mencuri aset Anda.", "description": "This explains the risks of using NeoNix Flask"}, "flaskWelcomeWarning3": {"message": "Semua API Flask bersifat eksperimental. API dapat diubah atau dihapus tanpa pemberitahuan, atau tetap berada di Flask tanpa batas waktu dan tanpa pernah dimigrasikan ke NeoNix yang stabil. Gunakan API dengan risiko Anda sendiri.", "description": "This message warns developers about unstable Flask APIs"}, "flaskWelcomeWarning4": {"message": "Pastikan untuk menonaktifkan ekstensi NeoNix reguler Anda saat menggunakan Flask.", "description": "This message calls to pay attention about multiple versions of NeoNix running on the same site (Flask + Prod)"}, "flaskWelcomeWarningAcceptButton": {"message": "<PERSON><PERSON> <PERSON> r<PERSON>", "description": "this text is shown on a button, which the user presses to confirm they understand the risks of using Flask"}, "floatAmountToken": {"message": "<PERSON><PERSON><PERSON> token harus berupa bilangan bulat"}, "followUsOnTwitter": {"message": "<PERSON><PERSON><PERSON> kami di Twitter"}, "forbiddenIpfsGateway": {"message": "Gateway IPFS Terlarang: Tentukan gateway CID"}, "forgetDevice": {"message": "Lupakan perangkat ini"}, "forgotPassword": {"message": "Lupa kata sandi?"}, "form": {"message": "formulir"}, "from": {"message": "<PERSON><PERSON>"}, "fromAddress": {"message": "Dari: $1", "description": "$1 is the address to include in the From label. It is typically shortened first using shortenAddress"}, "fromTokenLists": {"message": "Dari daftar token: $1"}, "function": {"message": "Fungsi: $1"}, "fundingMethod": {"message": "<PERSON><PERSON>"}, "gas": {"message": "Gas"}, "gasDisplayAcknowledgeDappButtonText": {"message": "Edit biaya gas yang disarankan"}, "gasDisplayDappWarning": {"message": "Biaya gas ini telah disarankan oleh $1. Pengabaian dapat menyebabkan masalah pada transaksi Anda. Hubungi $1 jika ada pertanyaan.", "description": "$1 represents the Dapp's origin"}, "gasFee": {"message": "Biaya gas"}, "gasLimit": {"message": "Batas gas"}, "gasLimitRecommended": {"message": "Batas gas yang disarankan adalah $1. Jika batas gas kurang dari itu, maka berpotensi gagal."}, "gasLimitTooLow": {"message": "Batas gas minimum adalah 21000"}, "gasLimitV2": {"message": "Batas gas"}, "gasOption": {"message": "Opsi gas"}, "gasPriceExcessive": {"message": "Biaya gas Anda diatur terlalu tinggi. Pertimbangkan untuk menurunkan jumlahnya."}, "gasPriceFetchFailed": {"message": "Estimasi biaya gas gagal karena terjadi kesalahan pada jaringan."}, "gasTimingHoursShort": {"message": "$1 jam", "description": "$1 represents a number of hours"}, "gasTimingLow": {"message": "Lambat"}, "gasTimingMinutesShort": {"message": "$1 mnt", "description": "$1 represents a number of minutes"}, "gasTimingSecondsShort": {"message": "$1 dtk", "description": "$1 represents a number of seconds"}, "gasUsed": {"message": "Gas yang digunakan"}, "general": {"message": "<PERSON><PERSON>"}, "generalCameraError": {"message": "<PERSON>mi tidak dapat mengakses kamera Anda. Harap coba lagi."}, "generalCameraErrorTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>...."}, "generalDescription": {"message": "Sinkronkan pengaturan di seluruh perang<PERSON>, pilih preferensi jaringan, dan lacak data token"}, "genericExplorerView": {"message": "Lihat akun di $1"}, "goToSite": {"message": "<PERSON>uka situs"}, "goerli": {"message": "<PERSON><PERSON><PERSON>"}, "gotIt": {"message": "<PERSON><PERSON><PERSON>"}, "grantExactAccess": {"message": "Berikan akses yang tepat"}, "gwei": {"message": "GWEI"}, "hardware": {"message": "Perangkat keras"}, "hardwareWalletConnected": {"message": "Dompet perangkat keras terhubung"}, "hardwareWalletLegacyDescription": {"message": "(warisan)", "description": "Text representing the MEW path"}, "hardwareWalletSubmissionWarningStep1": {"message": "Pastikan $1 sudah terhubung dan pilih aplikasi Ethereum."}, "hardwareWalletSubmissionWarningStep2": {"message": "Aktifkan \"data kontrak cerdas\" atau \"penandatanganan buta\" pada perangkat $1 Anda."}, "hardwareWalletSubmissionWarningTitle": {"message": "Sebelum mengklik Kirim:"}, "hardwareWalletSupportLinkConversion": {"message": "klik di sini"}, "hardwareWallets": {"message": "Hubungkan dompet perangkat keras"}, "hardwareWalletsInfo": {"message": "Integrasi dompet perangkat keras menggunakan panggilan API ke server eksternal, yang dapat melihat alamat IP Anda dan alamat kontrak cerdas tempat Anda berinteraksi."}, "hardwareWalletsMsg": {"message": "<PERSON><PERSON><PERSON> dompet perangkat keras yang ingin Anda gunakan dengan NeoNix."}, "here": {"message": "di sini", "description": "as in -click here- for more information (goes with troubleTokenBalances)"}, "hexData": {"message": "Data Hex"}, "hiddenAccounts": {"message": "<PERSON><PERSON><PERSON>"}, "hide": {"message": "Sembunyikan"}, "hideAccount": {"message": "Sembunyikan akun"}, "hideAdvancedDetails": {"message": "Sembunyikan detail lanjutan"}, "hideSentitiveInfo": {"message": "Sembunyikan informasi sensitif"}, "hideTokenPrompt": {"message": "Sembunyikan token?"}, "hideTokenSymbol": {"message": "Sembunyikan $1", "description": "$1 is the symbol for a token (e.g. 'DAI')"}, "hideZeroBalanceTokens": {"message": "Sembunyikan token tanpa saldo"}, "high": {"message": "Agresif"}, "highGasSettingToolTipMessage": {"message": "Gunakan $1 untuk menutupi lonjakan lalu lintas jaringan karena hal-hal seperti penurunan NFT populer.", "description": "$1 is key 'high' (text: 'Aggressive') separated here so that it can be passed in with bold font-weight"}, "highLowercase": {"message": "tinggi"}, "highestCurrentBid": {"message": "Penawaran tertinggi saat ini"}, "highestFloorPrice": {"message": "<PERSON><PERSON> dasar tertinggi"}, "history": {"message": "Riwayat"}, "holdToRevealContent1": {"message": "Frasa Pemulihan <PERSON> $1", "description": "$1 is a bolded text with the message from 'holdToRevealContent2'"}, "holdToRevealContent2": {"message": "akses penuh ke dompet dan dana <PERSON>.", "description": "Is the bolded text in 'holdToRevealContent1'"}, "holdToRevealContent3": {"message": "<PERSON>an bagikan ini kepada siapa pun. $1 $2", "description": "$1 is a message from 'holdToRevealContent4' and $2 is a text link with the message from 'holdToRevealContent5'"}, "holdToRevealContent4": {"message": "Dukungan NeoNix tidak akan memintanya,", "description": "Part of 'holdToRevealContent3'"}, "holdToRevealContent5": {"message": "tetapi penipu akan mencoba memintanya.", "description": "The text link in 'holdToRevealContent3'"}, "holdToRevealContentPrivateKey1": {"message": "Kunci Pribadi menyediakan $1", "description": "$1 is a bolded text with the message from 'holdToRevealContentPrivateKey2'"}, "holdToRevealContentPrivateKey2": {"message": "akses penuh ke dompet dan dana <PERSON>.", "description": "Is the bolded text in 'holdToRevealContentPrivateKey2'"}, "holdToRevealLockedLabel": {"message": "tahan untuk mengungkapkan lingkaran terkunci"}, "holdToRevealPrivateKey": {"message": "<PERSON><PERSON> untuk mengungkapkan Kunci Pribadi"}, "holdToRevealPrivateKeyTitle": {"message": "Amankan kunci pribadi Anda"}, "holdToRevealSRP": {"message": "<PERSON>han untuk mengungkapkan SRP"}, "holdToRevealSRPTitle": {"message": "Amankan SRP Anda"}, "holdToRevealUnlockedLabel": {"message": "tahan untuk mengungkapkan lingkaran terbuka"}, "honeypotDescription": {"message": "Token ini dapat menimbulkan risiko honeypot. Disarankan untuk melakukan uji tuntas sebelum berinteraksi untuk mencegah potensi kerugian finansial."}, "honeypotTitle": {"message": "Honey Pot"}, "howNetworkFeesWorkExplanation": {"message": "Estimasi biaya yang diperlukan untuk memproses transaksi. Biaya maksimumnya adalah $1."}, "howQuotesWork": {"message": "<PERSON> kerja kuotasi"}, "howQuotesWorkExplanation": {"message": "Kuotasi ini memiliki laba terbaik dari kuotasi yang kami cari. Ini berdasarkan pada nilai tukar, yang men<PERSON> biaya bridge dan biaya NeoNix sebesar $1%, dikurangi biaya gas. Biaya gas bergantung pada seberapa sibuk jaringan dan seberapa rumit transaksinya."}, "id": {"message": "ID"}, "ignoreAll": {"message": "<PERSON><PERSON><PERSON><PERSON> semua"}, "ignoreTokenWarning": {"message": "<PERSON><PERSON> Anda menyembunyikan token, token tersebut tidak akan ditampilkan di dompet <PERSON><PERSON>. <PERSON><PERSON>, <PERSON><PERSON> ma<PERSON>h dapat menambah<PERSON>nya dengan mencarinya."}, "imToken": {"message": "imToken"}, "import": {"message": "Impor", "description": "Button to import an account from a selected file"}, "importAccountError": {"message": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat mengimpor akun."}, "importAccountErrorIsSRP": {"message": "Anda telah memasukkan Frasa <PERSON> (atau mnemonik). Untuk mengimpor akun di sini, <PERSON>a harus memasukkan kunci pribadi, yang merupakan string heksadesimal dengan panjang 64 karakter."}, "importAccountErrorNotAValidPrivateKey": {"message": "Ini bukan kunci pribadi yang valid. Anda telah memasukkan string heks<PERSON><PERSON><PERSON>, tetapi pan<PERSON>a harus 64 karakter."}, "importAccountErrorNotHexadecimal": {"message": "Ini bukan kunci pribadi yang valid. Anda harus memasukkan string heksadesimal dengan panjang 64 karakter."}, "importAccountJsonLoading1": {"message": "Impor JSON diperkirakan memerlukan waktu beberapa menit dan bekukan NeoNix."}, "importAccountJsonLoading2": {"message": "<PERSON><PERSON>, kami akan berupaya untuk membuatnya semakin cepat di masa mendatang."}, "importAccountMsg": {"message": "Akun yang diimpor tidak akan ditautkan dengan Frasa Pemulihan Rahasia NeoNix Anda. Pelajari selengkapnya tentang akun yang diimpor"}, "importNFT": {"message": "Impor NFT"}, "importNFTAddressToolTip": {"message": "Di OpenSea, <PERSON><PERSON><PERSON>, pada halaman NFT di bawah <PERSON>ail, terdapat nilai pranala biru berlabel '<PERSON><PERSON><PERSON> Kontrak'. <PERSON><PERSON>, <PERSON><PERSON> akan diarahkan ke alamat kontrak di Etherscan; di sebelah kiri atas halaman tersebut, terdapat ikon berlabel '<PERSON><PERSON><PERSON>', dan di sebelah kanan, terdapat rangkaian huruf dan angka yang panjang. Ini merupakan alamat kontrak yang membuat NFT Anda. Klik ikon 'salin' di sebelah kanan alamat, dan Anda akan temukan di papan klip."}, "importNFTPage": {"message": "Impor halaman NFT"}, "importNFTTokenIdToolTip": {"message": "ID NFT merupakan pengenal unik karena tidak ada dua NFT yang sama. Se<PERSON>i lagi, angka ini berada di bawah 'Detail' pada OpenSea. Catat atau salin ke papan klip."}, "importNWordSRP": {"message": "Saya memiliki frasa pemulihan $1 kata", "description": "$1 is the number of words in the recovery phrase"}, "importPrivateKey": {"message": "<PERSON><PERSON><PERSON>"}, "importSRPDescription": {"message": "Impor dompet yang ada dengan frasa pemulihan rahasia 12 atau 24 kata."}, "importSRPNumberOfWordsError": {"message": "Fr<PERSON> terdiri dari 12 atau 24 kata"}, "importSRPWordError": {"message": "Kata $1 salah atau salah eja.", "description": "$1 is the word that is incorrect or misspelled"}, "importSRPWordErrorAlternative": {"message": "Kata $1 dan $2 salah atau salah eja.", "description": "$1 and $2 are multiple words that are mispelled."}, "importSecretRecoveryPhrase": {"message": "<PERSON><PERSON><PERSON>"}, "importSecretRecoveryPhraseUnknownError": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han yang tidak dikenal."}, "importSelectedTokens": {"message": "Impor token yang dipilih?"}, "importSelectedTokensDescription": {"message": "Hanya token yang dipilih yang akan muncul di dompet Anda. <PERSON><PERSON> se<PERSON>u dapat mengimpor token tersembunyi nanti dengan mencarinya."}, "importTokenQuestion": {"message": "Impor token?"}, "importTokenWarning": {"message": "Siapa pun dapat membuat token dengan nama apa pun, termasuk versi palsu dari token yang ada. Tambahkan dan perdagangkan dengan risiko ditanggung sendiri!"}, "importTokensCamelCase": {"message": "Impor token"}, "importTokensError": {"message": "<PERSON>mi tidak dapat mengimpor token. Coba lagi nanti."}, "importWallet": {"message": "Impor do<PERSON>"}, "importWalletOrAccountHeader": {"message": "I<PERSON>r dompet atau akun"}, "importWalletSuccess": {"message": "Frasa Pemulihan Rahasia $1 diimpor", "description": "$1 is the index of the secret recovery phrase"}, "importWithCount": {"message": "Impor $1", "description": "$1 will the number of detected tokens that are selected for importing, if all of them are selected then $1 will be all"}, "imported": {"message": "Diimpor", "description": "status showing that an account has been fully loaded into the keyring"}, "inYourSettings": {"message": "<PERSON> <PERSON><PERSON><PERSON><PERSON>"}, "included": {"message": "<PERSON><PERSON><PERSON>"}, "includesXTransactions": {"message": "Termasuk transaksi $1"}, "infuraBlockedNotification": {"message": "NeoNix tidak dapat terhubung ke host blockchain. <PERSON><PERSON><PERSON> alasan yang mungkin $1.", "description": "$1 is a clickable link with with text defined by the 'here' key"}, "initialTransactionConfirmed": {"message": "Transaksi awal Anda dikonfirma<PERSON>an o<PERSON>h <PERSON>. Klik <PERSON>e untuk kembali."}, "insightsFromSnap": {"message": "Wawasan dari $1", "description": "$1 represents the name of the snap"}, "install": {"message": "Instal"}, "installOrigin": {"message": "Instal asal"}, "installRequest": {"message": "Tambahkan ke NeoNix"}, "installedOn": {"message": "Diinstal di $1", "description": "$1 is the date when the snap has been installed"}, "insufficientBalance": {"message": "Saldo tidak cukup."}, "insufficientFunds": {"message": "<PERSON> tidak cukup."}, "insufficientFundsForGas": {"message": "Dana untuk gas tidak cukup"}, "insufficientLockedLiquidityDescription": {"message": "Kurangnya likuiditas yang dikunci atau dibakar secara memadai membuat token rentan terhadap penarikan likuiditas secara tiba-tiba, yang berpotensi menyebabkan ketidakstabilan pasar."}, "insufficientLockedLiquidityTitle": {"message": "Likuiditas Terkunci Tidak Cukup"}, "insufficientTokens": {"message": "Token tidak cukup."}, "interactWithSmartContract": {"message": "<PERSON><PERSON><PERSON> c<PERSON>"}, "interactingWith": {"message": "Berinteraks<PERSON> den<PERSON>"}, "interactingWithTransactionDescription": {"message": "Ini merupakan kontrak yang berinteraksi dengan Anda. Lindungi diri dari penipu dengan memverifikasi detailnya."}, "interaction": {"message": "Interaksi"}, "invalidAddress": {"message": "<PERSON><PERSON><PERSON> tida<PERSON> valid"}, "invalidAddressRecipient": {"message": "<PERSON><PERSON><PERSON> tida<PERSON> valid"}, "invalidAssetType": {"message": "Aset ini merupakan NFT dan harus ditambahkan kembali di halaman Impor NFT yang ada di bawah tab NFT"}, "invalidChainIdTooBig": {"message": "ID chain tidak valid. ID chain terlalu besar."}, "invalidCustomNetworkAlertContent1": {"message": "ID chain untuk jaringan kustom '$1' harus dimasukkan kembali.", "description": "$1 is the name/identifier of the network."}, "invalidCustomNetworkAlertContent2": {"message": "Untuk melindungi Anda dari penyedia jaringan yang jahat atau salah, ID chain kini diperlukan untuk semua jaringan kustom."}, "invalidCustomNetworkAlertContent3": {"message": "Buka Pengaturan > J<PERSON>an dan masukkan ID chain. Anda dapat menemukan ID chain dari jaringan paling populer di $1.", "description": "$1 is a link to https://chainid.network"}, "invalidCustomNetworkAlertTitle": {"message": "Jaringan kustom tidak valid"}, "invalidHexData": {"message": "Data hex tidak valid"}, "invalidHexNumber": {"message": "Bilangan heksadesimal tidak valid."}, "invalidHexNumberLeadingZeros": {"message": "Bilangan heksadesimal tidak valid. Hapus semua nol di depan."}, "invalidIpfsGateway": {"message": "Gateway IPFS tidak valid: <PERSON><PERSON> harus berupa URL yang valid"}, "invalidNumber": {"message": "Angka tidak valid. Masukkan bilangan heksadesimal berawalan '0x' atau desimal."}, "invalidNumberLeadingZeros": {"message": "Angka tidak valid. Hapus semua nol di depan."}, "invalidRPC": {"message": "URL RPC Tidak Valid"}, "invalidSeedPhrase": {"message": "Frasa Pemulihan <PERSON>"}, "invalidSeedPhraseCaseSensitive": {"message": "Masukan tidak valid! Frasa Pemulihan Rahasia peka terhadap huruf besar/kecil."}, "ipfsGateway": {"message": "Gateway IPFS"}, "ipfsGatewayDescription": {"message": "NeoNix menggunakan layanan pihak ketiga untuk menampilkan gambar NFT Anda yang disimpan di IPFS, menampilkan informasi terkait alamat ENS yang dimasukkan di bilah alamat browser Anda, serta mengambil ikon untuk token yang berbeda. Alamat IP Anda mungkin tidak memberikan perlindungan terhadap layanan ini saat Anda menggunakannya."}, "ipfsToggleModalDescriptionOne": {"message": "Ka<PERSON> menggunakan layanan pihak ketiga untuk menampilkan gambar NFT Anda yang disimpan di IPFS, menampilkan informasi terkait alamat ENS yang dimasukkan di bilah alamat browser Anda, serta mengambil ikon untuk token yang berbeda. Alamat IP Anda mungkin tidak memberikan perlindungan terhadap layanan ini saat Anda menggunakannya."}, "ipfsToggleModalDescriptionTwo": {"message": "Resolusi IPFS akan aktif saat Anda memilih <PERSON>nfi<PERSON>si. Anda dapat menonaktifkannya di $1 kapan saja.", "description": "$1 is the method to turn off ipfs"}, "ipfsToggleModalSettings": {"message": "Pengaturan > <PERSON><PERSON><PERSON> dan privasi"}, "isSigningOrSubmitting": {"message": "Transaksi sebelumnya sedang ditandatangani atau dikirim"}, "jazzAndBlockies": {"message": "Jazzicons dan Blockies merupakan dua gaya ikon unik yang berbeda untuk membantu Anda mengidentifikasi akun dengan cepat."}, "jazzicons": {"message": "Jazzicons"}, "jsonFile": {"message": "File JSON", "description": "format for importing an account"}, "keyringAccountName": {"message": "<PERSON><PERSON> akun"}, "keyringAccountPublicAddress": {"message": "<PERSON><PERSON><PERSON>"}, "keyringSnapRemovalResult1": {"message": "$1 $2dihapus", "description": "Displays the result after removal of a keyring snap. $1 is the snap name, $2 is whether it is successful or not"}, "keyringSnapRemovalResultNotSuccessful": {"message": "tidak ", "description": "Displays the `not` word in $2."}, "keyringSnapRemoveConfirmation": {"message": "Ketik $1 untuk mengonfirmasi bahwa Anda ingin menghapus snap ini:", "description": "Asks user to input the name nap prior to deleting the snap. $1 is the snap name"}, "keystone": {"message": "Keystone"}, "knownAddressRecipient": {"message": "<PERSON><PERSON><PERSON> k<PERSON> yang di<PERSON>."}, "knownTokenWarning": {"message": "Tindakan ini akan mengedit token yang telah terdaftar dalam dompet Anda, yang dapat digunakan untuk menipu Anda. Setujui hanya jika Anda yakin bahwa Anda ingin mengubah apa yang diwakili token ini. Pelajari selengkapnya seputar $1"}, "l1Fee": {"message": "Biaya L1"}, "l1FeeTooltip": {"message": "Biaya gas L1"}, "l2Fee": {"message": "Biaya L2"}, "l2FeeTooltip": {"message": "Biaya gas L2"}, "lastConnected": {"message": "<PERSON><PERSON><PERSON>"}, "lastSold": {"message": "<PERSON><PERSON><PERSON>"}, "lavaDomeCopyWarning": {"message": "<PERSON><PERSON> k<PERSON>, memilih teks ini tidak tersedia untuk saat ini."}, "layer1Fees": {"message": "Biaya layer 1"}, "layer2Fees": {"message": "Biaya layer 2"}, "learnHow": {"message": "<PERSON><PERSON><PERSON><PERSON>a"}, "learnMore": {"message": "pelajari se<PERSON>"}, "learnMoreAboutGas": {"message": "Ingin $1 seputar gas?", "description": "$1 will be replaced by the learnMore translation key"}, "learnMoreAboutPrivacy": {"message": "Selengkapnya seputar praktik terbaik privasi."}, "learnMoreAboutSolanaAccounts": {"message": "Pelajari selengkapnya tentang akun Sol<PERSON>"}, "learnMoreKeystone": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "learnMoreUpperCase": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "learnMoreUpperCaseWithDot": {"message": "Pelajari se<PERSON>g<PERSON>."}, "learnScamRisk": {"message": "penipuan dan risiko k<PERSON>n."}, "leaveNeoNix": {"message": "<PERSON><PERSON><PERSON> dari <PERSON>?"}, "leaveNeoNixDesc": {"message": "<PERSON>a akan mengunjungi situs di luar NeoNix. Periksa kembali URL sebelum melanjutkan."}, "ledgerAccountRestriction": {"message": "Manfaatkan akun terakhir Anda sebelum menambahkan yang baru."}, "ledgerConnectionInstructionCloseOtherApps": {"message": "Tutup perangkat lunak lain yang terhubung ke perangkat Anda, lalu klik di sini untuk memperbarui."}, "ledgerConnectionInstructionHeader": {"message": "Sebelum mengeklik konfirmasi:"}, "ledgerConnectionInstructionStepFour": {"message": "Aktifkan \"data kontrak cerdas\" atau \"penandatanganan buta\" pada perangkat Ledger Anda."}, "ledgerConnectionInstructionStepThree": {"message": "Pastikan Ledger terhubung dan untuk memilih aplikasi Ethereum."}, "ledgerDeviceOpenFailureMessage": {"message": "Perangkat Ledger gagal dibuka. Ledger Anda mungkin terhubung ke perangkat lunak lain. Tutup Ledger Live atau aplikasi lain yang terhubung ke perangkat Ledger Anda, dan coba hubungkan kembali."}, "ledgerErrorConnectionIssue": {"message": "Hubungkan kembali ledger <PERSON><PERSON>, buka aplikasi ETH dan coba lagi."}, "ledgerErrorDevicedLocked": {"message": "Ledger <PERSON><PERSON> terk<PERSON>. Buka lalu coba lagi."}, "ledgerErrorEthAppNotOpen": {"message": "Untuk mengatasi masalah ini, buka aplikasi ETH di perangkat Anda dan coba lagi."}, "ledgerErrorTransactionDataNotPadded": {"message": "Data input transaksi Ethereum tidak cukup lengkap."}, "ledgerLiveApp": {"message": "Aplikasi Ledger Live"}, "ledgerLocked": {"message": "Tidak dapat terhubung ke perangkat Ledger. Pastikan perangkat Anda tidak terkunci dan aplikasi Ethereum terbuka."}, "ledgerMultipleDevicesUnsupportedInfoDescription": {"message": "Untuk menghubungkan perangkat baru, putuskan koneksi perangkat sebelumnya."}, "ledgerMultipleDevicesUnsupportedInfoTitle": {"message": "Anda hanya dapat menghubungkan satu Ledger dalam satu waktu"}, "ledgerTimeout": {"message": "Respons Ledger Live terlalu lama atau waktu koneksi habis. Pastikan aplikasi Ledger Live terbuka dan perangkat Anda tidak terkunci."}, "ledgerWebHIDNotConnectedErrorMessage": {"message": "Perangkat ledger tidak terhubung. Jika ingin menghubungkan Ledger Anda, klik 'Lanjutkan' lagi dan setujui koneksi HID", "description": "An error message shown to the user during the hardware connect flow."}, "levelArrow": {"message": "panah tingkat"}, "lightTheme": {"message": "Terang"}, "likeToImportToken": {"message": "Ingin mengimpor token ini?"}, "likeToImportTokens": {"message": "<PERSON><PERSON><PERSON><PERSON> Anda ingin menambahkan token ini?"}, "lineaGoerli": {"message": "<PERSON><PERSON><PERSON> Goerli"}, "lineaMainnet": {"message": "Linea Mainnet"}, "lineaSepolia": {"message": "Jaringan uji Linea Sepolia"}, "link": {"message": "Tautan"}, "linkCentralizedExchanges": {"message": "Tautkan akun Coinbase atau Binance untuk mentransfer kripto ke NeoNix secara gratis."}, "links": {"message": "Tautan"}, "loadMore": {"message": "Muat lebih banyak"}, "loading": {"message": "Memuat..."}, "loadingScreenSnapMessage": {"message": "Selesaikan transaksi di Snap."}, "loadingTokenList": {"message": "Memuat daftar token"}, "localhost": {"message": "Localhost 8545"}, "lock": {"message": "<PERSON><PERSON><PERSON>"}, "lockNeoNix": {"message": "<PERSON><PERSON><PERSON>"}, "lockTimeInvalid": {"message": "<PERSON><PERSON><PERSON> kunci harus berupa angka antara 0 dan 10080"}, "logo": {"message": "Logo $1", "description": "$1 is the name of the ticker"}, "low": {"message": "Rendah"}, "lowEstimatedReturnTooltipMessage": {"message": "<PERSON><PERSON> akan membayar lebih dari $1% dari jumlah awal sebagai biaya. Periksa jumlah penerimaan dan biaya jaringan <PERSON>a."}, "lowEstimatedReturnTooltipTitle": {"message": "<PERSON><PERSON><PERSON> tinggi"}, "lowGasSettingToolTipMessage": {"message": "Gunakan $1 untuk menunggu harga yang lebih murah. Estimasi waktu kurang akurat karena harga sedang tidak dapat diprediksi.", "description": "$1 is key 'low' separated here so that it can be passed in with bold font-weight"}, "lowLowercase": {"message": "rendah"}, "mainnet": {"message": "Ethereum Mainnet"}, "mainnetToken": {"message": "Alamat ini cocok dengan alamat token Ethereum Mainnet yang dikenal. Periksa kembali alamat kontrak dan jaringan untuk token yang Anda coba tambahkan."}, "makeAnotherSwap": {"message": "Buat swap baru"}, "makeSureNoOneWatching": {"message": "Pastikan tidak ada yang melihat layar Anda", "description": "Warning to users to be care while creating and saving their new Secret Recovery Phrase"}, "manageDefaultSettings": {"message": "Kelola pengaturan privasi default"}, "manageInstitutionalWallets": {"message": "<PERSON><PERSON>la <PERSON>pet Institutional"}, "manageInstitutionalWalletsDescription": {"message": "Aktifkan ini untuk mengaktifkan dompet institutional."}, "manageNetworksMenuHeading": {"message": "<PERSON><PERSON><PERSON>"}, "managePermissions": {"message": "<PERSON><PERSON><PERSON>"}, "marketCap": {"message": "<PERSON><PERSON> pasar"}, "marketDetails": {"message": "Detail pasar"}, "max": {"message": "<PERSON><PERSON>"}, "maxBaseFee": {"message": "Biaya dasar maks"}, "maxFee": {"message": "Biaya maks"}, "maxFeeTooltip": {"message": "<PERSON><PERSON><PERSON> maksimum yang diberikan untuk membayar transaksi."}, "maxPriorityFee": {"message": "Biaya prioritas maks"}, "medium": {"message": "Pasar"}, "mediumGasSettingToolTipMessage": {"message": "Gunakan $1 untuk pemrosesan cepat dengan harga pasar saat ini.", "description": "$1 is key 'medium' (text: 'Market') separated here so that it can be passed in with bold font-weight"}, "memo": {"message": "memo"}, "message": {"message": "<PERSON><PERSON>"}, "NeoNixConnectStatusParagraphOne": {"message": "<PERSON><PERSON><PERSON>, Anda memiliki kontrol lebih banyak atas koneksi akun Anda di NeoNix."}, "NeoNixConnectStatusParagraphThree": {"message": "Klik untuk mengelola akun Anda yang terhubung."}, "NeoNixConnectStatusParagraphTwo": {"message": "Tombol status koneksi menunjukkan apakah situs web yang Anda kunjungi terhubung ke akun Anda yang dipilih saat ini."}, "metaMetricsIdNotAvailableError": {"message": "Karena Anda belum pernah ikut serta dalam MetaMetrics, tidak ada data yang dapat dihapus di sini."}, "metadataModalSourceTooltip": {"message": "$1 dihosting pada npm dan $2 merupakan pengenal unik Snap ini.", "description": "$1 is the snap name and $2 is the snap NPM id."}, "NeoNixNotificationsAreOff": {"message": "Saat ini notifikasi dompet tidak aktif."}, "NeoNixSwapsOfflineDescription": {"message": "NeoNix Swap sedang dalam pem<PERSON>. Harap periksa kembali nanti."}, "NeoNixVersion": {"message": "Versi NeoNix"}, "methodData": {"message": "Metode"}, "methodDataTransactionDesc": {"message": "Fungsi dijalankan berdasarkan data masukan yang didekodekan."}, "methodNotSupported": {"message": "Akun ini tidak mendukung."}, "metrics": {"message": "<PERSON><PERSON>"}, "millionAbbreviation": {"message": "Jt", "description": "Shortened form of 'million'"}, "mismatchedChainLinkText": {"message": "verifikasi detail jaringan", "description": "Serves as link text for the 'mismatched<PERSON><PERSON><PERSON>' key. This text will be embedded inside the translation for that key."}, "mismatchedChainRecommendation": {"message": "<PERSON><PERSON> agar Anda $1 sebelum melanjutkan.", "description": "$1 is a clickable link with text defined by the 'mismatchedChainLinkText' key. The link will open to instructions for users to validate custom network details."}, "mismatchedNetworkName": {"message": "Men<PERSON>t catatan kami, nama jaringan mungkin tidak sepenuhnya sesuai dengan ID chain ini."}, "mismatchedNetworkSymbol": {"message": "Simbol mata uang yang dikirimkan tidak sesuai dengan yang kami harapkan untuk ID chain ini."}, "mismatchedRpcChainId": {"message": "ID chain yang dipulihkan oleh jaringan khusus tidak cocok dengan ID chain yang dikirimkan."}, "mismatchedRpcUrl": {"message": "<PERSON><PERSON><PERSON> catatan kami, nilai URL RPC yang dikirimkan tidak sesuai dengan penyedia yang dikenal untuk ID chain ini."}, "missingSetting": {"message": "Tidak dapat menemukan pengaturan?"}, "missingSettingRequest": {"message": "Minta di sini"}, "more": {"message": "selengkapnya"}, "moreAccounts": {"message": "+ $1 akun lain", "description": "$1 is the number of accounts"}, "moreNetworks": {"message": "+ $1 jaringan lain", "description": "$1 is the number of networks"}, "moreQuotes": {"message": "<PERSON><PERSON><PERSON> la<PERSON>"}, "multichainAddEthereumChainConfirmationDescription": {"message": "Anda menambahkan jaringan ini ke NeoNix dan memberikan situs ini izin untuk menggunakannya."}, "multichainQuoteCardBridgingLabel": {"message": "Bridge"}, "multichainQuoteCardQuoteLabel": {"message": "<PERSON><PERSON><PERSON>"}, "multichainQuoteCardTimeLabel": {"message": "<PERSON><PERSON><PERSON>"}, "multipleSnapConnectionWarning": {"message": "$1 ingin menggunakan Snap $2", "description": "$1 is the dapp and $2 is the number of snaps it wants to connect to."}, "mustSelectOne": {"message": "<PERSON><PERSON> memilih minimal 1 token."}, "name": {"message": "<PERSON><PERSON>"}, "nameAddressLabel": {"message": "<PERSON><PERSON><PERSON>", "description": "Label above address field in name component modal."}, "nameAlreadyInUse": {"message": "<PERSON>a sudah digunakan"}, "nameInstructionsNew": {"message": "<PERSON><PERSON>a men<PERSON>ahui alamat ini, berikan nama panggilan agar dapat dikenali di kemudian hari.", "description": "Instruction text in name component modal when value is not recognised."}, "nameInstructionsRecognized": {"message": "<PERSON><PERSON><PERSON> ini memiliki nama panggilan default, tetapi <PERSON>a dapat mengeditnya atau menjelajahi saran lainnya.", "description": "Instruction text in name component modal when value is recognized but not saved."}, "nameInstructionsSaved": {"message": "Anda telah menambahkan nama panggilan untuk alamat ini sebelumnya. Anda dapat mengedit atau melihat nama panggilan lain yang disarankan.", "description": "Instruction text in name component modal when value is saved."}, "nameLabel": {"message": "<PERSON><PERSON>", "description": "Label above name input field in name component modal."}, "nameModalMaybeProposedName": {"message": "Mungkin: $1", "description": "$1 is the proposed name"}, "nameModalTitleNew": {"message": "<PERSON><PERSON><PERSON> tidak dikenal", "description": "Title of the modal created by the name component when value is not recognised."}, "nameModalTitleRecognized": {"message": "<PERSON><PERSON><PERSON> yang di<PERSON>", "description": "Title of the modal created by the name component when value is recognized but not saved."}, "nameModalTitleSaved": {"message": "<PERSON><PERSON><PERSON>", "description": "Title of the modal created by the name component when value is saved."}, "nameProviderProposedBy": {"message": "Diusulkan oleh $1", "description": "$1 is the name of the provider"}, "nameProvider_ens": {"message": "<PERSON><PERSON><PERSON> (ENS)"}, "nameProvider_etherscan": {"message": "Etherscan"}, "nameProvider_lens": {"message": "Lens Protocol"}, "nameProvider_token": {"message": "NeoNix"}, "nameSetPlaceholder": {"message": "<PERSON><PERSON>h nama panggilan...", "description": "Placeholder text for name input field in name component modal."}, "nativeNetworkPermissionRequestDescription": {"message": "$1 meminta perset<PERSON>juan <PERSON><PERSON> untuk:", "description": "$1 represents dapp name"}, "nativeTokenScamWarningConversion": {"message": "Edit detail jaringan"}, "nativeTokenScamWarningDescription": {"message": "Simbol token asli tidak cocok dengan simbol token asli yang diharapkan untuk jaringan dengan ID chain yang terkait. Anda telah memasukkan $1 sementara simbol token yang diharapkan adalah $2. Verifikasikan bahwa Anda terhubung ke chain yang benar.", "description": "$1 represents the currency name, $2 represents the expected currency symbol"}, "nativeTokenScamWarningDescriptionExpectedTokenFallback": {"message": "suatu hal lainnya", "description": "graceful fallback for when token symbol isn't found"}, "nativeTokenScamWarningTitle": {"message": "Simbol Token Asli yang Tak Terduga", "description": "Title for nativeTokenScamWarningDescription"}, "needHelp": {"message": "Butuh bantuan? Hubungi $1", "description": "$1 represents `needHelpLinkText`, the text which goes in the help link"}, "needHelpFeedback": {"message": "Bagikan umpan balik Anda"}, "needHelpLinkText": {"message": "Dukungan NeoNix"}, "needHelpSubmitTicket": {"message": "<PERSON><PERSON><PERSON> tiket"}, "needImportFile": {"message": "Anda harus memilih file untuk diimpor.", "description": "User is important an account and needs to add a file to continue"}, "negativeETH": {"message": "Tidak dapat mengirim jumlah negatif ETH."}, "negativeOrZeroAmountToken": {"message": "Tidak dapat mengirim aset dalam jumlah negatif atau nol."}, "network": {"message": "<PERSON><PERSON><PERSON>"}, "networkChanged": {"message": "<PERSON><PERSON><PERSON>"}, "networkChangedMessage": {"message": "Saat ini Anda bertransaksi pada $1.", "description": "$1 is the name of the network"}, "networkDetails": {"message": "<PERSON>ail jaringan"}, "networkFee": {"message": "<PERSON><PERSON><PERSON>"}, "networkIsBusy": {"message": "Jaringan sibuk. Harga gas tinggi dan estimasinya kurang akurat."}, "networkMenu": {"message": "<PERSON><PERSON>"}, "networkMenuHeading": {"message": "<PERSON><PERSON><PERSON>"}, "networkName": {"message": "<PERSON><PERSON>"}, "networkNameArbitrum": {"message": "Arbitrum"}, "networkNameAvalanche": {"message": "Avalanche"}, "networkNameBSC": {"message": "BSC"}, "networkNameBase": {"message": "<PERSON><PERSON>"}, "networkNameBitcoin": {"message": "Bitcoin"}, "networkNameDefinition": {"message": "<PERSON>a yang dikaitkan dengan jaringan ini."}, "networkNameEthereum": {"message": "Ethereum"}, "networkNameGoerli": {"message": "<PERSON><PERSON><PERSON>"}, "networkNameLinea": {"message": "Linea"}, "networkNameOpMainnet": {"message": "OP Mainnet"}, "networkNamePolygon": {"message": "Polygon"}, "networkNameSolana": {"message": "Solana"}, "networkNameTestnet": {"message": "Testnet"}, "networkNameZkSyncEra": {"message": "zkSync Era"}, "networkOptions": {"message": "<PERSON><PERSON> jar<PERSON>n"}, "networkPermissionToast": {"message": "<PERSON><PERSON>n <PERSON>"}, "networkProvider": {"message": "<PERSON><PERSON><PERSON>"}, "networkStatus": {"message": "Status jaringan"}, "networkStatusBaseFeeTooltip": {"message": "Biaya dasar diatur oleh jaringan dan berubah setiap 13-14 detik. Akun opsi $1 dan $2 kami untuk kenaikan mendadak.", "description": "$1 and $2 are bold text for Medium and Aggressive respectively."}, "networkStatusPriorityFeeTooltip": {"message": "Rentang biaya prioritas (alias “tip penambang”). Ini berlaku bagi penambang dan memberi insentif kepada mereka untuk memprioritaskan transaksi Anda."}, "networkStatusStabilityFeeTooltip": {"message": "Biaya gas relatif $1 dalam 72 jam terakhir.", "description": "$1 is networks stability value - stable, low, high"}, "networkSwitchConnectionError": {"message": "Kami tidak dapat terhubung ke $1", "description": "$1 represents the network name"}, "networkURL": {"message": "URL Jaringan"}, "networkURLDefinition": {"message": "URL yang digunakan untuk mengakses jaringan ini."}, "networkUrlErrorWarning": {"message": "Penyerang terkadang meniru situs dengan membuat perubahan kecil pada alamat situs. Pastikan Anda berinteraksi dengan situs yang dituju sebelum melanjutkan. Versi Punycode: $1", "description": "$1 replaced by RPC URL for network"}, "networks": {"message": "<PERSON><PERSON><PERSON>"}, "networksSmallCase": {"message": "<PERSON><PERSON><PERSON>"}, "nevermind": {"message": "Lupakan"}, "new": {"message": "Baru!"}, "newAccount": {"message": "<PERSON>kun baru"}, "newAccountNumberName": {"message": "Akun $1", "description": "Default name of next account to be created on create account screen"}, "newContact": {"message": "Kontak baru"}, "newContract": {"message": "Kontrak baru"}, "newNFTDetectedInImportNFTsMessageStrongText": {"message": "Pengaturan > <PERSON><PERSON><PERSON> dan privasi"}, "newNFTDetectedInImportNFTsMsg": {"message": "Untuk melihat NFT menggunakan OpenSea, aktifkan 'Tampilkan Media NFT' di $1.", "description": "$1 is used for newNFTDetectedInImportNFTsMessageStrongText"}, "newNFTDetectedInNFTsTabMessage": {"message": "Izinkan NeoNix mendeteksi dan menampilkan NFT di dompet secara otomatis."}, "newNFTsAutodetected": {"message": "Autodeteksi NFT"}, "newNetworkAdded": {"message": "“$1” ber<PERSON>il ditambahkan!"}, "newNetworkEdited": {"message": "“$1” ber<PERSON>il diedit!"}, "newNftAddedMessage": {"message": "NFT berhasil ditambahkan!"}, "newPassword": {"message": "<PERSON>a sandi baru (min 8 karakter)"}, "newPrivacyPolicyActionButton": {"message": "Baca selengkapnya"}, "newPrivacyPolicyTitle": {"message": "<PERSON><PERSON> telah memperbarui kebijakan privasi"}, "newRpcUrl": {"message": "URL RPC Baru"}, "newTokensImportedMessage": {"message": "<PERSON><PERSON> ber<PERSON><PERSON> mengimpor $1.", "description": "$1 is the string of symbols of all the tokens imported"}, "newTokensImportedTitle": {"message": "Token diimpor"}, "next": {"message": "Berikutnya"}, "nftAddFailedMessage": {"message": "NFT tidak dapat ditambahkan karena detail kepemilikan tidak cocok. Pastikan Anda telah memasukkan informasi yang benar."}, "nftAddressError": {"message": "Token ini merupakan NFT. Tambahkan ke $1", "description": "$1 is a clickable link with text defined by the 'importNFTPage' key"}, "nftAlreadyAdded": {"message": "NFT telah ditambahkan."}, "nftAutoDetectionEnabled": {"message": "Autodeteksi NFT diaktifkan"}, "nftDisclaimer": {"message": "Penafian: NeoNix mengambil file media dari url sumber. Url ini terkadang diubah oleh pasar tempat NFT dicetak."}, "nftOptions": {"message": "Opsi NFT"}, "nftTokenIdPlaceholder": {"message": "Masukkan id token"}, "nftWarningContent": {"message": "Anda memberikan akses ke $1, termasuk setiap akses yang Anda miliki di masa mendatang. <PERSON><PERSON> lain dapat mentransfer NFT ini dari dompet Anda kapan saja tanpa meminta izin Anda sampai Anda membatalkan persetujuan ini. $2", "description": "$1 is nftWarningContentBold bold part, $2 is Learn more link"}, "nftWarningContentBold": {"message": "semua NFT $1 Anda", "description": "$1 is name of the collection"}, "nftWarningContentGrey": {"message": "Lanjutkan dengan hati-hati."}, "nfts": {"message": "NFT"}, "nftsPreviouslyOwned": {"message": "Dimiliki Sebelumnya"}, "nickname": {"message": "<PERSON><PERSON>"}, "noAccountsFound": {"message": "Tidak ditemukan akun untuk kueri pencarian yang diberikan"}, "noActivity": {"message": "Belum ada aktivitas"}, "noConnectedAccountTitle": {"message": "NeoNix tidak terhubung ke situs ini"}, "noConnectionDescription": {"message": "Untuk terhubung ke situs, cari dan pilih tombol \"hubungkan\". Ingat NeoNix hanya dapat terhubung ke situs di web3"}, "noConversionRateAvailable": {"message": "<PERSON><PERSON> konversi tidak tersedia"}, "noDeFiPositions": {"message": "Belum ada posisi"}, "noDomainResolution": {"message": "Tidak ada resolusi untuk domain yang tersedia."}, "noHardwareWalletOrSnapsSupport": {"message": "<PERSON><PERSON>, dan sebagian besar dompet perangkat keras, tidak akan berfungsi dengan versi browser saat ini."}, "noNFTs": {"message": "Belum ada NFT"}, "noNetworksFound": {"message": "Tidak ditemukan jaringan untuk kueri pencarian yang diberikan"}, "noOptionsAvailableMessage": {"message": "Rute perdagangan ini tidak tersedia untuk saat ini. <PERSON><PERSON> ubah jumlah, jar<PERSON><PERSON>, atau <PERSON>, dan kami akan menemukan opsi terbaik."}, "noSnaps": {"message": "Belum ada snap yang diinstal."}, "noThanks": {"message": "Tidak, terima kasih"}, "noTransactions": {"message": "Anda tidak memiliki transaksi"}, "noWebcamFound": {"message": "Webcam komputer <PERSON><PERSON> tidak di<PERSON>n. Harap coba lagi."}, "noWebcamFoundTitle": {"message": "Webcam tidak ditemukan"}, "nonContractAddressAlertDesc": {"message": "Anda mengirim data panggilan ke alamat yang tidak terikat kontrak. Hal ini dapat menyebabkan Anda kehilangan dana. Pastikan untuk menggunakan alamat dan jaringan yang benar sebelum melanjutkan."}, "nonContractAddressAlertTitle": {"message": "<PERSON><PERSON><PERSON> k<PERSON>han"}, "nonce": {"message": "<PERSON><PERSON>"}, "none": {"message": "Tidak ada"}, "notBusy": {"message": "Tidak sibuk"}, "notCurrentAccount": {"message": "Apa ini akun yang benar? Ini berbeda dari akun yang saat ini dipilih di dompet Anda"}, "notEnoughBalance": {"message": "Saldo tidak cukup"}, "notEnoughGas": {"message": "Gas tidak cukup"}, "notNow": {"message": "<PERSON><PERSON>"}, "notificationDetail": {"message": "Detail"}, "notificationDetailBaseFee": {"message": "<PERSON><PERSON><PERSON> (GWEI)"}, "notificationDetailGasLimit": {"message": "Batas gas (unit)"}, "notificationDetailGasUsed": {"message": "Gas yang digunakan (unit)"}, "notificationDetailMaxFee": {"message": "Biaya maks per gas"}, "notificationDetailNetwork": {"message": "<PERSON><PERSON><PERSON>"}, "notificationDetailNetworkFee": {"message": "<PERSON><PERSON><PERSON>"}, "notificationDetailPriorityFee": {"message": "Biaya prioritas (GWEI)"}, "notificationItemCheckBlockExplorer": {"message": "Periksa di Block Explorer"}, "notificationItemCollection": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "notificationItemConfirmed": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "notificationItemError": {"message": "Saat ini biaya tidak dapat dipulihkan"}, "notificationItemFrom": {"message": "<PERSON><PERSON>"}, "notificationItemLidoStakeReadyToBeWithdrawn": {"message": "Penarikan Siap"}, "notificationItemLidoStakeReadyToBeWithdrawnMessage": {"message": "Kini Anda dapat menarik $1 yang belum di-stake"}, "notificationItemLidoWithdrawalRequestedMessage": {"message": "Permintaan untuk membatalkan stake $1 telah dikirim"}, "notificationItemNFTReceivedFrom": {"message": "Menerima NFT dari"}, "notificationItemNFTSentTo": {"message": "Mengirim NFT ke"}, "notificationItemNetwork": {"message": "<PERSON><PERSON><PERSON>"}, "notificationItemRate": {"message": "<PERSON><PERSON><PERSON> (termasuk biaya)"}, "notificationItemReceived": {"message": "Diterima"}, "notificationItemReceivedFrom": {"message": "<PERSON><PERSON><PERSON> dari"}, "notificationItemSent": {"message": "Terkirim"}, "notificationItemSentTo": {"message": "Terk<PERSON>m ke"}, "notificationItemStakeCompleted": {"message": "Stake selesai"}, "notificationItemStaked": {"message": "Di-stake"}, "notificationItemStakingProvider": {"message": "Penyedia Stake"}, "notificationItemStatus": {"message": "Status"}, "notificationItemSwapped": {"message": "<PERSON><PERSON><PERSON>"}, "notificationItemSwappedFor": {"message": "untuk"}, "notificationItemTo": {"message": "<PERSON>"}, "notificationItemTransactionId": {"message": "ID Transaksi"}, "notificationItemUnStakeCompleted": {"message": "Pembatalan Stake selesai"}, "notificationItemUnStaked": {"message": "<PERSON><PERSON>"}, "notificationItemUnStakingRequested": {"message": "Permintaan pembatalan stake"}, "notificationTransactionFailedMessage": {"message": "Transaksi $1 gagal! $2", "description": "Content of the browser notification that appears when a transaction fails"}, "notificationTransactionFailedTitle": {"message": "<PERSON><PERSON><PERSON> gagal", "description": "Title of the browser notification that appears when a transaction fails"}, "notificationTransactionSuccessMessage": {"message": "Transaksi $1 dikonfirmasi!", "description": "Content of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessTitle": {"message": "Transaksi terkonfirmasi", "description": "Title of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessView": {"message": "Lihat di $1", "description": "Additional content in a notification that appears when a transaction is confirmed and has a block explorer URL."}, "notifications": {"message": "Notif<PERSON><PERSON>"}, "notificationsFeatureToggle": {"message": "Aktifkan Notifikasi Dompet", "description": "Experimental feature title"}, "notificationsFeatureToggleDescription": {"message": "Ini mengaktifkan notifikasi dompet seperti mengirim/menerima dana atau nft dan pengumuman fitur.", "description": "Description of the experimental notifications feature"}, "notificationsMarkAllAsRead": {"message": "Tandai semua telah dibaca"}, "notificationsPageEmptyTitle": {"message": "Tak ada apa pun di sini"}, "notificationsPageErrorContent": {"message": "Coba kunjungi halaman ini lagi."}, "notificationsPageErrorTitle": {"message": "<PERSON><PERSON> terjadi k<PERSON>han"}, "notificationsPageNoNotificationsContent": {"message": "Anda belum menerima notifika<PERSON>."}, "notificationsSettingsBoxError": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>. Coba lagi."}, "notificationsSettingsPageAllowNotifications": {"message": "Pantau terus segala yang terjadi di dompet Anda dengan notifikasi. Untuk menggunakan notifikasi, kami menggunakan profil untuk menyinkronkan beberapa pengaturan di seluruh perangkat Anda. $1"}, "notificationsSettingsPageAllowNotificationsLink": {"message": "<PERSON><PERSON><PERSON>i cara kami melindungi privasi Anda saat menggunakan fitur ini."}, "numberOfNewTokensDetectedPlural": {"message": "$1 token baru ditemukan di akun ini", "description": "$1 is the number of new tokens detected"}, "numberOfNewTokensDetectedSingular": {"message": "1 token baru ditemukan di akun ini"}, "numberOfTokens": {"message": "<PERSON><PERSON><PERSON>"}, "ofTextNofM": {"message": "dari"}, "off": {"message": "<PERSON><PERSON>"}, "offlineForMaintenance": {"message": "Offline untuk pemeliharaan"}, "ok": {"message": "<PERSON>e"}, "on": {"message": "<PERSON><PERSON><PERSON>"}, "onboardedMetametricsAccept": {"message": "<PERSON><PERSON>"}, "onboardedMetametricsDisagree": {"message": "Tidak, terima kasih"}, "onboardedMetametricsKey1": {"message": "Perkembangan terkini"}, "onboardedMetametricsKey2": {"message": "<PERSON>tur produk"}, "onboardedMetametricsKey3": {"message": "<PERSON><PERSON> promosi lain yang relevan"}, "onboardedMetametricsLink": {"message": "MetaMetrics"}, "onboardedMetametricsParagraph1": {"message": "Selain $1, kami ingin menggunakan data untuk memahami cara Anda berinteraksi dengan komunikasi pemasaran.", "description": "$1 represents the 'onboardedMetametricsLink' locale string"}, "onboardedMetametricsParagraph2": {"message": "Ini membantu kami mempersonalisasi hal yang kami bagikan kepada <PERSON>, se<PERSON>i:"}, "onboardedMetametricsParagraph3": {"message": "Ingat, kami tidak pernah menjual data yang Anda berikan dan Anda dapat memilih untuk keluar setiap saat."}, "onboardedMetametricsTitle": {"message": "Bantu kami meningkatkan pengalaman Anda"}, "onboardingAdvancedPrivacyIPFSDescription": {"message": "Gateway IPFS memungkinkan untuk mengakses dan melihat data yang disimpan oleh pihak ketiga. Anda dapat menambahkan gateway IPFS khusus atau melanjutkan menggunakan default."}, "onboardingAdvancedPrivacyIPFSInvalid": {"message": "Masukkan URL yang valid"}, "onboardingAdvancedPrivacyIPFSTitle": {"message": "Tambahkan Gateway IPFS khusus"}, "onboardingAdvancedPrivacyIPFSValid": {"message": "URL gateway IPFS valid"}, "onboardingAdvancedPrivacyNetworkDescription": {"message": "Bila Anda menggunakan pengaturan dan konfigurasi default, kami menggunakan Infura sebagai penyedia panggilan prosedur jarak jauh (RPC) default kami untuk menawarkan akses paling andal dan pribadi ke data Ethereum yang kami bisa. <PERSON><PERSON> kasus terbatas, kami dapat menggunakan penyedia RPC lain untuk memberikan pengalaman terbaik bagi pengguna kami. Anda dapat memilih RPC sendiri, tetapi ingatlah bahwa setiap RPC akan menerima alamat IP dan dompet Ethereum Anda untuk melakukan transaksi. Untuk mempelajari selengkapnya tentang cara Infura menangani data untuk akun EVM, baca $1; untuk akun Solana, $2."}, "onboardingAdvancedPrivacyNetworkDescriptionCallToAction": {"message": "klik di sini"}, "onboardingAdvancedPrivacyNetworkTitle": {"message": "<PERSON><PERSON><PERSON>"}, "onboardingCreateWallet": {"message": "Buat dompet baru"}, "onboardingImportWallet": {"message": "I<PERSON>r dompet yang ada"}, "onboardingMetametricsAgree": {"message": "<PERSON><PERSON>"}, "onboardingMetametricsDescription": {"message": "<PERSON>mi ingin mengumpulkan data penggunaan dasar dan diagnostik untuk meningkatkan NeoNix. <PERSON><PERSON>i bahwa kami tidak pernah menjual data yang Anda berikan di sini."}, "onboardingMetametricsInfuraTerms": {"message": "Kami akan memberitahukan keputusan untuk menggunakan data ini dengan tujuan lain. Anda dapat meninjau $1 kami untuk informasi selengkapnya. Ingat, Anda dapat membuka pengaturan dan memilih keluar setiap saat.", "description": "$1 represents `onboardingMetametricsInfuraTermsPolicy`"}, "onboardingMetametricsInfuraTermsPolicy": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "onboardingMetametricsNeverCollect": {"message": "$1 klik dan tampilan pada aplikasi disimpan, tetapi tidak untuk detail lainnya (seperti alamat publik).", "description": "$1 represents `onboardingMetametricsNeverCollectEmphasis`"}, "onboardingMetametricsNeverCollectEmphasis": {"message": "Pribadi:"}, "onboardingMetametricsNeverCollectIP": {"message": "$1 kami menggunakan alamat IP sementara waktu untuk mendeteksi lokasi umum (seperti negara atau wilayah Anda), tetapi alamat tersebut tidak pernah disimpan.", "description": "$1 represents `onboardingMetametricsNeverCollectIPEmphasis`"}, "onboardingMetametricsNeverCollectIPEmphasis": {"message": "Umum:"}, "onboardingMetametricsNeverSellData": {"message": "$1 memutuskan jika Anda ingin membagikan atau menghapus data penggunaan melalui pengaturan setiap saat.", "description": "$1 represents `onboardingMetametricsNeverSellDataEmphasis`"}, "onboardingMetametricsNeverSellDataEmphasis": {"message": "Opsional:"}, "onboardingMetametricsTitle": {"message": "Bantu kami meningkatkan NeoNix"}, "onboardingMetametricsUseDataCheckbox": {"message": "<PERSON>mi akan menggunakan data ini untuk mempelajari cara Anda berinteraksi dengan komunikasi pemasaran. <PERSON><PERSON> mungkin akan membagikan berita yang relevan (seperti fitur produk)."}, "onboardingPinExtensionDescription": {"message": "Sematkan NeoNix pada browser <PERSON><PERSON> agar dapat diakses dan memudahkan dalam melihat konfirmasi transaksi."}, "onboardingPinExtensionDescription2": {"message": "Anda dapat membuka NeoNix dengan mengeklik ekstensi dan mengakses dompet Anda dalam 1 klik."}, "onboardingPinExtensionDescription3": {"message": "Klik ikon ekstensi browser untuk mengaksesnya secara langsung", "description": "$1 is the browser name"}, "onboardingPinExtensionTitle": {"message": "Pemasangan NeoNix Anda se<PERSON>ai!"}, "onekey": {"message": "OneKey"}, "only": {"message": "hanya"}, "onlyConnectTrust": {"message": "<PERSON>ya hubungkan ke situs yang Anda percayai. $1", "description": "Text displayed above the buttons for connection confirmation. $1 is the link to the learn more web page."}, "openFullScreenForLedgerWebHid": {"message": "<PERSON>uka layar penuh untuk menghubungkan Ledger Anda.", "description": "Shown to the user on the confirm screen when they are viewing NeoNix in a popup window but need to connect their ledger via webhid."}, "openInBlockExplorer": {"message": "Buka di block explorer"}, "optional": {"message": "Opsional"}, "options": {"message": "Opsi"}, "origin": {"message": "<PERSON><PERSON>"}, "originChanged": {"message": "<PERSON><PERSON> di<PERSON>h"}, "originChangedMessage": {"message": "Saat ini Anda sedang meninjau permintaan dari $1.", "description": "$1 is the name of the origin"}, "osTheme": {"message": "Sistem"}, "other": {"message": "la<PERSON><PERSON>"}, "otherSnaps": {"message": "snap lainnya", "description": "Used in the 'permission_rpc' message."}, "others": {"message": "la<PERSON><PERSON>"}, "outdatedBrowserNotification": {"message": "Browser <PERSON><PERSON> sudah usang. Jika browser tidak diper<PERSON><PERSON>, <PERSON><PERSON> tidak akan bisa mendapatkan tambalan keamanan dan fitur baru dari NeoNix."}, "overrideContentSecurityPolicyHeader": {"message": "Batalkan header Content-Security-Policy"}, "overrideContentSecurityPolicyHeaderDescription": {"message": "Opsi ini merupakan solusi sementara untuk masalah yang diketahui di Firefox, di mana header Content-Security-Policy dapp dapat mencegah ekstensi dimuat dengan benar. Menonaktifkan opsi ini tidak disarankan kecuali diperlukan untuk kompatibilitas halaman web tertentu."}, "padlock": {"message": "Gembok"}, "participateInMetaMetrics": {"message": "Berpartisipasilah dalam MetaMetrics"}, "participateInMetaMetricsDescription": {"message": "Berpartisipasilah dalam MetaMetrics untuk membantu kami menjadikan NeoNix lebih baik lagi"}, "password": {"message": "<PERSON>a sandi"}, "passwordNotLongEnough": {"message": "Kata sandi kurang panjang"}, "passwordStrength": {"message": "Kekuatan kata sandi: $1", "description": "Return password strength to the user when user wants to create password."}, "passwordStrengthDescription": {"message": "Kata sandi yang kuat dapat meningkatkan keamanan dompet jika perangkat Anda dicuri atau disusupi."}, "passwordTermsWarning": {"message": "<PERSON><PERSON> memahami bahwa NeoNix tidak dapat memulihkan kata sandi ini untuk saya. $1"}, "passwordsDontMatch": {"message": "Kata sandi tidak cocok"}, "pastePrivateKey": {"message": "Tempel string kunci pribadi Anda di sini:", "description": "For importing an account from a private key"}, "pending": {"message": "<PERSON><PERSON><PERSON>"}, "pendingConfirmationAddNetworkAlertMessage": {"message": "Memperbarui jaringan akan membatalkan $1 transaksi tertunda dari situs ini.", "description": "Number of transactions."}, "pendingConfirmationSwitchNetworkAlertMessage": {"message": "<PERSON><PERSON><PERSON> jaringan akan membatalkan $1 transaksi tertunda dari situs ini.", "description": "Number of transactions."}, "pendingTransactionAlertMessage": {"message": "Transaksi ini tidak akan berhasil sampai transaksi sebelumnya selesai. $1", "description": "$1 represents the words 'how to cancel or speed up a transaction' in a hyperlink"}, "pendingTransactionAlertMessageHyperlink": {"message": "<PERSON><PERSON><PERSON><PERSON> cara membatalkan atau mempercepat transaksi.", "description": "The text for the hyperlink in the pending transaction alert message"}, "permissionDetails": {"message": "Detail izin"}, "permissionFor": {"message": "<PERSON><PERSON> untuk"}, "permissionFrom": {"message": "<PERSON><PERSON> dari"}, "permissionRequested": {"message": "<PERSON><PERSON><PERSON>"}, "permissionRequestedForAccounts": {"message": "Diminta sekarang untuk $1", "description": "Permission cell status for requested permission including accounts, rendered as AvatarGroup which is $1."}, "permissionRevoked": {"message": "Dicabut dalam pembaruan ini"}, "permissionRevokedForAccounts": {"message": "Dicabut dalam pembaruan ini untuk $1", "description": "Permission cell status for revoked permission including accounts, rendered as AvatarGroup which is $1."}, "permission_accessNamedSnap": {"message": "Hubungkan ke $1.", "description": "The description for the `wallet_snap` permission. $1 is the human-readable name of the snap."}, "permission_accessNetwork": {"message": "Akses internet.", "description": "The description of the `endowment:network-access` permission."}, "permission_accessNetworkDescription": {"message": "Izinkan $1 untuk mengakses internet. Ini dapat digunakan untuk mengirim dan menerima data dari server pihak ketiga.", "description": "An extended description of the `endowment:network-access` permission. $1 is the snap name."}, "permission_accessSnap": {"message": "Hubungkan ke Snap $1.", "description": "The description for the `wallet_snap` permission. $1 is the name of the snap."}, "permission_accessSnapDescription": {"message": "Izinkan situs web atau snap untuk berinteraksi dengan $1.", "description": "The description for the `wallet_snap_*` permission. $1 is the name of the Snap."}, "permission_assets": {"message": "Menampilkan aset akun di NeoNix.", "description": "The description for the `endowment:assets` permission."}, "permission_assetsDescription": {"message": "Izinkan $1 untuk memberikan informasi aset kepada klien NeoNix. Aset dapat berupa onchain atau offchain.", "description": "An extended description for the `endowment:assets` permission. $1 is the name of the Snap."}, "permission_cronjob": {"message": "<PERSON><PERSON><PERSON><PERSON> dan lakukan tindakan berkala.", "description": "The description for the `snap_cronjob` permission"}, "permission_cronjobDescription": {"message": "Izinkan $1 untuk melakukan tindakan yang diatur secara berkala pada waktu, tanggal, atau interval yang tetap. Ini dapat digunakan untuk memicu interaksi atau notifikasi yang peka terhadap waktu.", "description": "An extended description for the `snap_cronjob` permission. $1 is the snap name."}, "permission_dialog": {"message": "<PERSON><PERSON><PERSON><PERSON> jendela dialog di NeoNix.", "description": "The description for the `snap_dialog` permission"}, "permission_dialogDescription": {"message": "Izinkan $1 untuk menampilkan sembulan NeoNix yang berisi teks khusus, kolom input, dan tombol untuk menyetujui atau menolak suatu tindakan.\nDapat digunakan untuk membuat peringatan, k<PERSON><PERSON><PERSON><PERSON>, dan alur keikutsertaan untuk Snap.", "description": "An extended description for the `snap_dialog` permission. $1 is the snap name."}, "permission_ethereumAccounts": {"message": "<PERSON><PERSON>, saldo a<PERSON>, aktiv<PERSON>, dan mulai <PERSON>i", "description": "The description for the `eth_accounts` permission"}, "permission_ethereumProvider": {"message": "Akses penyedia Ethereum.", "description": "The description for the `endowment:ethereum-provider` permission"}, "permission_ethereumProviderDescription": {"message": "Izinkan $1 untuk berkomunikasi dengan NeoNix secara langsung, agar dapat membaca data dari blockchain serta menyarankan pesan dan transaksi.", "description": "An extended description for the `endowment:ethereum-provider` permission. $1 is the snap name."}, "permission_getEntropy": {"message": "Dapatkan kunci arbitrer unik untuk $1.", "description": "The description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getEntropyDescription": {"message": "Izinkan $1 untuk mendapatkan kunci arbitrer unik untuk $1 tanpa mengeksposnya. Kunci ini terpisah dari akun NeoNix dan tidak terkait dengan kunci pribadi atau Frasa Pemulihan Rahasia. Snap lain tidak dapat mengakses informasi ini.", "description": "An extended description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getLocale": {"message": "<PERSON><PERSON> bahasa pilihan <PERSON>.", "description": "The description for the `snap_getLocale` permission"}, "permission_getLocaleDescription": {"message": "Izinkan $1 mengakses bahasa pilihan Anda dari pengaturan NeoNix. Ini dapat digunakan untuk melokalisasi dan menampilkan konten $1 menggunakan bahasa Anda.", "description": "An extended description for the `snap_getLocale` permission. $1 is the snap name."}, "permission_getPreferences": {"message": "Lihat informasi seperti bahasa pilihan Anda dan mata uang fiat.", "description": "The description for the `snap_getPreferences` permission"}, "permission_getPreferencesDescription": {"message": "Izinkan $1 mengakses informasi seperti bahasa pilihan dan mata uang fiat di pengaturan NeoNix Anda. Ini membantu $1 menampilkan konten yang disesuaikan dengan preferensi Anda. ", "description": "An extended description for the `snap_getPreferences` permission. $1 is the snap name."}, "permission_homePage": {"message": "<PERSON><PERSON><PERSON><PERSON> layar khusus", "description": "The description for the `endowment:page-home` permission"}, "permission_homePageDescription": {"message": "Izinkan $1 menampilkan layar beranda khusus di NeoNix. Ini dapat digunakan untuk antarmuka pengguna, k<PERSON><PERSON><PERSON><PERSON><PERSON>, dan das<PERSON>.", "description": "An extended description for the `endowment:page-home` permission. $1 is the snap name."}, "permission_keyring": {"message": "Izinkan permintaan untuk menambah dan mengontrol akun Ethereum", "description": "The description for the `endowment:keyring` permission"}, "permission_keyringDescription": {"message": "Izinkan $1 menerima permintaan untuk menambah atau menghapus akun, serta menandatangani dan bertransaksi atas nama akun tersebut.", "description": "An extended description for the `endowment:keyring` permission. $1 is the snap name."}, "permission_lifecycleHooks": {"message": "Gunakan lifecycle hook.", "description": "The description for the `endowment:lifecycle-hooks` permission"}, "permission_lifecycleHooksDescription": {"message": "Izinkan $1 menggunakan lifecycle hook untuk menjalankan kode pada waktu tertentu selama siklus hidupnya.", "description": "An extended description for the `endowment:lifecycle-hooks` permission. $1 is the snap name."}, "permission_manageAccounts": {"message": "Tambah dan kontrol akun Ethereum", "description": "The description for `snap_manageAccounts` permission"}, "permission_manageAccountsDescription": {"message": "Izinkan $1 untuk menambah atau menghapus akun Ethereum, lalu bertransaksi dan menandatangani menggunakan akun ini.", "description": "An extended description for the `snap_manageAccounts` permission. $1 is the snap name."}, "permission_manageBip32Keys": {"message": "Kelola akun $1.", "description": "The description for the `snap_getBip32Entropy` permission. $1 is a derivation path, e.g. 'm/44'/0'/0' (secp256k1)'."}, "permission_manageBip44AndBip32KeysDescription": {"message": "Izinkan $1 untuk mengelola akun dan aset pada jaringan yang diminta. Akun-akun ini diperoleh dan dicadangkan menggunakan frasa pemulihan rahasia (tanpa mengungkapkannya). Dengan kemampuannya untuk mendapatkan kunci, $1 dapat mendukung berbagai protokol blockchain di luar Ethereum (EVM).", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions. $1 is the snap name."}, "permission_manageBip44Keys": {"message": "Kelola akun $1.", "description": "The description for the `snap_getBip44Entropy` permission. $1 is the name of a protocol, e.g. 'Filecoin'."}, "permission_manageState": {"message": "Simpan dan kelola datanya di perangkat Anda.", "description": "The description for the `snap_manageState` permission"}, "permission_manageStateDescription": {"message": "Izinkan $1 untuk menyi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan memulihkan data secara aman dengan enkripsi. Snap lain tidak dapat mengakses informasi ini.", "description": "An extended description for the `snap_manageState` permission. $1 is the snap name."}, "permission_nameLookup": {"message": "Menyediakan pencarian domain dan alamat.", "description": "The description for the `endowment:name-lookup` permission."}, "permission_nameLookupDescription": {"message": "Izinkan Snap untuk mengambil dan menampilkan pencarian alamat dan domain pada bagian UI NeoNix yang berbeda.", "description": "An extended description for the `endowment:name-lookup` permission."}, "permission_notifications": {"message": "Tampilkan pemberitahuan.", "description": "The description for the `snap_notify` permission"}, "permission_notificationsDescription": {"message": "Izinkan $1 untuk menampilkan notifikasi dalam NeoNix. Teks notifikasi singkat dapat dipicu oleh Snap untuk informasi yang dapat ditindaklanjuti atau peka terhadap waktu.", "description": "An extended description for the `snap_notify` permission. $1 is the snap name."}, "permission_protocol": {"message": "Memberikan data protokol untuk satu chain atau lebih.", "description": "The description for the `endowment:protocol` permission."}, "permission_protocolDescription": {"message": "Izinkan $1 untuk memberikan data protokol kepada NeoNix seperti estimasi gas atau informasi token.", "description": "An extended description for the `endowment:protocol` permission. $1 is the name of the Snap."}, "permission_rpc": {"message": "Izinkan $1 untuk terhubung secara langsung dengan $2.", "description": "The description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescription": {"message": "Izinkan $1 untuk mengirim pesan ke $2 dan menerima tanggapan dari $2.", "description": "An extended description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescriptionOriginList": {"message": "$1 dan $2", "description": "A list of allowed origins where $2 is the last origin of the list and $1 is the rest of the list separated by ','."}, "permission_signatureInsight": {"message": "<PERSON><PERSON><PERSON><PERSON> modal wawasan tanda tangan.", "description": "The description for the `endowment:signature-insight` permission"}, "permission_signatureInsightDescription": {"message": "Izinkan $1 untuk menampilkan modal dengan wawasan seputar permintaan tanda tangan sebelum disetujui. Ini dapat digunakan sebagai solusi anti pengelabuan dan keamanan.", "description": "An extended description for the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_signatureInsightOrigin": {"message": "Lihat asal situs web yang memulai permintaan tanda tangan", "description": "The description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission"}, "permission_signatureInsightOriginDescription": {"message": "Izinkan $1 untuk melihat asal (URI) situs web yang memulai permintaan tanda tangan. Ini dapat digunakan sebagai solusi anti pengelabuan dan keamanan.", "description": "An extended description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_transactionInsight": {"message": "Dapatkan dan tampilkan wawasan transaksi.", "description": "The description for the `endowment:transaction-insight` permission"}, "permission_transactionInsightDescription": {"message": "Izinkan $1 untuk membaca kode transaksi dan menampilkan wawasan dalam UI NeoNix. Ini dapat digunakan sebagai solusi anti pengelabuan dan keamanan.", "description": "An extended description for the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_transactionInsightOrigin": {"message": "Lihat asal situs web yang mengirimkan transaksi", "description": "The description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission"}, "permission_transactionInsightOriginDescription": {"message": "Izinkan $1 untuk melihat asal (URI) situs web yang menyarankan transaksi. Ini dapat digunakan sebagai solusi anti pengelabuan dan keamanan.", "description": "An extended description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_unknown": {"message": "Izin tidak dikenal: $1", "description": "$1 is the name of a requested permission that is not recognized."}, "permission_viewBip32PublicKeys": {"message": "Lihat kunci publik Anda untuk $1 ($2).", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a derivation path, e.g. 'm/44'/0'/0''. $2 is the elliptic curve name, e.g. 'secp256k1'."}, "permission_viewBip32PublicKeysDescription": {"message": "Izinkan $2 untuk melihat kunci publik (dan al<PERSON>t) untuk $1. Ini tidak memberi kendali atas akun atau aset.", "description": "An extended description for the `snap_getBip32PublicKey` permission. $1 is a derivation path (name). $2 is the snap name."}, "permission_viewNamedBip32PublicKeys": {"message": "Lihat kunci publik Anda untuk $1.", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a name for the derivation path, e.g., 'Ethereum accounts'."}, "permission_walletSwitchEthereumChain": {"message": "<PERSON><PERSON><PERSON> jaringan yang diak<PERSON>kan", "description": "The label for the `wallet_switchEthereumChain` permission"}, "permission_webAssembly": {"message": "Mendukung WebAssembly.", "description": "The description of the `endowment:webassembly` permission."}, "permission_webAssemblyDescription": {"message": "Izinkan $1 untuk mengakses lingkungan pelaksanaan tingkat rendah melalui WebAssembly.", "description": "An extended description of the `endowment:webassembly` permission. $1 is the snap name."}, "permissions": {"message": "<PERSON><PERSON>"}, "permissionsPageEmptyContent": {"message": "Tak ada yang bisa dilihat di sini"}, "permissionsPageEmptySubContent": {"message": "<PERSON> sinilah Anda dapat melihat izin yang Anda berikan untuk Snap yang terinstal atau situs yang terhubung."}, "permitSimulationChange_approve": {"message": "<PERSON><PERSON>"}, "permitSimulationChange_bidding": {"message": "<PERSON><PERSON>"}, "permitSimulationChange_listing": {"message": "<PERSON><PERSON> men<PERSON>"}, "permitSimulationChange_nft_listing": {"message": "<PERSON>rga yang terdaftar"}, "permitSimulationChange_receive": {"message": "<PERSON><PERSON>"}, "permitSimulationChange_revoke2": {"message": "<PERSON><PERSON><PERSON>"}, "permitSimulationChange_transfer": {"message": "<PERSON><PERSON>"}, "permitSimulationDetailInfo": {"message": "Anda memberikan izin kepada pengguna untuk menggunakan token sebanyak ini dari akun."}, "permittedChainToastUpdate": {"message": "$1 memiliki akses ke $2."}, "personalAddressDetected": {"message": "Alamat pribadi terdeteksi. <PERSON><PERSON><PERSON>n alamat kontrak token."}, "pinToTop": {"message": "Sematkan ke atas"}, "pleaseConfirm": {"message": "<PERSON><PERSON>"}, "plusMore": {"message": "+ $1 lagi", "description": "$1 is the number of additional items"}, "plusXMore": {"message": "+ $1 lagi", "description": "$1 is a number of additional but unshown items in a list- this message will be shown in place of those items"}, "popularNetworkAddToolTip": {"message": "Beberapa jaringan ini mengandalkan pihak ketiga. Koneksinya mungkin kurang dapat diandalkan atau memungkinkan pihak ketiga untuk melacak aktivitas.", "description": "Learn more link"}, "popularNetworks": {"message": "Jaringan populer"}, "portfolio": {"message": "Portofolio"}, "preparingSwap": {"message": "Mempersiapkan pertukaran..."}, "prev": {"message": "Sebelumnya"}, "price": {"message": "<PERSON><PERSON>"}, "priceUnavailable": {"message": "harga tidak tersedia"}, "primaryType": {"message": "Tipe primer"}, "priorityFee": {"message": "Biaya prioritas"}, "priorityFeeProperCase": {"message": "Biaya Prioritas"}, "privacy": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "privacyMsg": {"message": "<PERSON><PERSON><PERSON><PERSON> privasi"}, "privateKey": {"message": "Kebijakan Pribadi", "description": "select this type of file to use to import an account"}, "privateKeyCopyWarning": {"message": "Kunci pribadi untuk $1", "description": "$1 represents the account name"}, "privateKeyHidden": {"message": "Kunci pribadi disembunyikan", "description": "Explains that the private key input is hidden"}, "privateKeyShow": {"message": "Tam<PERSON><PERSON>an/Sembunyikan input kunci pribadi", "description": "Describes a toggle that is used to show or hide the private key input"}, "privateKeyShown": {"message": "Kunci pribadi ini sedang ditampilkan", "description": "Explains that the private key input is being shown"}, "privateKeyWarning": {"message": "Peringatan: <PERSON><PERSON> ung<PERSON>kan kunci ini. Siapa pun yang memiliki kunci pribadi Anda dapat mencuri aset yang disimpan di akun Anda."}, "privateNetwork": {"message": "<PERSON><PERSON><PERSON>"}, "proceedWithTransaction": {"message": "<PERSON>a tetap ingin melanju<PERSON>kan"}, "productAnnouncements": {"message": "<PERSON><PERSON><PERSON> produk"}, "proposedApprovalLimit": {"message": "<PERSON><PERSON> per<PERSON> yang di<PERSON>ukan"}, "provide": {"message": "Berikan"}, "publicAddress": {"message": "<PERSON><PERSON><PERSON> publik"}, "pushPlatformNotificationsFundsReceivedDescription": {"message": "<PERSON>a <PERSON> $1 $2"}, "pushPlatformNotificationsFundsReceivedDescriptionDefault": {"message": "<PERSON>a men<PERSON> be<PERSON>"}, "pushPlatformNotificationsFundsReceivedTitle": {"message": "<PERSON>"}, "pushPlatformNotificationsFundsSentDescription": {"message": "<PERSON><PERSON> be<PERSON><PERSON><PERSON> men<PERSON>kan $1 $2"}, "pushPlatformNotificationsFundsSentDescriptionDefault": {"message": "<PERSON><PERSON> be<PERSON><PERSON><PERSON> men<PERSON> beberapa <PERSON>"}, "pushPlatformNotificationsFundsSentTitle": {"message": "<PERSON> terkirim"}, "pushPlatformNotificationsNftReceivedDescription": {"message": "<PERSON><PERSON> menerima NFT baru"}, "pushPlatformNotificationsNftReceivedTitle": {"message": "NFT diterima"}, "pushPlatformNotificationsNftSentDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>mkan NFT"}, "pushPlatformNotificationsNftSentTitle": {"message": "NFT terkirim"}, "pushPlatformNotificationsStakingLidoStakeCompletedDescription": {"message": "Stake <PERSON> be<PERSON>"}, "pushPlatformNotificationsStakingLidoStakeCompletedTitle": {"message": "Stake selesai"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnDescription": {"message": "Stake Lido kini siap untuk ditarik"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnTitle": {"message": "Stake siap untuk penarikan"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedDescription": {"message": "Penarikan <PERSON>"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedTitle": {"message": "Penarikan se<PERSON>ai"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedDescription": {"message": "Permintaan penarikan Lido telah di<PERSON>rim"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedTitle": {"message": "<PERSON><PERSON><PERSON> diminta"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedDescription": {"message": "Stake RocketPool berhasil"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedTitle": {"message": "Stake selesai"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedDescription": {"message": "Pembatalan stake RocketPool berhasil"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedTitle": {"message": "Pembatalan stake selesai"}, "pushPlatformNotificationsSwapCompletedDescription": {"message": "Swap NeoNix berhasil"}, "pushPlatformNotificationsSwapCompletedTitle": {"message": "<PERSON><PERSON><PERSON>"}, "queued": {"message": "<PERSON><PERSON><PERSON>"}, "quoteRate": {"message": "Tingkat kuotasi"}, "quotedReceiveAmount": {"message": "Jumlah $1 yang diterima"}, "quotedTotalCost": {"message": "Biaya total $1"}, "rank": {"message": "<PERSON><PERSON><PERSON>"}, "rateIncludesMMFee": {"message": "<PERSON><PERSON><PERSON> sudah termasuk biaya sebesar $1%"}, "reAddAccounts": {"message": "tambahkan kembali akun lain"}, "reAdded": {"message": "ditambahkan kembali"}, "readdToken": {"message": "Anda dapat menambahkan token ini kembali di masa mendatang dengan membuka “Impor token” di menu opsi akun Anda."}, "receive": {"message": "Terima"}, "receiveCrypto": {"message": "Te<PERSON> kripto"}, "recipientAddressPlaceholderNew": {"message": "Ma<PERSON>kkan alamat publik (0x) atau nama domain"}, "recommendedGasLabel": {"message": "Di<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "recoveryPhraseReminderBackupStart": {"message": "<PERSON><PERSON> di sini"}, "recoveryPhraseReminderConfirm": {"message": "<PERSON><PERSON><PERSON>"}, "recoveryPhraseReminderHasBackedUp": {"message": "<PERSON><PERSON> se<PERSON>u <PERSON>a di tempat yang aman dan rahasia"}, "recoveryPhraseReminderHasNotBackedUp": {"message": "Perlu mencadangkan Frasa Pemulihan Rahasia Anda lagi?"}, "recoveryPhraseReminderItemOne": {"message": "<PERSON><PERSON> memba<PERSON>a kepada siapa pun"}, "recoveryPhraseReminderItemTwo": {"message": "<PERSON> tidak akan pernah meminta Frasa P<PERSON>"}, "recoveryPhraseReminderSubText": {"message": "<PERSON><PERSON>a mengendalikan semua akun <PERSON>."}, "recoveryPhraseReminderTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> dana <PERSON>"}, "redeposit": {"message": "<PERSON><PERSON><PERSON><PERSON> ulang"}, "refreshList": {"message": "Segarkan daftar"}, "reject": {"message": "<PERSON><PERSON>"}, "rejectAll": {"message": "<PERSON><PERSON> se<PERSON>a"}, "rejectRequestsDescription": {"message": "<PERSON><PERSON> akan menolak permintaan $1 secara bertahap."}, "rejectRequestsN": {"message": "Tolak permintaan $1"}, "rejectTxsDescription": {"message": "<PERSON><PERSON> akan menolak $1 transaksi secara bertahap."}, "rejectTxsN": {"message": "Tolak $1 transaksi"}, "rejected": {"message": "<PERSON><PERSON><PERSON>"}, "remove": {"message": "Hapus"}, "removeAccount": {"message": "<PERSON><PERSON> akun"}, "removeAccountDescription": {"message": "Akun ini akan dihapus dari dompet Anda. Pastikan Anda memiliki Frasa Pemulihan Rahas<PERSON> asli atau kunci pribadi untuk akun impor ini sebelum melanjutkan. Anda dapat mengimpor atau membuat akun lagi dari menu gulir bawah akun. "}, "removeKeyringSnap": {"message": "<PERSON>kun berikut akan dihapus dari NeoNix saat menghapus Snap ini:"}, "removeKeyringSnapToolTip": {"message": "Snap mengontrol akun. <PERSON><PERSON>, maka akun tersebut akan dihapus juga dari <PERSON>, tetapi akun tersebut akan tetap berada di blockchain."}, "removeNFT": {"message": "Hapus NFT"}, "removeNftErrorMessage": {"message": "<PERSON><PERSON> tidak dapat menghapus NFT ini."}, "removeNftMessage": {"message": "NFT berhasil dihapus!"}, "removeSnap": {"message": "Hapus <PERSON>"}, "removeSnapAccountDescription": {"message": "<PERSON><PERSON>, akun ini tidak lagi tersedia di NeoNix."}, "removeSnapAccountTitle": {"message": "<PERSON><PERSON> akun"}, "removeSnapConfirmation": {"message": "Yakin ingin menghapus $1?", "description": "$1 represents the name of the snap"}, "removeSnapDescription": {"message": "<PERSON>dakan ini akan menghapus snap, data<PERSON>, dan mencabut izin yang <PERSON>a berikan."}, "replace": {"message": "mengganti"}, "reportIssue": {"message": "Laporkan masalah"}, "requestFrom": {"message": "<PERSON><PERSON><PERSON><PERSON> dari"}, "requestFromInfo": {"message": "Ini merupakan situs yang meminta tanda tangan Anda."}, "requestFromInfoSnap": {"message": "Ini merupakan Snap yang meminta tanda tangan Anda."}, "requestFromTransactionDescription": {"message": "Ini merupakan situs yang meminta konfirmasi Anda."}, "requestingFor": {"message": "<PERSON><PERSON><PERSON> untuk"}, "requestingForAccount": {"message": "Meminta untuk $1", "description": "Name of Account"}, "requestingForNetwork": {"message": "Meminta untuk $1", "description": "Name of Network"}, "required": {"message": "<PERSON><PERSON><PERSON>"}, "reset": {"message": "<PERSON><PERSON>"}, "resetWallet": {"message": "Reset dompet"}, "resetWalletSubHeader": {"message": "NeoNix tidak menyimpan salinan kata sandi Anda. <PERSON><PERSON> terjadi masalah saat membuka akun, reset dompet Anda. <PERSON>a dapat melakukannya dengan member<PERSON>n <PERSON>asa <PERSON> yang digunakan saat mengatur dompet."}, "resetWalletUsingSRP": {"message": "Tindakan ini akan menghapus dompet saat ini dan <PERSON>asa Pemulihan Rahasia dari perangkat ini, bersa<PERSON> dengan daftar akun yang telah Anda kelola. Setelah direset dengan Frasa Pemulihan Rahas<PERSON>, <PERSON><PERSON> akan melihat daftar akun berdasarkan Frasa Pemulihan Rahasia yang Anda gunakan untuk mereset. Daftar baru ini secara otomatis akan menyertakan akun yang memiliki saldo. Anda juga dapat $1 yang telah dibuat sebelumnya. Akun khusus yang Anda impor harus $2, serta token khusus yang Anda tambahkan ke akun harus $3."}, "resetWalletWarning": {"message": "Pastikan Anda menggunakan Frasa Pemulihan Rahasia yang benar sebelum melanjutkan. Anda tidak dapat membatalkan tindakan ini."}, "restartNeoNix": {"message": "<PERSON><PERSON>"}, "restore": {"message": "Pulihkan"}, "restoreUserData": {"message": "Pulihkan data pengguna"}, "resultPageError": {"message": "<PERSON><PERSON><PERSON>"}, "resultPageErrorDefaultMessage": {"message": "<PERSON><PERSON> gagal."}, "resultPageSuccess": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "resultPageSuccessDefaultMessage": {"message": "<PERSON>si be<PERSON>."}, "retryTransaction": {"message": "Coba lagi transaksi"}, "reusedTokenNameWarning": {"message": "Token di sini menggunakan kembali simbol dari token lain yang <PERSON>a lihat, ini bisa jadi membingungkan atau menipu."}, "revealSecretRecoveryPhrase": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "revealSeedWords": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "revealSeedWordsDescription1": {"message": "$1 memberikan $2", "description": "This is a sentence consisting of link using 'revealSeedWordsSRPName' as $1 and bolded text using 'revealSeedWordsDescription3' as $2."}, "revealSeedWordsDescription2": {"message": "NeoNix merupakan $1. <PERSON><PERSON><PERSON>, <PERSON><PERSON> pemilik SRP Anda sendiri.", "description": "$1 is text link with the message from 'revealSeedWordsNonCustodialWallet'"}, "revealSeedWordsDescription3": {"message": "akses penuh ke dompet dan dana <PERSON>.\n"}, "revealSeedWordsNonCustodialWallet": {"message": "dompet dengan kendali penuh"}, "revealSeedWordsQR": {"message": "QR"}, "revealSeedWordsSRPName": {"message": "<PERSON><PERSON> (SRP)"}, "revealSeedWordsText": {"message": "Teks"}, "revealSeedWordsWarning": {"message": "Pastikan tidak ada yang melihat layar Anda. $1", "description": "$1 is bolded text using the message from 'revealSeedWordsWarning2'"}, "revealSeedWordsWarning2": {"message": "Dukungan NeoNix tidak akan pernah memintanya.", "description": "The bolded texted in the second part of 'revealSeedWordsWarning'"}, "revealSensitiveContent": {"message": "<PERSON><PERSON><PERSON><PERSON> konten sensitif"}, "review": {"message": "Tinjau"}, "reviewAlert": {"message": "<PERSON><PERSON><PERSON>"}, "reviewAlerts": {"message": "<PERSON><PERSON><PERSON>"}, "reviewPendingTransactions": {"message": "<PERSON><PERSON>u transaksi tertunda"}, "reviewPermissions": {"message": "<PERSON><PERSON><PERSON>"}, "revokePermission": {"message": "<PERSON><PERSON><PERSON>"}, "revokePermissionTitle": {"message": "Hapus izin $1", "description": "The token symbol that is being revoked"}, "revokeSimulationDetailsDesc": {"message": "<PERSON><PERSON> mengh<PERSON> izin orang lain untuk menggunakan token dari akun <PERSON>."}, "reward": {"message": "Imbalan"}, "rpcNameOptional": {"message": "<PERSON><PERSON> (Opsional)"}, "rpcUrl": {"message": "URL RPC"}, "safeTransferFrom": {"message": "Transfer aman dari"}, "save": {"message": "Simpan"}, "scanInstructions": {"message": "Tempatkan kode QR di bagian depan kamera Anda"}, "scanQrCode": {"message": "Pindai kode QR"}, "scrollDown": {"message": "<PERSON><PERSON><PERSON> ke bawah"}, "search": {"message": "<PERSON><PERSON>"}, "searchAccounts": {"message": "<PERSON><PERSON> akun"}, "searchNfts": {"message": "Cari NFT"}, "searchTokens": {"message": "Cari token"}, "searchTokensByNameOrAddress": {"message": "Cari token berdasarkan nama atau alamat"}, "secretRecoveryPhrase": {"message": "<PERSON><PERSON>"}, "secretRecoveryPhrasePlusNumber": {"message": "Frasa Pemulihan Ra<PERSON> $1", "description": "The $1 is the order of the Secret Recovery Phrase"}, "secureWallet": {"message": "Amankan do<PERSON>et"}, "security": {"message": "<PERSON><PERSON><PERSON>"}, "securityAlert": {"message": "Peringatan keamanan dari $1 dan $2"}, "securityAlerts": {"message": "<PERSON><PERSON><PERSON> keamanan"}, "securityAlertsDescription": {"message": "Fitur ini memperingatkan Anda tentang aktivitas berbahaya atau tidak biasa dengan secara aktif meninjau transaksi dan permintaan tanda tangan. $1", "description": "Link to learn more about security alerts"}, "securityAndPrivacy": {"message": "Keamanan & privasi"}, "securityDescription": {"message": "Ku<PERSON><PERSON> kemungkinan Anda bergabung dengan jaringan yang tidak aman dan lindungi akun Anda"}, "securityMessageLinkForNetworks": {"message": "penipuan jaringan dan risiko kea<PERSON>n"}, "securityProviderPoweredBy": {"message": "Didukung oleh $1", "description": "The security provider that is providing data"}, "seeAllPermissions": {"message": "<PERSON><PERSON> se<PERSON>", "description": "Used for revealing more content (e.g. permission list, etc.)"}, "seeDetails": {"message": "Lihat <PERSON>"}, "seedPhraseIntroTitle": {"message": "Amankan do<PERSON>et Anda"}, "seedPhraseReq": {"message": "<PERSON><PERSON> 12, 15, 18, 21, atau 24 kata"}, "select": {"message": "<PERSON><PERSON><PERSON>"}, "selectAccountToConnect": {"message": "<PERSON><PERSON><PERSON> akun yang akan dihubu<PERSON>kan"}, "selectAccounts": {"message": "Pilih akun untuk menggunakan situs ini"}, "selectAccountsForSnap": {"message": "<PERSON><PERSON>h akun yang akan digunakan bersama snap ini"}, "selectAll": {"message": "<PERSON><PERSON><PERSON> se<PERSON>a"}, "selectAnAccount": {"message": "<PERSON><PERSON><PERSON> akun"}, "selectAnAccountAlreadyConnected": {"message": "Akun ini sudah terhubung ke NeoNix."}, "selectEnableDisplayMediaPrivacyPreference": {"message": "Aktifkan Tampilkan Media NFT"}, "selectHdPath": {"message": "Pilih path HD"}, "selectNFTPrivacyPreference": {"message": "Aktifkan Autodeteksi NFT"}, "selectPathHelp": {"message": "Jika Anda tidak menemukan akun yang di<PERSON>, coba alihkan jalur HD atau jaringan yang dipilih saat ini."}, "selectRpcUrl": {"message": "Pilih URL RPC"}, "selectSecretRecoveryPhrase": {"message": "<PERSON><PERSON><PERSON>"}, "selectType": {"message": "<PERSON><PERSON><PERSON>"}, "selectedAccountMismatch": {"message": "<PERSON>kun yang berbeda dipilih"}, "selectingAllWillAllow": {"message": "<PERSON><PERSON><PERSON> semua akan mengizinkan situs ini untuk melihat semua akun Anda saat ini. Pastikan Anda memercayai situs ini."}, "send": {"message": "<PERSON><PERSON>"}, "sendBugReport": {"message": "<PERSON><PERSON>i kami laporan bug."}, "sendNoContactsConversionText": {"message": "klik di sini"}, "sendNoContactsDescription": {"message": "Kontak memungkinkan Anda untuk mengirim transaksi dengan aman ke akun lain beberapa kali. Untuk membuat kontak, $1", "description": "$1 represents the action text 'click here'"}, "sendNoContactsTitle": {"message": "Anda belum memiliki kontak"}, "sendSelectReceiveAsset": {"message": "<PERSON><PERSON><PERSON> aset yang akan diterima"}, "sendSelectSendAsset": {"message": "<PERSON><PERSON><PERSON> aset yang akan dikirim"}, "sendSpecifiedTokens": {"message": "Kirim $1", "description": "Symbol of the specified token"}, "sendSwapSubmissionWarning": {"message": "Mengklik tombol ini akan langsung memulai transaksi swap. Tinjau detail transaksi sebelum melanjutkan."}, "sendTokenAsToken": {"message": "Kirim $1 sebagai $2", "description": "Used in the transaction display list to describe a swap and send. $1 and $2 are the symbols of tokens in involved in the swap."}, "sendingAsset": {"message": "Mengirim $1"}, "sendingDisabled": {"message": "Belum mendukung pengiriman aset NFT ERC-1155."}, "sendingNativeAsset": {"message": "Mengirim $1", "description": "$1 represents the native currency symbol for the current network (e.g. ETH or BNB)"}, "sendingToTokenContractWarning": {"message": "Peringatan: <PERSON><PERSON> akan men<PERSON>m kontrak token yang berpotensi mengakibatkan hilangnya dana. $1", "description": "$1 is a clickable link with text defined by the 'learnMoreUpperCase' key. The link will open to a support article regarding the known contract address warning"}, "sepolia": {"message": "<PERSON><PERSON><PERSON>"}, "setApprovalForAll": {"message": "<PERSON><PERSON> per<PERSON> untuk semua"}, "setApprovalForAllRedesignedTitle": {"message": "Permintaan penarikan"}, "setApprovalForAllTitle": {"message": "Setujui $1 tanpa batas penggunaan", "description": "The token symbol that is being approved"}, "settingAddSnapAccount": {"message": "Tambahkan akun <PERSON>"}, "settings": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "settingsSearchMatchingNotFound": {"message": "Tidak menemukan hasil yang cocok."}, "settingsSubHeadingSignaturesAndTransactions": {"message": "<PERSON><PERSON><PERSON><PERSON> tanda tangan dan trans<PERSON>i"}, "show": {"message": "Tampil"}, "showAccount": {"message": "<PERSON><PERSON><PERSON><PERSON> akun"}, "showAdvancedDetails": {"message": "Tampilkan detail lanjutan"}, "showExtensionInFullSizeView": {"message": "<PERSON><PERSON><PERSON><PERSON> ekstensi dalam tampilan berukuran penuh"}, "showExtensionInFullSizeViewDescription": {"message": "Aktifkan ini untuk membuat tampilan berukuran penuh sebagai default saat mengklik ikon ekstensi."}, "showFiatConversionInTestnets": {"message": "Tampilkan konversi di jaringan uji"}, "showFiatConversionInTestnetsDescription": {"message": "Pilih ini untuk menampilkan konversi fiat di Testnet"}, "showHexData": {"message": "Tampilkan data Hex"}, "showHexDataDescription": {"message": "Pilih ini untuk menampilkan bidang data hex di layar kirim"}, "showLess": {"message": "Ciutkan"}, "showMore": {"message": "<PERSON><PERSON><PERSON><PERSON>g<PERSON>"}, "showNativeTokenAsMainBalance": {"message": "<PERSON><PERSON><PERSON><PERSON> token asli sebagai saldo utama"}, "showNft": {"message": "Tampilkan NFT"}, "showPermissions": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "showPrivateKey": {"message": "<PERSON><PERSON><PERSON><PERSON> kunci pribadi"}, "showSRP": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "showTestnetNetworks": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "showTestnetNetworksDescription": {"message": "Pilih opsi ini untuk menampilkan jaringan pengujian dalam daftar jaringan"}, "sign": {"message": "<PERSON>da tangan"}, "signatureRequest": {"message": "<PERSON><PERSON><PERSON><PERSON> tanda tangan"}, "signature_decoding_bid_nft_tooltip": {"message": "NFT akan terlihat di dompet Anda saat tawaran diterima."}, "signature_decoding_list_nft_tooltip": {"message": "Nantikan perubahan hanya jika seseorang membeli NFT Anda."}, "signed": {"message": "Ditandatangani"}, "signing": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "signingInWith": {"message": "<PERSON><PERSON><PERSON>"}, "signingWith": {"message": "<PERSON>da tangani dengan"}, "simulationApproveHeading": {"message": "<PERSON><PERSON>"}, "simulationDetailsApproveDesc": {"message": "Anda memberi orang lain izin untuk menarik NFT dari akun <PERSON>."}, "simulationDetailsERC20ApproveDesc": {"message": "Anda memberi orang lain izin untuk menggunakan jumlah ini dari akun <PERSON>."}, "simulationDetailsFiatNotAvailable": {"message": "Tidak Tersedia"}, "simulationDetailsIncomingHeading": {"message": "<PERSON><PERSON>"}, "simulationDetailsNoChanges": {"message": "Tidak ada per<PERSON>han"}, "simulationDetailsOutgoingHeading": {"message": "<PERSON><PERSON>"}, "simulationDetailsRevokeSetApprovalForAllDesc": {"message": "<PERSON><PERSON> men<PERSON> izin orang lain untuk menarik NFT dari akun <PERSON>."}, "simulationDetailsSetApprovalForAllDesc": {"message": "Anda memberikan izin kepada orang lain untuk menarik NFT dari akun <PERSON>."}, "simulationDetailsTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "simulationDetailsTitleTooltip": {"message": "<PERSON><PERSON><PERSON><PERSON> perubahan merupakan hal yang mungkin terjadi jika Anda melakukan transaksi ini. Ini hanyalah prediksi, bukan jaminan."}, "simulationDetailsTotalFiat": {"message": "Total = $1", "description": "$1 is the total amount in fiat currency on one side of the transaction"}, "simulationDetailsTransactionReverted": {"message": "Transaksi ini kemungkinan besar akan gagal"}, "simulationDetailsUnavailable": {"message": "Tidak tersedia"}, "simulationErrorMessageV2": {"message": "Kami tidak dapat memperkirakan gas. Tampaknya ada kesalahan dalam kontrak dan transaksi ini berpotensi gagal."}, "simulationsSettingDescription": {"message": "Aktifkan untuk mengestimasikan perubahan saldo transaksi dan tanda tangan sebelum Anda mengonfirmasikannya. Ini tidak menjamin hasil akhirnya. $1"}, "simulationsSettingSubHeader": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON>"}, "singleNetwork": {"message": "1 jaringan"}, "siweIssued": {"message": "Dikeluarkan"}, "siweNetwork": {"message": "<PERSON><PERSON><PERSON>"}, "siweRequestId": {"message": "Permintaan ID"}, "siweResources": {"message": "Sumber daya"}, "siweURI": {"message": "URL"}, "skipAccountSecurity": {"message": "<PERSON><PERSON> keamanan akun?"}, "skipAccountSecurityDetails": {"message": "Saya memahami bahwa sampai saya mencadan<PERSON>, saya dapat kehilangan akun saya dan semua aset yang ada."}, "slideBridgeDescription": {"message": "Berpindah antara 9 chain, semuanya di dalam dompet Anda"}, "slideBridgeTitle": {"message": "Siap untuk bridge?"}, "slideCashOutDescription": {"message": "<PERSON><PERSON> kripto dan dapatkan uang tunai"}, "slideCashOutTitle": {"message": "Cairkan uang tunai dengan NeoNix"}, "slideDebitCardDescription": {"message": "Tersedia di wilayah tertentu"}, "slideDebitCardTitle": {"message": "Kartu debit NeoNix"}, "slideFundWalletDescription": {"message": "Tambah atau transfer token untuk memulai"}, "slideFundWalletTitle": {"message": "<PERSON><PERSON>"}, "slideMultiSrpDescription": {"message": "Impor dan gunakan beberapa dompet di NeoNix"}, "slideMultiSrpTitle": {"message": "Tambahkan beberapa Frasa Pemulihan <PERSON>"}, "slideRemoteModeDescription": {"message": "Gunakan dompet cold secara nirkabel"}, "slideRemoteModeTitle": {"message": "Cold storage, akses cepat"}, "slideSmartAccountUpgradeDescription": {"message": "<PERSON><PERSON><PERSON>, fitur lebih cerdas"}, "slideSmartAccountUpgradeTitle": {"message": "<PERSON><PERSON> mengg<PERSON>kan akun cerdas"}, "slideSolanaDescription": {"message": "<PERSON>uat akun <PERSON> untuk memulai"}, "slideSolanaTitle": {"message": "<PERSON><PERSON>"}, "slideSweepStakeDescription": {"message": "Cetak NFT sekarang dan raih peluang untuk menang"}, "slideSweepStakeTitle": {"message": "I<PERSON>ti Giveaway USDC senilai $5000!"}, "smartAccountAccept": {"message": "<PERSON><PERSON><PERSON> akun cerdas"}, "smartAccountBetterTransaction": {"message": "Transaksi lebih cepat, biaya lebih rendah"}, "smartAccountBetterTransactionDescription": {"message": "<PERSON><PERSON> waktu dan uang dengan memproses trans<PERSON>i secara be<PERSON>."}, "smartAccountFeaturesDescription": {"message": "<PERSON>mpan alamat akun yang sama dan Anda dapat beralih kembali setiap saat."}, "smartAccountLabel": {"message": "<PERSON><PERSON><PERSON>"}, "smartAccountPayToken": {"message": "Bayar dengan token apa pun setiap saat"}, "smartAccountPayTokenDescription": {"message": "Gunakan token yang sudah Anda miliki untuk menutup biaya jaringan."}, "smartAccountReject": {"message": "<PERSON><PERSON> gunakan akun cerdas"}, "smartAccountRequestFor": {"message": "<PERSON>min<PERSON><PERSON> untuk"}, "smartAccountSameAccount": {"message": "<PERSON><PERSON><PERSON> sama, fitur lebih cerdas."}, "smartAccountSplashTitle": {"message": "<PERSON>akan akun cerdas?"}, "smartAccountUpgradeBannerDescription": {"message": "<PERSON><PERSON><PERSON> sa<PERSON>. <PERSON><PERSON> lebih cerdas."}, "smartAccountUpgradeBannerTitle": {"message": "Beralih ke akun cerdas"}, "smartContracts": {"message": "<PERSON><PERSON><PERSON> c<PERSON>"}, "smartSwapsErrorNotEnoughFunds": {"message": "Dana tidak cukup untuk pertukaran cerdas."}, "smartSwapsErrorUnavailable": {"message": "Smart Swap tidak tersedia untuk sementara waktu."}, "smartTransactionCancelled": {"message": "Transaksi <PERSON>"}, "smartTransactionCancelledDescription": {"message": "Transaksi Anda tidak dapat disel<PERSON>, sehingga dibatalkan agar Anda tidak perlu membayar biaya gas yang tidak seharusnya."}, "smartTransactionError": {"message": "Trans<PERSON><PERSON> gagal"}, "smartTransactionErrorDescription": {"message": "<PERSON>bahan pasar yang mendadak dapat menyebabkan kegagalan. <PERSON><PERSON> ma<PERSON>, hubungi dukungan pelanggan NeoNix."}, "smartTransactionPending": {"message": "Transaksi Anda telah di<PERSON>"}, "smartTransactionSuccess": {"message": "Transaksi <PERSON>"}, "smartTransactions": {"message": "Transaksi Pintar"}, "smartTransactionsEnabledDescription": {"message": " dan per<PERSON><PERSON>an MEV. Kini aktif secara default."}, "smartTransactionsEnabledLink": {"message": "Tingkat keberhasilan lebih tinggi"}, "smartTransactionsEnabledTitle": {"message": "Transaksi menjadi lebih pintar"}, "snapAccountCreated": {"message": "Akun dibuat"}, "snapAccountCreatedDescription": {"message": "Akun baru Anda siap digunakan!"}, "snapAccountCreationFailed": {"message": "Pem<PERSON>atan akun gagal"}, "snapAccountCreationFailedDescription": {"message": "$1 tidak berhasil membuat akun untuk Anda.", "description": "$1 is the snap name"}, "snapAccountRedirectFinishSigningTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "snapAccountRedirectSiteDescription": {"message": "Ikuti petunjuk dari $1"}, "snapAccountRemovalFailed": {"message": "Penghapusan akun gagal"}, "snapAccountRemovalFailedDescription": {"message": "$1 tidak berhasil menghapus akun ini untuk Anda.", "description": "$1 is the snap name"}, "snapAccountRemoved": {"message": "<PERSON><PERSON><PERSON>"}, "snapAccountRemovedDescription": {"message": "Akun ini tidak lagi tersedia untuk digunakan di NeoNix."}, "snapAccounts": {"message": "<PERSON><PERSON><PERSON>"}, "snapAccountsDescription": {"message": "<PERSON><PERSON>n yang dikontrol oleh Snap pihak ketiga."}, "snapConnectTo": {"message": "Hubungkan ke $1", "description": "$1 is the website URL or a Snap name. Used for Snaps pre-approved connections."}, "snapConnectionPermissionDescription": {"message": "Izinkan $1 terhubung secara otomatis ke $2 tanpa persetujuan Anda.", "description": "Used for Snap pre-approved connections. $1 is the Snap name, $2 is a website URL."}, "snapConnectionWarning": {"message": "$1 ingin menggunakan $2", "description": "$2 is the snap and $1 is the dapp requesting connection to the snap."}, "snapDetailWebsite": {"message": "Situs web"}, "snapHomeMenu": {"message": "<PERSON><PERSON>"}, "snapInstallRequest": {"message": "Anda memberikan izin berikut dengan menginstal $1.", "description": "$1 is the snap name."}, "snapInstallSuccess": {"message": "Instalasi se<PERSON>"}, "snapInstallWarningCheck": {"message": "$1 meminta izin untuk melakukan hal berikut:", "description": "Warning message used in popup displayed on snap install. $1 is the snap name."}, "snapInstallWarningHeading": {"message": "Lanju<PERSON><PERSON> dengan hati-hati"}, "snapInstallWarningPermissionDescriptionForBip32View": {"message": "Izinkan $1 untuk melihat kunci publik (dan al<PERSON><PERSON>). Ini tidak memberi kendali atas akun atau aset.", "description": "An extended description for the `snap_getBip32PublicKey` permission used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionDescriptionForEntropy": {"message": "Izinkan Snap $1 untuk mengelola akun dan aset pada jaringan yang diminta. Akun-akun ini diperoleh dan dicadangkan menggunakan frasa pemulihan rahasia (tanpa mengungkapkannya). Dengan kemampuannya untuk mendapatkan kunci, $1 dapat mendukung berbagai protokol blockchain di luar Ethereum (EVM).", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionNameForEntropy": {"message": "Kelola akun $1", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallWarningPermissionNameForViewPublicKey": {"message": "Lihat kunci publik untuk $1", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallationErrorDescription": {"message": "$1 tidak dapat diinstal.", "description": "Error description used when snap installation fails. $1 is the snap name."}, "snapInstallationErrorTitle": {"message": "<PERSON><PERSON><PERSON> gagal", "description": "Error title used when snap installation fails."}, "snapResultError": {"message": "<PERSON><PERSON><PERSON>"}, "snapResultSuccess": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "snapResultSuccessDescription": {"message": "$1 siap digunakan"}, "snapUIAssetSelectorTitle": {"message": "<PERSON><PERSON><PERSON> aset"}, "snapUpdateAlertDescription": {"message": "Dapatkan versi terbaru $1", "description": "Description used in Snap update alert banner when snap update is available. $1 is the Snap name."}, "snapUpdateAvailable": {"message": "Pembaruan tersedia"}, "snapUpdateErrorDescription": {"message": "$1 tidak dapat diperbarui.", "description": "Error description used when snap update fails. $1 is the snap name."}, "snapUpdateErrorTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> gagal", "description": "Error title used when snap update fails."}, "snapUpdateRequest": {"message": "Anda memberikan izin berikut dengan memperbarui $1.", "description": "$1 is the Snap name."}, "snapUpdateSuccess": {"message": "Pembaruan se<PERSON>ai"}, "snapUrlIsBlocked": {"message": "Snap ini ingin men<PERSON>kan Anda ke situs yang diblokir. $1."}, "snaps": {"message": "Snap"}, "snapsConnected": {"message": "Snap terhubung"}, "snapsNoInsight": {"message": "Tidak ada wawasan untuk ditampilkan"}, "snapsPrivacyWarningFirstMessage": {"message": "Anda menyatakan bahwa Snap yang diinstal adalah <PERSON>, kecuali jika diidentifikasi lain, sebagaimana dijelaskan dalam $1 Consensys. Penggunaan Layanan Pihak Ketiga diatur oleh syarat dan ketentuan terpisah yang ditetapkan oleh penyedia Layanan Pi<PERSON>. Consensys tidak merekomendasikan penggunaan Snap oleh orang tertentu karena alasan tertentu. <PERSON><PERSON> menga<PERSON>, mengan<PERSON><PERSON>, atau menggunakan Layanan Pihak Ketiga dengan risiko sendiri. Consensys menafikan semua tanggung jawab dan kewajiban atas kerugian yang timbul karena penggunaan Layanan Pihak Ketiga.", "description": "First part of a message in popup modal displayed when installing a snap for the first time. $1 is terms of use link."}, "snapsPrivacyWarningSecondMessage": {"message": "Setiap informasi yang Anda bagikan kepada Layanan Pihak Ketiga akan dikumpulkan langsung oleh Layanan Pihak Ketiga tersebut sesuai dengan kebijakan privasinya. Baca kebijakan privasinya untuk informasi lebih lanjut.", "description": "Second part of a message in popup modal displayed when installing a snap for the first time."}, "snapsPrivacyWarningThirdMessage": {"message": "Consensys tidak memiliki akses ke informasi yang Anda bagikan kepada <PERSON>n <PERSON>.", "description": "Third part of a message in popup modal displayed when installing a snap for the first time."}, "snapsSettings": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "snapsTermsOfUse": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "snapsToggle": {"message": "Snap hanya akan be<PERSON>asi jika diaktifkan"}, "snapsUIError": {"message": "Hubungi pembuat $1 untuk dukungan lebih lanjut.", "description": "This is shown when the insight snap throws an error. $1 is the snap name"}, "solanaAccountRequested": {"message": "Situs ini meminta akun <PERSON>."}, "solanaAccountRequired": {"message": "<PERSON><PERSON><PERSON> diperlukan untuk terhubung ke situs ini."}, "solanaImportAccounts": {"message": "<PERSON><PERSON><PERSON> a<PERSON>"}, "solanaImportAccountsDescription": {"message": "<PERSON><PERSON><PERSON> untuk memindahkan akun <PERSON> dari dompet lain."}, "solanaMoreFeaturesComingSoon": {"message": "<PERSON>tur lainnya segera hadir"}, "solanaMoreFeaturesComingSoonDescription": {"message": "NFT, dukungan dompet perangkat keras, dan yang lainnya akan segera hadir."}, "solanaOnNeoNix": {"message": "Solana di NeoNix"}, "solanaSendReceiveSwapTokens": {"message": "<PERSON><PERSON>, <PERSON><PERSON>, dan tukar token"}, "solanaSendReceiveSwapTokensDescription": {"message": "Transfer dan bert<PERSON><PERSON><PERSON>i dengan token seperti SOL, USDC, dan la<PERSON><PERSON>."}, "someNetworks": {"message": "$1 jaringan"}, "somethingDoesntLookRight": {"message": "Ada yang tidak beres? $1", "description": "A false positive message for users to contact support. $1 is a link to the support page."}, "somethingIsWrong": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>. Coba muat ulang halaman."}, "somethingWentWrong": {"message": "Kami tidak dapat memuat halaman ini."}, "sortBy": {"message": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>ai"}, "sortByAlphabetically": {"message": "<PERSON><PERSON><PERSON> (A-Z)"}, "sortByDecliningBalance": {"message": "<PERSON><PERSON> ($1 tinggi-rendah)", "description": "Indicates a descending order based on token fiat balance. $1 is the preferred currency symbol"}, "source": {"message": "Sumber"}, "spamModalBlockedDescription": {"message": "Situs ini akan diblokir selama 1 menit."}, "spamModalBlockedTitle": {"message": "Anda telah memblokir situs ini untuk sementara"}, "spamModalDescription": {"message": "<PERSON><PERSON> dibombardir dengan banyak permin<PERSON>, <PERSON><PERSON> dapat memblokir situs tersebut untuk sementara."}, "spamModalTemporaryBlockButton": {"message": "Blokir situs ini untuk sementara"}, "spamModalTitle": {"message": "<PERSON><PERSON> telah melihat banyak permintaan"}, "speed": {"message": "Kecepatan"}, "speedUp": {"message": "Percepat"}, "speedUpCancellation": {"message": "Percepat pembatalan ini"}, "speedUpExplanation": {"message": "<PERSON><PERSON> telah memperbarui biaya gas berdasarkan kondisi jaringan saat ini dan telah meningkatkannya minimal 10% (diperlukan oleh jaringan)."}, "speedUpPopoverTitle": {"message": "Percepat transaksi"}, "speedUpTooltipText": {"message": "Biaya gas baru"}, "speedUpTransaction": {"message": "Percepat transaksi ini"}, "spendLimitInsufficient": {"message": "Batas penggunaan tidak mencukupi"}, "spendLimitInvalid": {"message": "Batas penggunaan tidak valid; harus berupa bilangan positif"}, "spendLimitPermission": {"message": "<PERSON><PERSON> batas penggunaan"}, "spendLimitRequestedBy": {"message": "Batas penggunaan diminta oleh $1", "description": "Origin of the site requesting the spend limit"}, "spendLimitTooLarge": {"message": "Batas penggunaan terlalu besar"}, "spender": {"message": "Pengguna"}, "spenderTooltipDesc": {"message": "Ini adalah alamat yang dapat digunakan untuk menarik NFT Anda."}, "spenderTooltipERC20ApproveDesc": {"message": "Ini adalah alamat yang dapat menggunakan token Anda atas nama Anda."}, "spendingCap": {"message": "<PERSON><PERSON>"}, "spendingCaps": {"message": "<PERSON><PERSON>"}, "srpInputNumberOfWords": {"message": "Frasa milik saya memiliki $1 kata", "description": "This is the text for each option in the dropdown where a user selects how many words their secret recovery phrase has during import. The $1 is the number of words (either 12, 15, 18, 21, or 24)."}, "srpListName": {"message": "Frasa Pemulihan Ra<PERSON> $1", "description": "$1 is the order of the Secret Recovery Phrase"}, "srpListNumberOfAccounts": {"message": "$1 akun", "description": "$1 is the number of accounts in the list"}, "srpListSelectionDescription": {"message": "<PERSON><PERSON> akun baru Anda akan dibuat dari"}, "srpListSingleOrZero": {"message": "$1 akun", "description": "$1 is the number of accounts in the list, it is either 1 or 0"}, "srpPasteFailedTooManyWords": {"message": "<PERSON><PERSON> ditempel karena memuat lebih dari 24 kata. Fr<PERSON> pemulihan rahasia dapat memuat maksimum 24 kata.", "description": "Description of SRP paste error when the pasted content has too many words"}, "srpPasteTip": {"message": "<PERSON>a dapat menempelkan seluruh frasa pemulihan rahasia ke bagian mana pun", "description": "Our secret recovery phrase input is split into one field per word. This message explains to users that they can paste their entire secrete recovery phrase into any field, and we will handle it correctly."}, "srpSecurityQuizGetStarted": {"message": "<PERSON><PERSON>"}, "srpSecurityQuizImgAlt": {"message": "<PERSON> dengan lubang kunci di tengah, dan tiga bidang kata sandi melayang"}, "srpSecurityQuizIntroduction": {"message": "Untuk mengungkapkan Frasa <PERSON>, <PERSON><PERSON> perlu menjawab dua pertanyaan dengan benar"}, "srpSecurityQuizQuestionOneQuestion": {"message": "<PERSON><PERSON>, NeoNix..."}, "srpSecurityQuizQuestionOneRightAnswer": {"message": "Tidak dapat membantu Anda"}, "srpSecurityQuizQuestionOneRightAnswerDescription": {"message": "<PERSON>at, ukir pada logam, atau simpan di beberapa tempat rahasia agar Anda tidak pernah kehilangan. <PERSON><PERSON> kehilangan, maka akan hilang selamanya."}, "srpSecurityQuizQuestionOneRightAnswerTitle": {"message": "Benar! Tidak ada yang dapat membantu mengembalikan Frasa <PERSON>a"}, "srpSecurityQuizQuestionOneWrongAnswer": {"message": "Dapat mengembalikannya untuk Anda"}, "srpSecurityQuizQuestionOneWrongAnswerDescription": {"message": "<PERSON><PERSON> Anda kehilangan <PERSON>, maka akan hilang selam<PERSON>. Tidak ada yang dapat membantu <PERSON><PERSON>, apa pun yang mereka katakan."}, "srpSecurityQuizQuestionOneWrongAnswerTitle": {"message": "Salah! Tidak ada yang dapat membantu mengembalikan Frasa <PERSON>a"}, "srpSecurityQuizQuestionTwoQuestion": {"message": "<PERSON><PERSON> ada yang men<PERSON>akan <PERSON>, bahkan agen pendukung,..."}, "srpSecurityQuizQuestionTwoRightAnswer": {"message": "<PERSON><PERSON> <PERSON>"}, "srpSecurityQuizQuestionTwoRightAnswerDescription": {"message": "Siapa pun yang mengaku membutuhkan <PERSON>, mereka berbohong kepada <PERSON>. <PERSON><PERSON> memba<PERSON>, mereka akan mencuri aset <PERSON>."}, "srpSecurityQuizQuestionTwoRightAnswerTitle": {"message": "Benar! Membagikan Frasa Pemulihan Rahas<PERSON> bukanlah ide yang baik"}, "srpSecurityQuizQuestionTwoWrongAnswer": {"message": "<PERSON><PERSON> harus member<PERSON><PERSON>a kepada mereka"}, "srpSecurityQuizQuestionTwoWrongAnswerDescription": {"message": "Siapa pun yang mengaku membutuhkan <PERSON>, mereka berbohong kepada <PERSON>. <PERSON><PERSON> memba<PERSON>, mereka akan mencuri aset <PERSON>."}, "srpSecurityQuizQuestionTwoWrongAnswerTitle": {"message": "Tidak! Jangan pernah membagikan Frasa Pemulihan Rahas<PERSON> kepada siapa pun"}, "srpSecurityQuizTitle": {"message": "<PERSON><PERSON> k<PERSON>"}, "srpToggleShow": {"message": "<PERSON><PERSON><PERSON><PERSON>/Sembunyikan kata dari frasa pemulihan rahasia ini", "description": "Describes a toggle that is used to show or hide a single word of the secret recovery phrase"}, "srpWordHidden": {"message": "Kata ini disembunyikan", "description": "Explains that a word in the secret recovery phrase is hidden"}, "srpWordShown": {"message": "Kata ini sedang ditampilkan", "description": "Explains that a word in the secret recovery phrase is being shown"}, "stable": {"message": "Stabil"}, "stableLowercase": {"message": "stabil"}, "stake": {"message": "Stake"}, "staked": {"message": "Di-stake"}, "standardAccountLabel": {"message": "<PERSON><PERSON><PERSON>"}, "startEarning": {"message": "<PERSON><PERSON>"}, "stateLogError": {"message": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han pada log status pengambilan."}, "stateLogFileName": {"message": "Log status NeoNix"}, "stateLogs": {"message": "Log status"}, "stateLogsDescription": {"message": "Log status berisi alamat akun publik Anda dan transaksi terkirim."}, "status": {"message": "Status"}, "statusNotConnected": {"message": "Tidak terhubung"}, "statusNotConnectedAccount": {"message": "Tidak ada akun yang terhubung"}, "step1LatticeWallet": {"message": "Hubungkan Lattice1 Anda"}, "step1LatticeWalletMsg": {"message": "Anda dapat menghubungkan NeoNix ke perangkat Lattice1 setelah diatur dan daring. Buka perangkat Anda dan siapkan ID Perangkat Anda.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step1LedgerWallet": {"message": "<PERSON><PERSON>h aplikasi Ledger"}, "step1LedgerWalletMsg": {"message": "<PERSON><PERSON><PERSON>, atur, dan masukkan kata sandi Anda untuk membuka $1.", "description": "$1 represents the `ledgerLiveApp` localization value"}, "step1TrezorWallet": {"message": "Hubung<PERSON> T<PERSON>"}, "step1TrezorWalletMsg": {"message": "<PERSON><PERSON> lang<PERSON>g ke komputer dan buka. Pastikan Anda menggunakan frasa sandi yang benar.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step2LedgerWallet": {"message": "Hubungkan Ledger Anda"}, "step2LedgerWalletMsg": {"message": "Pasang Ledger <PERSON><PERSON> langsung ke komputer dan buka. <PERSON><PERSON>, buka aplikasi Ethereum.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "stillGettingMessage": {"message": "Masih menerima pesan ini?"}, "strong": {"message": "Ku<PERSON>"}, "stxCancelled": {"message": "<PERSON><PERSON><PERSON><PERSON> akan gagal"}, "stxCancelledDescription": {"message": "Transaksi Anda akan gagal dan dibatalkan agar Anda tidak perlu membayar biaya gas yang tidak seharusnya."}, "stxCancelledSubDescription": {"message": "Cobalah untuk menukar lagi. <PERSON><PERSON> akan selalu hadir untuk melindungi Anda dari risiko serupa di lain waktu."}, "stxFailure": {"message": "<PERSON><PERSON><PERSON><PERSON> gagal"}, "stxFailureDescription": {"message": "Perubahan pasar yang terjadi secara tiba-tiba dapat menyebabkan kegagalan. Jika masalah be<PERSON>, hubungi $1.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.NeoNix.io"}, "stxOptInSupportedNetworksDescription": {"message": "Aktifkan Transaksi Pintar untuk transaksi yang lebih andal dan aman di jaringan yang didukung. $1"}, "stxPendingPrivatelySubmittingSwap": {"message": "<PERSON><PERSON><PERSON>a secara pribadi..."}, "stxPendingPubliclySubmittingSwap": {"message": "Kirimkan Swap Anda secara publik..."}, "stxSuccess": {"message": "Pertukaran selesai!"}, "stxSuccessDescription": {"message": "$1 kini telah tersedia.", "description": "$1 is a token symbol, e.g. ETH"}, "stxSwapCompleteIn": {"message": "<PERSON><PERSON><PERSON> akan se<PERSON>ai dalam <", "description": "'<' means 'less than', e.g. <PERSON><PERSON><PERSON> will complete in < 2:59"}, "stxTryingToCancel": {"message": "Mencoba membatalkan transaksi Anda..."}, "stxUnknown": {"message": "Status tidak diketahui"}, "stxUnknownDescription": {"message": "Transaksi telah berhasil tetapi kami tidak yakin transaksi yang mana. Hal ini dikarenakan Anda mengirimkan transaksi lain saat pertukaran ini sedang diproses."}, "stxUserCancelled": {"message": "<PERSON><PERSON><PERSON>"}, "stxUserCancelledDescription": {"message": "Transaksi Anda telah dibatalkan dan Anda tidak membayar biaya gas yang tidak perlu."}, "submit": {"message": "<PERSON><PERSON>"}, "submitted": {"message": "Terkirim"}, "suggestedBySnap": {"message": "Disarankan oleh $1", "description": "$1 is the snap name"}, "suggestedCurrencySymbol": {"message": "Simbol mata uang yang disarankan:"}, "suggestedTokenName": {"message": "<PERSON><PERSON> yang disarankan:"}, "supplied": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "support": {"message": "Dukungan"}, "supportCenter": {"message": "<PERSON><PERSON><PERSON>gi pusat dukungan kami"}, "supportMultiRpcInformation": {"message": "Saat ini kami mendukung beberapa RPC untuk satu jaringan. RPC terbaru Anda telah dipilih sebagai RPC default untuk mengatasi informasi yang saling bertentangan."}, "surveyConversion": {"message": "<PERSON><PERSON><PERSON> survei kami"}, "surveyTitle": {"message": "Bentuk masa depan NeoNix"}, "swap": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "swapAdjustSlippage": {"message": "<PERSON><PERSON><PERSON><PERSON> selip"}, "swapAggregator": {"message": "Agregator"}, "swapAllowSwappingOf": {"message": "<PERSON><PERSON><PERSON> $1", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "swapAmountReceived": {"message": "<PERSON><PERSON><PERSON> yang dijamin"}, "swapAmountReceivedInfo": {"message": "Ini merupakan jumlah minimum yang akan Anda terima. Anda bisa mendapatkan lebih banyak lagi tergantung pada selip."}, "swapAndSend": {"message": "Tukar & Kirim"}, "swapAnyway": {"message": "Te<PERSON>p tukar"}, "swapApproval": {"message": "Setujui pertukaran $1", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be swapped.. $1 is the symbol of a token that has been approved."}, "swapApproveNeedMoreTokens": {"message": "<PERSON><PERSON> me<PERSON> $1 $2 lagi untuk menyelesaikan pertukaran ini", "description": "Tells the user how many more of a given token they need for a specific swap. $1 is an amount of tokens and $2 is the token symbol."}, "swapAreYouStillThere": {"message": "Ma<PERSON>h di sana?"}, "swapAreYouStillThereDescription": {"message": "<PERSON><PERSON> siap menampilkan kuotasi terbaru jika Anda ingin melanjutkan"}, "swapConfirmWithHwWallet": {"message": "Konfirmasikan dengan dompet perangkat keras Anda"}, "swapContinueSwapping": {"message": "Lanju<PERSON><PERSON>"}, "swapContractDataDisabledErrorDescription": {"message": "Di aplikasi Ethereum di Ledger Anda, b<PERSON> \"Pengaturan\" dan izinkan data kontrak. <PERSON><PERSON>, coba lagi pertukaran <PERSON>."}, "swapContractDataDisabledErrorTitle": {"message": "Data kontrak tidak diaktifkan di Ledger Anda"}, "swapCustom": {"message": "kustom"}, "swapDecentralizedExchange": {"message": "Bursa terdesentralisasi"}, "swapDirectContract": {"message": "<PERSON><PERSON><PERSON> langsun<PERSON>"}, "swapEditLimit": {"message": "<PERSON> batas"}, "swapEnableDescription": {"message": "Ini diwajibkan dan memberikan NeoNix izin untuk menukar $1 Anda.", "description": "Gives the user info about the required approval transaction for swaps. $1 will be the symbol of a token being approved for swaps."}, "swapEnableTokenForSwapping": {"message": "Ini akan $1 untuk menukar", "description": "$1 is for the 'enableToken' key, e.g. 'enable ETH'"}, "swapEnterAmount": {"message": "<PERSON><PERSON><PERSON><PERSON> jum<PERSON>"}, "swapEstimatedNetworkFees": {"message": "Estimasi biaya jaringan"}, "swapEstimatedNetworkFeesInfo": {"message": "Ini merupakan estimasi biaya jaringan yang akan digunakan untuk menyelesaikan pertukaran Anda. Jumlah aktual dapat berubah sesuai dengan kondisi jaringan."}, "swapFailedErrorDescriptionWithSupportLink": {"message": "Kegagalan transaksi terjadi dan kami siap membantu. <PERSON><PERSON> masalah terus be<PERSON>, <PERSON><PERSON> dapat menghubungi dukungan pelanggan kami di $1 untuk mendapatkan bantuan lebih lanjut.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.NeoNix.io"}, "swapFailedErrorTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> gagal"}, "swapFetchingQuote": {"message": "Mengambil kuotasi"}, "swapFetchingQuoteNofN": {"message": "Mengambil kuotasi $1 dari $2", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapFetchingQuotes": {"message": "Mengambil kuotasi..."}, "swapFetchingQuotesErrorDescription": {"message": "Hmmm... ter<PERSON><PERSON> k<PERSON>. <PERSON><PERSON> lagi, atau jika masalah te<PERSON> be<PERSON>, hubungi dukungan pelanggan."}, "swapFetchingQuotesErrorTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat mengambil kuota"}, "swapFromTo": {"message": "Pertukaran dari $1 ke $2", "description": "Tells a user that they need to confirm on their hardware wallet a swap of 2 tokens. $1 is a source token and $2 is a destination token"}, "swapGasFeesDetails": {"message": "Biaya gas diperkirakan dan akan berfluktuasi berdasarkan lalu lintas jaringan dan kompleksitas transaksi."}, "swapGasFeesExplanation": {"message": "NeoNix tidak menghasilkan uang dari biaya gas. Biaya ini merupakan estimasi dan dapat berubah berdasarkan seberapa sibuk jaringan dan seberapa rumit transaksinya. Pelajari selengkapnya $1.", "description": "$1 is a link (text in link can be found at 'swapGasFeesSummaryLinkText')"}, "swapGasFeesExplanationLinkText": {"message": "di sini", "description": "Text for link in swapGasFeesExplanation"}, "swapGasFeesLearnMore": {"message": "Pelajari selengkapnya seputar biaya gas"}, "swapGasFeesSplit": {"message": "Biaya gas pada layar sebelumnya dibagi antara kedua transaksi ini."}, "swapGasFeesSummary": {"message": "Biaya gas dibayarkan kepada penambang kripto yang memproses transaksi di jaringan $1. NeoNix tidak mengambil keuntungan dari biaya gas.", "description": "$1 is the selected network, e.g. Ethereum or BSC"}, "swapGasIncludedTooltipExplanation": {"message": "Kuotasi ini mencakup biaya gas dengan menyesuaikan jumlah token yang dikirim atau diterima. Anda bisa menerima ETH dalam transaksi terpisah pada daftar aktivitas Anda."}, "swapGasIncludedTooltipExplanationLinkText": {"message": "Pelajari selengkapnya seputar biaya gas"}, "swapHighSlippage": {"message": "<PERSON><PERSON>"}, "swapIncludesGasAndNeoNixFee": {"message": "Termasuk biaya gas dan NeoNix sebesar $1%", "description": "Provides information about the fee that NeoNix takes for swaps. $1 is a decimal number."}, "swapIncludesMMFee": {"message": "Termasuk $1% biaya NeoNix.", "description": "Provides information about the fee that NeoNix takes for swaps. $1 is a decimal number."}, "swapIncludesMMFeeAlt": {"message": "Kuotasi mencerminkan biaya NeoNix sebesar $1%", "description": "Provides information about the fee that NeoNix takes for swaps using the latest copy. $1 is a decimal number."}, "swapIncludesNeoNixFeeViewAllQuotes": {"message": "Termasuk $1% biaya NeoNix – $2", "description": "Provides information about the fee that NeoNix takes for swaps. $1 is a decimal number and $2 is a link to view all quotes."}, "swapLearnMore": {"message": "P<PERSON>jari selengkapnya seputar Swap"}, "swapLiquiditySourceInfo": {"message": "<PERSON><PERSON> mencari beberapa sumber likuiditas (bursa, agregator, dan pendiri pasar profesional) untuk membandingkan tarif bursa dan biaya jaringan."}, "swapLowSlippage": {"message": "<PERSON><PERSON>"}, "swapMaxSlippage": {"message": "<PERSON><PERSON><PERSON> maks"}, "swapNeoNixFee": {"message": "Biaya NeoNix"}, "swapNeoNixFeeDescription": {"message": "Biaya sebesar $1% otomatis diperhitungkan ke dalam kuotasi ini. Anda membayarnya sebagai ganti lisensi untuk menggunakan perangkat lunak pengumpul informasi penyedia likuiditas NeoNix.", "description": "Provides information about the fee that NeoNix takes for swaps. $1 is a decimal number."}, "swapNQuotesWithDot": {"message": "Kuotasi $1.", "description": "$1 is the number of quotes that the user can select from when opening the list of quotes on the 'view quote' screen"}, "swapNewQuoteIn": {"message": "Kuotasi baru di $1", "description": "Tells the user the amount of time until the currently displayed quotes are update. $1 is a time that is counting down from 1:00 to 0:00"}, "swapNoTokensAvailable": {"message": "Token yang cocok dengan $1 tidak tersedia", "description": "Tells the user that a given search string does not match any tokens in our token lists. $1 can be any string of text"}, "swapOnceTransactionHasProcess": {"message": "$1 akan ditambahkan ke akun Anda setelah transaksi ini diproses.", "description": "This message communicates the token that is being transferred. It is shown on the awaiting swap screen. The $1 will be a token symbol."}, "swapPriceDifference": {"message": "<PERSON><PERSON> akan menukar $1 $2 (~$3) untuk $4 $5 (~$6).", "description": "This message represents the price slippage for the swap.  $1 and $4 are a number (ex: 2.89), $2 and $5 are symbols (ex: ETH), and $3 and $6 are fiat currency amounts."}, "swapPriceDifferenceTitle": {"message": "<PERSON><PERSON><PERSON> harga ~$1%", "description": "$1 is a number (ex: 1.23) that represents the price difference."}, "swapPriceUnavailableDescription": {"message": "Dampak harga tidak dapat ditentukan karena kurangnya data harga pasar. <PERSON><PERSON> konfirmasi bahwa Anda setuju dengan jumlah token yang akan Anda terima sebelum penukaran."}, "swapPriceUnavailableTitle": {"message": "<PERSON><PERSON><PERSON> tarif Anda sebelum melanju<PERSON>kan"}, "swapProcessing": {"message": "Memproses"}, "swapQuoteDetails": {"message": "Detail kuotasi"}, "swapQuoteNofM": {"message": "$1 dari $2", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapQuoteSource": {"message": "Sumber kuotasi"}, "swapQuotesExpiredErrorDescription": {"message": "Minta kuotasi baru untuk mendapatkan tarif terbaru."}, "swapQuotesExpiredErrorTitle": {"message": "<PERSON><PERSON>tu kuotasi habis"}, "swapQuotesNotAvailableDescription": {"message": "Rute perdagangan ini tidak tersedia untuk saat ini. <PERSON><PERSON> ubah jum<PERSON>, jar<PERSON><PERSON>, atau token dan kami akan menemukan opsi terbaik."}, "swapQuotesNotAvailableErrorDescription": {"message": "Cobalah untuk menyesuaikan pengaturan selip atau jumlahnya dan coba lagi."}, "swapQuotesNotAvailableErrorTitle": {"message": "Kuotasi tidak tersedia"}, "swapRate": {"message": "<PERSON><PERSON><PERSON>"}, "swapReceiving": {"message": "<PERSON><PERSON><PERSON>"}, "swapReceivingInfoTooltip": {"message": "<PERSON>i hanyalah perkiraan. <PERSON><PERSON><PERSON> yang tepat bergantung pada selip."}, "swapRequestForQuotation": {"message": "Minta kuotasi"}, "swapSelect": {"message": "<PERSON><PERSON><PERSON>"}, "swapSelectAQuote": {"message": "<PERSON><PERSON><PERSON> k<PERSON>"}, "swapSelectAToken": {"message": "<PERSON><PERSON><PERSON> token"}, "swapSelectQuotePopoverDescription": {"message": "Di bawah ini merupakan semua kuotasi yang dikumpulkan dari beberapa sumber likuiditas."}, "swapSelectToken": {"message": "<PERSON><PERSON><PERSON> token"}, "swapShowLatestQuotes": {"message": "<PERSON><PERSON><PERSON><PERSON> kuotasi terbaru"}, "swapSlippageHighDescription": {"message": "Selip yang dimasukkan ($1%) dianggap sangat tinggi dan dapat mengakibatkan tarif yang tidak efektif", "description": "$1 is the amount of % for slippage"}, "swapSlippageHighTitle": {"message": "<PERSON><PERSON>"}, "swapSlippageLowDescription": {"message": "<PERSON><PERSON> serendah ini ($1%) dapat menyebabkan pertukaran gagal", "description": "$1 is the amount of % for slippage"}, "swapSlippageLowTitle": {"message": "<PERSON><PERSON>"}, "swapSlippageNegativeDescription": {"message": "<PERSON><PERSON> harus lebih besar atau sama dengan nol"}, "swapSlippageNegativeTitle": {"message": "Tingkatkan selip untuk melanjutkan"}, "swapSlippageOverLimitDescription": {"message": "Toleransi selip harus 15% atau kurang. <PERSON><PERSON> yang lebih tinggi akan menghasilkan tarif yang tidak efektif."}, "swapSlippageOverLimitTitle": {"message": "<PERSON><PERSON> sangat tinggi"}, "swapSlippagePercent": {"message": "$1%", "description": "$1 is the amount of % for slippage"}, "swapSlippageTooltip": {"message": "Jika harga berubah antara waktu penempatan dan konfirmasi order Anda, ini disebut “selip”. Pertukaran <PERSON>a akan otomatis dibatalkan jika selip melebihi pengaturan “toleransi selip”."}, "swapSlippageZeroDescription": {"message": "<PERSON> lebih sedikit penyedia kuotasi nol selip se<PERSON>ga akan mengh<PERSON>lkan kuotasi yang kurang kompetitif."}, "swapSlippageZeroTitle": {"message": "Sumber penyedia nol selip"}, "swapSource": {"message": "<PERSON>mber likuiditas"}, "swapSuggested": {"message": "<PERSON><PERSON><PERSON><PERSON> yang disarankan"}, "swapSuggestedGasSettingToolTipMessage": {"message": "<PERSON>tukara<PERSON> adalah transaksi yang kompleks dan sensitif terhadap waktu. <PERSON><PERSON>n biaya gas ini untuk keseimbangan yang baik antara biaya dan konfidensi dari keber<PERSON>n <PERSON>."}, "swapSwapFrom": {"message": "<PERSON><PERSON> dari"}, "swapSwapSwitch": {"message": "Alihkan order token"}, "swapSwapTo": {"message": "<PERSON><PERSON> ke"}, "swapToConfirmWithHwWallet": {"message": "untuk mengonfirmasikan dengan dompet perangkat keras Anda"}, "swapTokenAddedManuallyDescription": {"message": "Verifikasikan token ini di $1 dan pastikan ini adalah token yang ingin Anda perdagangkan.", "description": "$1 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenAddedManuallyTitle": {"message": "Token ditambahkan secara manual"}, "swapTokenAvailable": {"message": "$1 telah ditambahkan ke akun Anda.", "description": "This message is shown after a swap is successful and communicates the exact amount of tokens the user has received for a swap. The $1 is a decimal number of tokens followed by the token symbol."}, "swapTokenBalanceUnavailable": {"message": "Kami tidak dapat mengambil saldo $1 Anda", "description": "This message communicates to the user that their balance of a given token is currently unavailable. $1 will be replaced by a token symbol"}, "swapTokenNotAvailable": {"message": "Token tidak tersedia untuk ditukar di wilayah ini"}, "swapTokenToToken": {"message": "Tukar $1 ke $2", "description": "Used in the transaction display list to describe a swap. $1 and $2 are the symbols of tokens in involved in a swap."}, "swapTokenVerifiedOn1SourceDescription": {"message": "$1 hanya diverifikasi di 1 sumber. Pertimbangkan untuk memverifikasinya di $2 sebelum melanjutkan.", "description": "$1 is a token name, $2 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenVerifiedOn1SourceTitle": {"message": "Token berpotensi tidak autentik"}, "swapTokenVerifiedSources": {"message": "Dikonfirmasi oleh $1 sumber. Verifikasikan pada $2.", "description": "$1 the number of sources that have verified the token, $2 points the user to a block explorer as a place they can verify information about the token."}, "swapTooManyDecimalsError": {"message": "$1 memungkinkan hingga $2 desimal", "description": "$1 is a token symbol and $2 is the max. number of decimals allowed for the token"}, "swapTransactionComplete": {"message": "Transaksi <PERSON>"}, "swapTwoTransactions": {"message": "2 transaksi"}, "swapUnknown": {"message": "Tidak diketahui"}, "swapZeroSlippage": {"message": "Selip 0%"}, "swapsMaxSlippage": {"message": "Toler<PERSON>i selip"}, "swapsNotEnoughToken": {"message": "$1 tidak cukup", "description": "Tells the user that they don't have enough of a token for a proposed swap. $1 is a token symbol"}, "swapsViewInActivity": {"message": "Lihat di aktivitas"}, "switch": {"message": "<PERSON><PERSON><PERSON>"}, "switchEthereumChainConfirmationDescription": {"message": "Ini akan mengalihkan jaringan yang dipilih dalam NeoNix ke jaringan yang ditambahkan sebelumnya:"}, "switchEthereumChainConfirmationTitle": {"message": "Izinkan situs ini untuk beralih jaringan?"}, "switchInputCurrency": {"message": "Ganti mata uang input"}, "switchNetwork": {"message": "<PERSON><PERSON><PERSON>"}, "switchNetworks": {"message": "<PERSON><PERSON><PERSON>"}, "switchToNetwork": {"message": "Beralih ke $1", "description": "$1 represents the custom network that has previously been added"}, "switchToThisAccount": {"message": "<PERSON><PERSON><PERSON> ke akun ini"}, "switchedNetworkToastDecline": {"message": "<PERSON>an tampilkan lagi"}, "switchedNetworkToastMessage": {"message": "$1 kini telah aktif di $2", "description": "$1 represents the account name, $2 represents the network name"}, "switchedNetworkToastMessageNoOrigin": {"message": "Saat ini Anda menggunakan $1", "description": "$1 represents the network name"}, "switchingNetworksCancelsPendingConfirmations": {"message": "Mengalihkan jaringan akan membatalkan semua konfirmasi yang berstatus menunggu"}, "symbol": {"message": "Simbol"}, "symbolBetweenZeroTwelve": {"message": "Simbol harus terdiri dari 11 karakter atau lebih sedikit."}, "tenPercentIncreased": {"message": "Meningkat 10%"}, "terms": {"message": "<PERSON><PERSON><PERSON><PERSON> pengg<PERSON>an"}, "termsOfService": {"message": "<PERSON><PERSON><PERSON><PERSON> layanan"}, "termsOfUseAgreeText": {"message": " <PERSON><PERSON>, yang berlaku untuk penggunaan atas NeoNix dan semua fiturnya"}, "termsOfUseFooterText": {"message": "Gulirkan layar untuk membaca semua bagian"}, "termsOfUseTitle": {"message": "<PERSON><PERSON><PERSON> kami telah diperbarui"}, "testNetworks": {"message": "<PERSON><PERSON><PERSON>"}, "testnets": {"message": "Testnet"}, "theme": {"message": " <PERSON><PERSON>, yang berlaku untuk penggunaan atas NeoNix dan semua fiturnya"}, "themeDescription": {"message": "<PERSON><PERSON><PERSON> tema NeoNix yang <PERSON>a sukai."}, "thirdPartySoftware": {"message": "Pemberitahuan perangkat lunak pihak ketiga", "description": "Title of a popup modal displayed when installing a snap for the first time."}, "time": {"message": "<PERSON><PERSON><PERSON>"}, "tipsForUsingAWallet": {"message": "Tip mengg<PERSON>kan dompet"}, "tipsForUsingAWalletDescription": {"message": "Menambahkan token membuka lebih banyak cara untuk menggunakan web3."}, "to": {"message": "Untuk"}, "toAddress": {"message": "Ke: $1", "description": "$1 is the address to include in the To label. It is typically shortened first using shortenAddress"}, "toggleDecodeDescription": {"message": "Kami menggunakan layanan 4byte.directory dan Sourcify untuk mengonversi kode dan menampilkan data transaksi yang lebih mudah dibaca. Ini membantu Anda memahami hasil transaksi yang tertunda dan yang sudah lewat, tetapi alamat IP Anda berpotensi tersebar."}, "token": {"message": "Token"}, "tokenAddress": {"message": "<PERSON><PERSON><PERSON> token"}, "tokenAlreadyAdded": {"message": "Token telah ditambahkan."}, "tokenAutoDetection": {"message": "<PERSON>ek<PERSON> otomatis token"}, "tokenContractAddress": {"message": "<PERSON><PERSON><PERSON> k<PERSON>"}, "tokenDecimal": {"message": "Desimal token"}, "tokenDecimalFetchFailed": {"message": "Desimal token diperlukan. Temukan di: $1"}, "tokenDetails": {"message": "Detail token"}, "tokenFoundTitle": {"message": "1 token baru di<PERSON>ukan"}, "tokenId": {"message": "ID token"}, "tokenList": {"message": "Daftar token"}, "tokenMarketplace": {"message": "Pasar token"}, "tokenScamSecurityRisk": {"message": "penipuan dan risiko keamanan token"}, "tokenStandard": {"message": "Standar token"}, "tokenSymbol": {"message": "Simbol token"}, "tokens": {"message": "Token"}, "tokensFoundTitle": {"message": "$1 token baru di<PERSON>ukan", "description": "$1 is the number of new tokens detected"}, "tokensInCollection": {"message": "Token dalam koleksi"}, "tooltipApproveButton": {"message": "<PERSON><PERSON>"}, "tooltipSatusConnected": {"message": "te<PERSON><PERSON><PERSON><PERSON>"}, "tooltipSatusConnectedUpperCase": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tooltipSatusNotConnected": {"message": "tidak terhubung"}, "total": {"message": "Total"}, "totalVolume": {"message": "Volume total"}, "transaction": {"message": "<PERSON><PERSON><PERSON>"}, "transactionCancelAttempted": {"message": "Pembatalan transaksi diupayakan dengan biaya gas sebesar $1 pada $2"}, "transactionCancelSuccess": {"message": "Transaksi berhasil dibatalkan pada $2"}, "transactionConfirmed": {"message": "Transaksi dikonfirmasi pada $2."}, "transactionCreated": {"message": "Transaksi dibuat dengan nilai sebesar $1 pada $2."}, "transactionDataFunction": {"message": "<PERSON><PERSON><PERSON>"}, "transactionDetailGasHeading": {"message": "Estimasi biaya gas"}, "transactionDetailMultiLayerTotalSubtitle": {"message": "Jumlah + biaya"}, "transactionDropped": {"message": "Transaksi terputus pada $2."}, "transactionError": {"message": "Kesalahan transaksi. Pengecualian diberikan dalam kode kontrak."}, "transactionErrorNoContract": {"message": "Mencoba memanggil fungsi pada alamat non<PERSON>."}, "transactionErrored": {"message": "<PERSON><PERSON><PERSON> men<PERSON> k<PERSON>han."}, "transactionFlowNetwork": {"message": "<PERSON><PERSON><PERSON>"}, "transactionHistoryBaseFee": {"message": "<PERSON><PERSON><PERSON> (GWEI)"}, "transactionHistoryL1GasLabel": {"message": "Total biaya gas L1"}, "transactionHistoryL2GasLimitLabel": {"message": "Batas gas L2"}, "transactionHistoryL2GasPriceLabel": {"message": "Harga gas L2"}, "transactionHistoryMaxFeePerGas": {"message": "Biaya maks per gas"}, "transactionHistoryPriorityFee": {"message": "Biaya prioritas (GWEI)"}, "transactionHistoryTotalGasFee": {"message": "Total biaya gas"}, "transactionIdLabel": {"message": "ID Transaksi", "description": "Label for the source transaction ID field."}, "transactionIncludesTypes": {"message": "Transaksi ini termasuk: $1."}, "transactionResubmitted": {"message": "Transaksi dikirim kembali dengan estimasi biaya gas naik $1 pada $2"}, "transactionSettings": {"message": "Pengat<PERSON><PERSON> trans<PERSON>i"}, "transactionSubmitted": {"message": "Transaksi dikirim dengan estimasi biaya gas sebesar $1 pada $2."}, "transactionTotalGasFee": {"message": "Total biaya gas", "description": "Label for the total gas fee incurred in the transaction."}, "transactionUpdated": {"message": "Transaksi diperbarui pada $2."}, "transactions": {"message": "Transaksi"}, "transfer": {"message": "Transfer"}, "transferCrypto": {"message": "Transfer kripto"}, "transferFrom": {"message": "Transfer dari"}, "transferRequest": {"message": "Permintaan transfer"}, "trillionAbbreviation": {"message": "T", "description": "Shortened form of 'trillion'"}, "troubleConnectingToLedgerU2FOnFirefox": {"message": "<PERSON><PERSON> mengalami masalah saat menghubungkan Ledger Anda. $1", "description": "$1 is a link to the wallet connection guide;"}, "troubleConnectingToLedgerU2FOnFirefox2": {"message": "Tinjau panduan koneksi dompet perangkat keras kami dan coba lagi.", "description": "$1 of the ledger wallet connection guide"}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution": {"message": "Jika menggunakan Firefox versi terbaru, <PERSON><PERSON> da<PERSON>t mengalami masalah terkait Firefox yang menghentikan dukungan U2F. Pelajari cara memperbaiki masalah ini $1.", "description": "It is a link to the ledger website for the workaround."}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution2": {"message": "di sini", "description": "Second part of the error message; It is a link to the ledger website for the workaround."}, "troubleConnectingToWallet": {"message": "<PERSON><PERSON> mengalami masalah saat mencoba terhubung ke $1 Anda, tinjau kembali $2 dan coba lagi.", "description": "$1 is the wallet device name; $2 is a link to wallet connection guide"}, "troubleStarting": {"message": "NeoNix mengalami masalah saat memulai. Kesalahan ini dapat terjadi berselang, coba mulai ulang ekstensi."}, "tryAgain": {"message": "Coba lagi"}, "turnOff": {"message": "Nonaktifkan"}, "turnOffNeoNixNotificationsError": {"message": "<PERSON><PERSON><PERSON><PERSON> kesalahan saat menonaktifkan notifikasi. Coba lagi nanti."}, "turnOn": {"message": "Aktifkan"}, "turnOnNeoNixNotifications": {"message": "Aktifkan notifikasi"}, "turnOnNeoNixNotificationsButton": {"message": "Aktifkan"}, "turnOnNeoNixNotificationsError": {"message": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuat notifikasi. Coba lagi nanti."}, "turnOnNeoNixNotificationsMessageFirst": {"message": "Pan<PERSON>u terus segala yang terjadi di dompet Anda dengan notifikasi."}, "turnOnNeoNixNotificationsMessagePrivacyBold": {"message": "pengat<PERSON><PERSON> notifikasi."}, "turnOnNeoNixNotificationsMessagePrivacyLink": {"message": "<PERSON><PERSON><PERSON>i cara kami melindungi privasi Anda saat menggunakan fitur ini."}, "turnOnNeoNixNotificationsMessageSecond": {"message": "Untuk menggunakan notifikasi dompet, kami menggunakan profil untuk menyinkronkan beberapa pengaturan di seluruh perangkat Anda. $1"}, "turnOnNeoNixNotificationsMessageThird": {"message": "Notifikasi dapat dinonaktifkan setiap saat di $1"}, "turnOnTokenDetection": {"message": "Aktifkan deteksi token yang ditingkatkan"}, "tutorial": {"message": "Tutorial"}, "twelveHrTitle": {"message": "12 j:"}, "u2f": {"message": "U2F", "description": "A name on an API for the browser to interact with devices that support the U2F protocol. On some browsers we use it to connect NeoNix to Ledger devices."}, "unapproved": {"message": "Tidak disetujui"}, "unexpectedBehavior": {"message": "Perilaku ini tidak terduga dan harus dilaporkan sebagai bug, meskipun akun Anda telah dipulihkan dengan benar. <PERSON><PERSON><PERSON> tautan di bawah ini untuk mengirim laporan bug ke NeoNix."}, "units": {"message": "unit"}, "unknown": {"message": "Tidak diketahui"}, "unknownCollection": {"message": "<PERSON><PERSON><PERSON><PERSON> tanpa nama"}, "unknownNetworkForKeyEntropy": {"message": "<PERSON><PERSON><PERSON> tidak di<PERSON>al", "description": "Displayed on places like Snap install warning when regular name is not available."}, "unknownQrCode": {"message": "Kesalahan: <PERSON><PERSON> tidak dapat mengidentifikasi kode QR itu"}, "unlimited": {"message": "Tidak terbatas"}, "unlock": {"message": "<PERSON><PERSON>"}, "unpin": {"message": "<PERSON><PERSON> semat"}, "unrecognizedChain": {"message": "<PERSON>aringan kustom ini tidak dikenali", "description": "$1 is a clickable link with text defined by the 'unrecognizedChanLinkText' key. The link will open to instructions for users to validate custom network details."}, "unsendableAsset": {"message": "Tidak mendukung pengiriman token NFT (ERC-721) untuk saat ini", "description": "This is an error message we show the user if they attempt to send an NFT asset type, for which currently don't support sending"}, "unstableTokenPriceDescription": {"message": "Harga token ini dalam USD sangat fluktuatif, menunjukkan risiko tinggi kehilangan nilai yang signifikan saat berinteraksi dengannya."}, "unstableTokenPriceTitle": {"message": "Harga Token Tidak Stabil"}, "upArrow": {"message": "panah naik"}, "update": {"message": "<PERSON><PERSON><PERSON>"}, "updateEthereumChainConfirmationDescription": {"message": "Situs ini meminta pembaruan URL jaringan default Anda. Anda dapat mengedit informasi default dan jaringan setiap saat."}, "updateNetworkConfirmationTitle": {"message": "Perbarui $1", "description": "$1 represents network name"}, "updateOrEditNetworkInformations": {"message": "Perbarui informasi <PERSON>"}, "updateRequest": {"message": "Permintaan pembaruan"}, "updatedRpcForNetworks": {"message": "RPC <PERSON><PERSON><PERSON>"}, "uploadDropFile": {"message": "Letakkan fail di sini"}, "uploadFile": {"message": "<PERSON><PERSON><PERSON> fail"}, "urlErrorMsg": {"message": "URL memerlukan awalan HTTP/HTTPS yang sesuai."}, "use4ByteResolution": {"message": "Uraikan kode kontrak cerdas"}, "useMultiAccountBalanceChecker": {"message": "Kelompokkan permintaan saldo akun"}, "useMultiAccountBalanceCheckerSettingDescription": {"message": "Dapatkan pembaruan saldo yang lebih cepat dengan mengelompokkan permintaan saldo akun. Ini membantu kami untuk mengambil saldo akun Anda secara be<PERSON>, se<PERSON><PERSON> Anda mendapatkan pembaruan lebih cepat untuk pengalaman yang lebih baik. Saat fitur ini dinonaktifkan, pihak ketiga kemungkinan kecil akan menghubungkan akun Anda yang satu dengan yang lainnya."}, "useNftDetection": {"message": "Deteksi otomatis NFT"}, "useNftDetectionDescriptionText": {"message": "Izinkan NeoNix menambahkan NFT yang Anda miliki menggunakan layanan pihak ketiga. Autodeteksi NFT mengekspos alamat IP dan akun Anda ke layanan ini. Mengaktifkan fitur ini dapat mengaitkan alamat IP dengan alamat Ethereum Anda dan menampilkan NFT palsu yang di-airdrop oleh penipu. Anda dapat menambahkan token secara manual untuk menghindari risiko ini."}, "usePhishingDetection": {"message": "<PERSON><PERSON><PERSON> dete<PERSON>i phishing"}, "usePhishingDetectionDescription": {"message": "Menampilkan peringatan untuk domain phishing yang menargetkan pengguna Ethereum"}, "useSafeChainsListValidation": {"message": "Pemeriksaan detail jaringan"}, "useSafeChainsListValidationDescription": {"message": "NeoNix menggunakan layanan pihak ketiga yang disebut $1 untuk menampilkan detail jaringan yang akurat dan terstandardisasi. Hal ini dapat mengurangi kemungkinan Anda untuk terhubung ke jaringan berbahaya atau salah. Saat menggunakan fitur ini, alamat IP akan diketahui oleh chainid.network."}, "useSafeChainsListValidationWebsite": {"message": "chainid.network", "description": "useSafeChainsListValidationWebsite is separated from the rest of the text so that we can bold the third party service name in the middle of them"}, "useTokenDetectionPrivacyDesc": {"message": "Menampilkan token yang dikirim ke akun Anda secara otomatis yang melibatkan komunikasi dengan server pihak ketiga untuk mengambil gambar token. Server tersebut akan memiliki akses ke alamat IP Anda."}, "usedByClients": {"message": "<PERSON><PERSON><PERSON><PERSON> oleh berbagai klien yang berbeda"}, "userName": {"message": "<PERSON><PERSON>"}, "userOpContractDeployError": {"message": "Penerapan kontrak dari akun kontrak cerdas tidak didukung"}, "version": {"message": "<PERSON><PERSON><PERSON>"}, "view": {"message": "Lihat"}, "viewActivity": {"message": "Lihat aktivitas"}, "viewAllQuotes": {"message": "lihat semua kuotasi"}, "viewContact": {"message": "<PERSON><PERSON> k<PERSON>"}, "viewDetails": {"message": "Lihat detail"}, "viewMore": {"message": "<PERSON>hat selengka<PERSON>"}, "viewOnBlockExplorer": {"message": "Lihat di block explorer"}, "viewOnCustomBlockExplorer": {"message": "Lihat $1 di $2", "description": "$1 is the action type. e.g (Account, Transaction, Swap) and $2 is the Custom Block Explorer URL"}, "viewOnEtherscan": {"message": "Lihat $1 di Etherscan", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "viewOnExplorer": {"message": "Li<PERSON> pada explorer"}, "viewOnOpensea": {"message": "Lihat di Opensea"}, "viewSolanaAccount": {"message": "<PERSON><PERSON> a<PERSON>"}, "viewTransaction": {"message": "<PERSON><PERSON>"}, "viewinExplorer": {"message": "Lihat $1 di explorer", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "visitSite": {"message": "<PERSON><PERSON><PERSON><PERSON> situs"}, "visitSupportDataConsentModalAccept": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "visitSupportDataConsentModalDescription": {"message": "Ingin membagikan Pengidentifikasi NeoNix dan versi aplikasi dengan P<PERSON>t Du<PERSON>ngan kami? Ini dapat membantu kami menyelesaikan masalah Anda dengan lebih baik, tetapi tindakan ini bersifat opsional."}, "visitSupportDataConsentModalReject": {"message": "<PERSON><PERSON>n"}, "visitSupportDataConsentModalTitle": {"message": "Bagikan detail perangkat kepada dukungan"}, "visitWebSite": {"message": "Kunjungi situs web kami"}, "wallet": {"message": "Dompet"}, "walletConnectionGuide": {"message": "panduan koneksi dompet perangkat keras kami"}, "wantToAddThisNetwork": {"message": "Ingin menambahkan jaringan ini?"}, "wantsToAddThisAsset": {"message": "Ini memungkinkan aset untuk ditambahkan ke dompet Anda."}, "warning": {"message": "Peringatan"}, "warningFromSnap": {"message": "Peringatan dari $1", "description": "$1 represents the name of the snap"}, "watchEthereumAccountsDescription": {"message": "Mengaktifkan opsi ini akan memberi Anda kemampuan untuk memantau akun Ethereum melalui alamat publik atau nama ENS. Untuk masukan tentang fitur Beta ini, silakan isi formulir $1 ini.", "description": "$1 is the link to a product feedback form"}, "watchEthereumAccountsToggle": {"message": "Pantau Akun Ethereum (Beta)"}, "watchOutMessage": {"message": "Waspadalah terhadap $1.", "description": "$1 is a link with text that is provided by the 'securityMessageLinkForNetworks' key"}, "weak": {"message": "Lemah"}, "web3": {"message": "Web3"}, "web3ShimUsageNotification": {"message": "Ka<PERSON> melihat situs web saat ini mencoba menggunakan API window.web3 yang dihapus. Jika situs tersebut tampak bermasalah, klik $1 untuk informasi selengkapnya.", "description": "$1 is a clickable link."}, "webhid": {"message": "WebHID", "description": "Refers to a interface for connecting external devices to the browser. Used for connecting ledger to the browser. Read more here https://developer.mozilla.org/en-US/docs/Web/API/WebHID_API"}, "websites": {"message": "situs web", "description": "Used in the 'permission_rpc' message."}, "welcomeBack": {"message": "Selamat datang kembali"}, "welcomeToNeoNix": {"message": "Mari kita mulai"}, "whatsThis": {"message": "Apa ini?"}, "willApproveAmountForBridging": {"message": "Ini akan menyetujui $1 untuk bridge."}, "willApproveAmountForBridgingHardware": {"message": "<PERSON>a perlu mengon<PERSON><PERSON><PERSON>an dua transaksi pada dompet perangkat keras."}, "withdrawing": {"message": "Penarikan"}, "wrongNetworkName": {"message": "<PERSON><PERSON><PERSON> catatan kami, nama jaringan mungkin tidak cocok dengan ID chain ini."}, "yes": {"message": "Ya"}, "you": {"message": "<PERSON><PERSON>"}, "youDeclinedTheTransaction": {"message": "<PERSON><PERSON> menolak trans<PERSON>i tersebut."}, "youNeedToAllowCameraAccess": {"message": "Anda harus mengizinkan akses kamera untuk menggunakan fitur ini."}, "youReceived": {"message": "<PERSON><PERSON>", "description": "Label indicating the amount and asset the user received."}, "youSent": {"message": "<PERSON><PERSON>", "description": "Label indicating the amount and asset the user sent."}, "yourAccounts": {"message": "<PERSON><PERSON><PERSON>"}, "yourActivity": {"message": "Aktivitas Anda"}, "yourBalance": {"message": "<PERSON><PERSON>"}, "yourNFTmayBeAtRisk": {"message": "NFT <PERSON>a mungkin berisiko"}, "yourNetworks": {"message": "<PERSON><PERSON><PERSON>"}, "yourPrivateSeedPhrase": {"message": "Frasa Pemulihan <PERSON> pribadi <PERSON>a"}, "yourTransactionConfirmed": {"message": "Transaksi telah dikonfi<PERSON>"}, "yourTransactionJustConfirmed": {"message": "<PERSON><PERSON> tidak dapat membatalkan transaksi Anda sebelum dikonfirmasi di blockchain."}, "yourWalletIsReady": {"message": "<PERSON><PERSON> Anda sudah siap"}}