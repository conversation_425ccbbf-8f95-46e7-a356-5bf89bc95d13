// no destructuring as process.env detection stops working
// eslint-disable-next-line prefer-destructuring
export const SUPPORT_LINK = process.env.SUPPORT_LINK;

export const COINGECKO_LINK = 'https://www.coingecko.com/';
export const CRYPTOCOMPARE_LINK = 'https://www.cryptocompare.com/';
export const PRIVACY_POLICY_LINK = 'https://nnxscan.io';
export const SURVEY_LINK = 'https://www.getfeedback.com/r/Oczu1vP0';

// TODO make sure these links are correct
export const ETHERSCAN_PRIVACY_LINK = 'https://etherscan.io/privacyPolicy';
export const CONSENSYS_PRIVACY_LINK = 'https://nnxscan.io';
export const AUTO_DETECT_TOKEN_LEARN_MORE_LINK =
  'https://nnxscan.io';
export const CONSENSYS_TERMS_OF_USE = 'https://nnxscan.io';

export const SECURITY_ALERTS_LEARN_MORE_LINK =
  'https://support.metamask.io/privacy-and-security/how-to-turn-on-security-alerts/';

export const TRANSACTION_SIMULATIONS_LEARN_MORE_LINK =
  'https://support.metamask.io/transactions-and-gas/transactions/simulations/';

export const GAS_FEES_LEARN_MORE_URL =
  'https://community.metamask.io/t/what-is-gas-why-do-transactions-take-so-long/3172';

export const SMART_ACCOUNT_INFO_LINK =
  'https://support.metamask.io/configure/accounts/what-is-a-smart-account';

export const VAULT_RECOVERY_LINK = `https://support.metamask.io/configure/wallet/how-to-recover-your-secret-recovery-phrase/#step-two-locate-your-vault`;
