{"QRHardwareInvalidTransactionTitle": {"message": "오류"}, "QRHardwareMismatchedSignId": {"message": "일치하지 않는 트랜잭션 데이터. 트랜잭션 세부 정보를 확인하세요."}, "QRHardwarePubkeyAccountOutOfRange": {"message": "더 이상 계정이 없습니다. 아래 목록에 없는 다른 계정에 액세스하려면 하드웨어 지갑을 다시 연결하고 선택하세요."}, "QRHardwareScanInstructions": {"message": "QR 코드를 카메라 앞에 놓으세요. 화면이 흐릿하지만 판독에 영향을 미치지 않습니다."}, "QRHardwareSignRequestCancel": {"message": "거부"}, "QRHardwareSignRequestDescription": {"message": "지갑으로 가입한 후 '서명 받기'를 클릭하여 서명을 받으세요."}, "QRHardwareSignRequestGetSignature": {"message": "서명 받기"}, "QRHardwareSignRequestSubtitle": {"message": "지갑으로 QR 코드 스캔"}, "QRHardwareSignRequestTitle": {"message": "서명 요청"}, "QRHardwareUnknownQRCodeTitle": {"message": "오류"}, "QRHardwareUnknownWalletQRCode": {"message": "잘못된 QR 코드입니다. 하드웨어 지갑의 동기화 QR 코드를 스캔하세요."}, "QRHardwareWalletImporterTitle": {"message": "QR 코드 스캔"}, "QRHardwareWalletSteps1Description": {"message": "아래의 QR 코드 공식 지원 협력업체 목록에서 선택할 수 있습니다."}, "QRHardwareWalletSteps1Title": {"message": "QR 하드웨어 지갑을 연결하세요"}, "QRHardwareWalletSteps2Description": {"message": "N그레이브 제로"}, "SrpListHideAccounts": {"message": "계정 $1개 숨기기", "description": "$1 is the number of accounts"}, "SrpListHideSingleAccount": {"message": "계정 1개 숨기기"}, "SrpListShowAccounts": {"message": "계정 $1개 표시", "description": "$1 is the number of accounts"}, "SrpListShowSingleAccount": {"message": "계정 1개 표시"}, "about": {"message": "정보"}, "accept": {"message": "동의"}, "acceptTermsOfUse": {"message": "$1의 내용을 읽고 이에 동의합니다", "description": "$1 is the `terms` message"}, "accessingYourCamera": {"message": "카메라에 접근 중..."}, "account": {"message": "계정"}, "accountActivity": {"message": "계정 활동"}, "accountActivityText": {"message": "알림을 수신할 계정을 선택하세요."}, "accountDetails": {"message": "계정 세부 정보"}, "accountIdenticon": {"message": "계정 식별 아이콘"}, "accountIsntConnectedToastText": {"message": "$1은(는) $2에 연결되지 않았습니다"}, "accountName": {"message": "계정 이름"}, "accountNameDuplicate": {"message": "이 계정 이름은 이미 존재합니다.", "description": "This is an error message shown when the user enters a new account name that matches an existing account name"}, "accountNameReserved": {"message": "이 계정 이름은 예약되었습니다", "description": "This is an error message shown when the user enters a new account name that is reserved for future use"}, "accountOptions": {"message": "계정 옵션"}, "accountPermissionToast": {"message": "계정 권한이 업데이트됨"}, "accountSelectionRequired": {"message": "계정을 선택해야 합니다!"}, "accountTypeNotSupported": {"message": "지원하지 않는 계정 유형"}, "accounts": {"message": "계정"}, "accountsConnected": {"message": "계정 연결됨"}, "accountsPermissionsTitle": {"message": "계정 보기 및 트랜잭션 제안"}, "accountsSmallCase": {"message": "계정"}, "active": {"message": "활성"}, "activity": {"message": "활동"}, "activityLog": {"message": "활동 로그"}, "add": {"message": "추가"}, "addACustomNetwork": {"message": "사용자 지정 네트워크 추가"}, "addANetwork": {"message": "네트워크 추가"}, "addANickname": {"message": "닉네임 추가"}, "addAUrl": {"message": "URL 추가"}, "addAccount": {"message": "계정 추가"}, "addAccountFromNetwork": {"message": "$1 계정 추가", "description": "$1 is the network name, e.g. Bitcoin or Solana"}, "addAccountToNeoNix": {"message": "NeoNix에 계정 추가"}, "addAcquiredTokens": {"message": "NeoNix를 이용해 얻은 토큰 추가"}, "addAlias": {"message": "별칭 추가"}, "addBitcoinAccountLabel": {"message": "비트코인 계정"}, "addBlockExplorer": {"message": "블록 탐색기 추가"}, "addBlockExplorerUrl": {"message": "블록 탐색기 URL 추가"}, "addContact": {"message": "연락처 추가"}, "addCustomNetwork": {"message": "맞춤 네트워크 추가"}, "addEthereumChainWarningModalHeader": {"message": "신뢰할 수 있는 경우에만 이 RPC 공급입체를 추가하세요. $1", "description": "$1 is addEthereumChainWarningModalHeaderPartTwo passed separately so that it can be bolded"}, "addEthereumChainWarningModalHeaderPartTwo": {"message": "악성 공급업체는 블록체인 상태와 네트워크 활동을 거짓으로 보고할 수 있습니다."}, "addEthereumChainWarningModalListHeader": {"message": "다음과 같은 권한이 부여되기에 신뢰할 수 있는 공급업체만 추가해야 합니다."}, "addEthereumChainWarningModalListPointOne": {"message": "사용자의 계정과 IP 주소를 확인하고 서로를 연결하세요"}, "addEthereumChainWarningModalListPointThree": {"message": "계정 잔액 및 기타 블록체인 상황 표시"}, "addEthereumChainWarningModalListPointTwo": {"message": "트랜잭션을 브로드캐스팅합니다"}, "addEthereumChainWarningModalTitle": {"message": "이더리움 메인넷에 새로운 RPC 공급업체를 추가하려고 합니다"}, "addEthereumWatchOnlyAccount": {"message": "이더리움 계정 모니터(베타)"}, "addFriendsAndAddresses": {"message": "신뢰하는 친구 및 주소 추가"}, "addHardwareWalletLabel": {"message": "하드웨어 지갑"}, "addIPFSGateway": {"message": "선호하는 IPFS 게이트웨이를 추가하세요"}, "addImportAccount": {"message": "계정 또는 하드웨어 지갑 추가"}, "addMemo": {"message": "메모 추가"}, "addNetwork": {"message": "네트워크 추가"}, "addNetworkConfirmationTitle": {"message": "$1 추가", "description": "$1 represents network name"}, "addNewAccount": {"message": "새 이더리움 계정 추가"}, "addNewEthereumAccountLabel": {"message": "이더리움 계정"}, "addNewSolanaAccountLabel": {"message": "솔라나 계정"}, "addNft": {"message": "NFT 추가"}, "addNfts": {"message": "NFT 추가"}, "addNonEvmAccount": {"message": "$1 계정 추가", "description": "$1 is the non EVM network where the account is going to be created, e.g. Bitcoin or Solana"}, "addNonEvmAccountFromNetworkPicker": {"message": "$1 네트워크를 활성화하려면 $2 계정을 만들어야 합니다.", "description": "$1 is the non EVM network where the account is going to be created, e.g. Solana Mainnet or Solana Devnet. $2 is the account type, e.g. Bitcoin or Solana"}, "addRpcUrl": {"message": "RPC URL 추가"}, "addSnapAccountToggle": {"message": "\"계정 Snap 추가(베타)\" 활성화"}, "addSnapAccountsDescription": {"message": "이 기능을 사용하면 계정 목록에서 바로 새 베타 계정 Snap을 추가할 수 있습니다. 설치하는 계정 Snap은 타사 서비스라는 점을 명심하세요."}, "addSuggestedNFTs": {"message": "추천 NFT 추가"}, "addSuggestedTokens": {"message": "추천 토큰 추가"}, "addToken": {"message": "토큰 추가"}, "addTokenByContractAddress": {"message": "이 토큰을 찾을 수 없으신가요? 토큰 주소를 붙여넣으면 토큰을 직접 추가할 수 있습니다. 토큰의 계약 주소는 $1에서 찾을 수 있습니다", "description": "$1 is a blockchain explorer for a specific network, e.g. Etherscan for Ethereum"}, "addUrl": {"message": "URL 추가"}, "addingAccount": {"message": "계정 추가"}, "addingCustomNetwork": {"message": "네트워크 추가"}, "additionalNetworks": {"message": "추가 네트워크"}, "address": {"message": "주소"}, "addressCopied": {"message": "주소 복사 완료!"}, "addressMismatch": {"message": "사이트 주소 불일치"}, "addressMismatchOriginal": {"message": "현재 URL: $1", "description": "$1 replaced by origin URL in confirmation request"}, "addressMismatchPunycode": {"message": "Punycode 버전: $1", "description": "$1 replaced by punycode version of the URL in confirmation request"}, "advanced": {"message": "고급"}, "advancedBaseGasFeeToolTip": {"message": "트랜잭션이 블록에 포함되면 최대 기본 요금과 실제 기본 요금 간의 차액이 환불됩니다. 총 금액은 최대 기본 요금(GWEI 단위) 곱하기 가스 한도로 계산합니다."}, "advancedDetailsDataDesc": {"message": "데이터"}, "advancedDetailsHexDesc": {"message": "16진수"}, "advancedDetailsNonceDesc": {"message": "논스"}, "advancedDetailsNonceTooltip": {"message": "계정의 트랜잭션 번호입니다. 첫 트랜잭션의 논스는 0이며 이는 순차적으로 증가합니다."}, "advancedGasFeeDefaultOptIn": {"message": "이 수치를 $1 네트워크의 기본값으로 저장합니다.", "description": "$1 is the current network name."}, "advancedGasFeeModalTitle": {"message": "고급 가스비"}, "advancedGasPriceTitle": {"message": "가스 가격"}, "advancedPriorityFeeToolTip": {"message": "우선 요금(일명 \"채굴자 팁\")이란 내 트랜잭션을 우선 거래한 것에 대한 인센티브로 채굴자에게 직접 전달되는 금액입니다."}, "airDropPatternDescription": {"message": "해당 토큰의 온체인 기록에서 의심스러운 에어드롭 활동이 확인되었습니다."}, "airDropPatternTitle": {"message": "에어드롭 패턴"}, "airgapVault": {"message": "에어갭 볼트"}, "alert": {"message": "경고"}, "alertAccountTypeUpgradeMessage": {"message": "스마트 계정으로 업그레이드 중입니다. 계정 주소는 그대로 유지되며, 더욱 빠른 속도의 트랜잭션을 더 낮은 네트워크 수수료로 이용할 수 있습니다. $1."}, "alertAccountTypeUpgradeTitle": {"message": "계정 유형"}, "alertActionBuyWithNativeCurrency": {"message": "$1 매수"}, "alertActionUpdateGas": {"message": "가스 한도 업데이트"}, "alertActionUpdateGasFee": {"message": "수수료 업데이트"}, "alertActionUpdateGasFeeLevel": {"message": "가스 옵션 업데이트"}, "alertDisableTooltip": {"message": "\"설정 > 경고\"에서 변경할 수 있습니다"}, "alertMessageAddressMismatchWarning": {"message": "공격자는 사이트 주소를 약간 변경하여 유사 사이트를 만들기도 합니다. 계속하기 전에 정상적인 사이트와 상호 작용하고 있는지 확인하세요."}, "alertMessageChangeInSimulationResults": {"message": "이 트랜잭션의 예상 변경 사항이 업데이트되었습니다. 계속하기 전에 자세히 검토하세요."}, "alertMessageFirstTimeInteraction": {"message": "이 주소와 처음으로 상호 작용합니다. 계속하기 전에 주소가 올바른지 확인하세요."}, "alertMessageGasEstimateFailed": {"message": "정확한 수수료를 제공할 수 없으며 예상 수수료가 높을 수 있습니다. 사용자 지정 가스 한도를 입력하는 것이 좋지만 트랜잭션이 여전히 실패할 위험이 있습니다."}, "alertMessageGasFeeLow": {"message": "낮은 수수료를 선택하면 트랜잭션 속도가 느려지고 대기 시간이 길어집니다. 트랜잭션 속도를 높이려면 시장 수수료 또는 공격적 수수료 옵션을 선택하세요."}, "alertMessageGasTooLow": {"message": "이 트랜잭션을 계속 진행하려면, 가스 한도를 21000 이상으로 늘려야 합니다."}, "alertMessageInsufficientBalanceWithNativeCurrency": {"message": "계정에 네트워크 수수료를 지불할 만큼의 $1(이)가 부족합니다."}, "alertMessageNetworkBusy": {"message": "가스비가 높고 견적의 정확도도 떨어집니다."}, "alertMessageNoGasPrice": {"message": "수수료를 직접 업데이트할 때까지는 이 트랜잭션을 진행할 수 없습니다."}, "alertMessageSignInDomainMismatch": {"message": "요청을 보낸 사이트에 로그인되어 있지 않습니다. 이는 로그인 정보를 도용하려는 시도일 수 있습니다."}, "alertMessageSignInWrongAccount": {"message": "이 사이트에서 잘못된 계정으로 로그인하라고 요청합니다."}, "alertModalAcknowledge": {"message": "위험성을 인지했으며, 계속 진행합니다"}, "alertModalDetails": {"message": "경고 세부 사항"}, "alertModalReviewAllAlerts": {"message": "모든 경고 검토하기"}, "alertReasonChangeInSimulationResults": {"message": "결과가 변경되었습니다"}, "alertReasonFirstTimeInteraction": {"message": "첫 상호작용"}, "alertReasonGasEstimateFailed": {"message": "잘못된 수수료"}, "alertReasonGasFeeLow": {"message": "느린 속도"}, "alertReasonGasTooLow": {"message": "낮은 가스 한도"}, "alertReasonInsufficientBalance": {"message": "자금 부족"}, "alertReasonNetworkBusy": {"message": "네트워크 혼잡"}, "alertReasonNoGasPrice": {"message": "수수료 견적 제공 불가"}, "alertReasonPendingTransactions": {"message": "보류 중인 트랜잭션"}, "alertReasonSignIn": {"message": "의심스러운 로그인 요청"}, "alertReasonWrongAccount": {"message": "잘못된 계정"}, "alertSelectedAccountWarning": {"message": "이 요청은 지갑에서 선택한 것과 다른 계정에 대한 것입니다. 다른 계정을 사용하려면 사이트에 연결하세요."}, "alerts": {"message": "경고"}, "all": {"message": "모두"}, "allNetworks": {"message": "모든 네트워크"}, "allPermissions": {"message": "모든 권한"}, "allTimeHigh": {"message": "역대 최고"}, "allTimeLow": {"message": "역대 최저"}, "allowNotifications": {"message": "알림 허용"}, "allowWithdrawAndSpend": {"message": "$1에서 다음 금액까지 인출 및 지출하도록 허용:", "description": "The url of the site that requested permission to 'withdraw and spend'"}, "amount": {"message": "금액"}, "amountReceived": {"message": "수취 금액"}, "amountSent": {"message": "송금액"}, "andForListItems": {"message": "$1, 그리고 $2", "description": "$1 is the first item, $2 is the last item in a list of items. Used in Snap Install Warning modal."}, "andForTwoItems": {"message": "$1 및 $2", "description": "$1 is the first item, $2 is the second item. Used in Snap Install Warning modal."}, "appDescription": {"message": "전 세계에서 가장 신뢰받는 암호화폐 지갑", "description": "The description of the application"}, "appName": {"message": "NeoNix", "description": "The name of the application"}, "appNameBeta": {"message": "NeoNix Beta", "description": "The name of the application (Beta)"}, "appNameFlask": {"message": "NeoNix Flask", "description": "The name of the application (Flask)"}, "apply": {"message": "적용"}, "approve": {"message": "지출 한도 승인"}, "approveButtonText": {"message": "승인"}, "approveIncreaseAllowance": {"message": "지출 한도 $1 증액", "description": "The token symbol that is being approved"}, "approveSpendingCap": {"message": "$1 지출 한도 승인", "description": "The token symbol that is being approved"}, "approved": {"message": "승인됨"}, "approvedOn": {"message": "$1에 승인", "description": "$1 is the approval date for a permission"}, "approvedOnForAccounts": {"message": "$2 관련하여 $1에 승인됨", "description": "$1 is the approval date for a permission. $2 is the AvatarGroup component displaying account images."}, "areYouSure": {"message": "확실한가요?"}, "asset": {"message": "자산"}, "assetChartNoHistoricalPrices": {"message": "과거 데이터를 불러올 수 없습니다"}, "assetMultipleNFTsBalance": {"message": "NFT $1개"}, "assetOptions": {"message": "자산 옵션"}, "assetSingleNFTBalance": {"message": "NFT $1개"}, "assets": {"message": "자산"}, "assetsDescription": {"message": "지갑에서 토큰을 자동으로 감지하고, NFT를 표시하며, 계정 잔액을 일괄 업데이트하세요."}, "attemptToCancelSwapForFree": {"message": "무료 스왑 취소 시도"}, "attributes": {"message": "속성"}, "attributions": {"message": "속성"}, "auroraRpcDeprecationMessage": {"message": "Infura RPC URL은 더 이상 Aurora를 지원하지 않습니다."}, "authorizedPermissions": {"message": "다음 권한을 승인했습니다."}, "autoDetectTokens": {"message": "토큰 자동 감지"}, "autoDetectTokensDescription": {"message": "지갑에서 받은 새로운 토큰을 감지해 표시하기 위해 타사 API를 사용합니다. 이 서비스를 이용하여 데이터를 자동으로 가져오기 원치 않으시면 해당 기능을 끄세요. $1", "description": "$1 is a link to a support article"}, "autoLockTimeLimit": {"message": "자동 잠금 타이머(분)"}, "autoLockTimeLimitDescription": {"message": "NeoNix가 잠길 때까지 걸리는 시간을 분 단위로 설정합니다."}, "average": {"message": "평균"}, "back": {"message": "뒤로"}, "backupAndSync": {"message": "백업 및 동기화"}, "backupAndSyncBasicFunctionalityNameMention": {"message": "기본 기능"}, "backupAndSyncEnable": {"message": "백업 및 동기화 켜기"}, "backupAndSyncEnableConfirmation": {"message": "백업 및 동기화를 켜면 $1도 함께 켜집니다. 계속하시겠습니까?", "description": "$1 is backupAndSyncBasicFunctionalityNameMention in bold."}, "backupAndSyncEnableDescription": {"message": "백업 및 동기화를 통해 사용자 지정 설정과 기능을 암호화된 상태로 저장할 수 있습니다. 이 기능은 NeoNix를 여러 기기에서 동일하게 사용할 수 있게 해주며, NeoNix를 재설치할 경우 설정과 기능을 복원해 줍니다. 비밀복구구문은 백업되지 않습니다. $1.", "description": "$1 is link to the backup and sync privacy policy."}, "backupAndSyncEnableDescriptionUpdatePreferences": {"message": "언제든지 $1에서 설정을 변경할 수 있습니다", "description": "$1 is a bolded text that highlights the path to the settings page."}, "backupAndSyncEnableDescriptionUpdatePreferencesPath": {"message": "설정 > 백업 및 동기화"}, "backupAndSyncFeatureAccounts": {"message": "계정"}, "backupAndSyncManageWhatYouSync": {"message": "동기화할 항목 관리"}, "backupAndSyncManageWhatYouSyncDescription": {"message": "기기 간에 동기화할 항목을 선택해 켜세요."}, "backupAndSyncPrivacyLink": {"message": "개인 정보 보호 방법 알아보기"}, "backupAndSyncSlideDescription": {"message": "계정을 백업하고 설정을 동기화하세요."}, "backupAndSyncSlideTitle": {"message": "백업 및 동기화 소개"}, "backupApprovalInfo": {"message": "이 비밀 코드는 장치를 분실하여 지갑을 복구해야 하거나, 비밀번호를 잊은 경우, NeoNix를 다시 설치해야 하거나, 다른 장치에서 지갑에 액세스해야 할 때 필요합니다."}, "backupApprovalNotice": {"message": "비밀복구구문을 백업하여 지갑과 자금을 안전하게 보호하세요."}, "backupKeyringSnapReminder": {"message": "제거하기 전에 이 Snap에서 생성한 계정에 직접 액세스할 수 있는지 확인하세요"}, "backupNow": {"message": "지금 백업"}, "balance": {"message": "잔액"}, "balanceOutdated": {"message": "최종 잔액이 아닐 수도 있습니다."}, "baseFee": {"message": "기본 요금"}, "basic": {"message": "기본"}, "basicConfigurationBannerTitle": {"message": "기본 기능이 꺼져 있습니다."}, "basicConfigurationDescription": {"message": "NeoNix는 인터넷 서비스를 통해 토큰 세부 정보 및 가스 설정과 같은 기본 기능을 제공합니다. 인터넷 서비스를 이용할 때 IP 주소가 공유되며, 이 경우 NeoNix와 공유됩니다. 이는 다른 웹사이트를 방문할 때와 마찬가지입니다. NeoNix는 이 데이터를 일시적으로 사용하며 데이터를 절대 판매하지 않습니다. VPN을 사용하거나 이러한 서비스를 비활성화할 수 있지만 NeoNix 사용 환경에 영향을 미칠 수 있습니다. 자세한 내용은 $1 내용을 참고하세요.", "description": "$1 is to be replaced by the message for privacyMsg, and will link to https://nnxscan.io"}, "basicConfigurationLabel": {"message": "기본 기능"}, "basicConfigurationModalCheckbox": {"message": "이해했습니다. 계속 진행하겠습니다"}, "basicConfigurationModalDisclaimerOff": {"message": "이렇게 하면 NeoNix에서 시간을 완전히 최적화할 수 없습니다. 기본 기능(토큰 세부 정보, 최적의 가스 설정 등)을 사용할 수 없게 됩니다."}, "basicConfigurationModalDisclaimerOffAdditionalText": {"message": "이 기능을 끄면 $1 및 $2에 포함된 모든 기능도 함께 비활성화됩니다.", "description": "$1 and $2 are bold text for basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst and basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast respectively"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst": {"message": "보안 및 개인정보 보호, 백업 및 동기화"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast": {"message": "알림"}, "basicConfigurationModalDisclaimerOn": {"message": "NeoNix에서 시간을 최적화하려면 이 기능을 켜야 합니다. 기본 기능(토큰 세부 정보, 최적의 가스 설정 등)은 웹3 경험에 중요한 요소입니다."}, "basicConfigurationModalHeadingOff": {"message": "기본 기능 끄기"}, "basicConfigurationModalHeadingOn": {"message": "기본 기능 켜기"}, "bestPrice": {"message": "최고의 가격"}, "beta": {"message": "베타"}, "betaHeaderText": {"message": "베타 버전입니다. 버그는 $1로 보고하세요"}, "betaNeoNixVersion": {"message": "NeoNix 베타 버전"}, "betaTerms": {"message": "베타 이용약관"}, "billionAbbreviation": {"message": "B", "description": "Shortened form of 'billion'"}, "blockExplorerAccountAction": {"message": "계정", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Account in Explorer"}, "blockExplorerAssetAction": {"message": "자산", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Asset in Explorer"}, "blockExplorerSwapAction": {"message": "스왑", "description": "This is used with viewOnEtherscan e.g View Swap on Etherscan"}, "blockExplorerUrl": {"message": "블록 탐색기 URL"}, "blockExplorerUrlDefinition": {"message": "이 네트워크에 대한 블록 탐색기로 사용되는 URL입니다."}, "blockExplorerView": {"message": "$1의 계정 보기", "description": "$1 replaced by URL for custom block explorer"}, "blockaid": {"message": "Blockaid"}, "blockaidAlertDescriptionBlur": {"message": "계속하면 Blur에 등록한 모든 자산이 위험에 처할 수 있습니다."}, "blockaidAlertDescriptionMalicious": {"message": "악성 사이트와 인터렉션하고 있습니다. 계속하면 자산을 잃게 됩니다."}, "blockaidAlertDescriptionOpenSea": {"message": "계속하면 OpenSea에 등록한 모든 자산이 위험에 처할 수 있습니다."}, "blockaidAlertDescriptionOthers": {"message": "이 요청을 컨펌하면, 자산을 잃을 수 있습니다. 이 요청을 취소할 것을 권장합니다."}, "blockaidAlertDescriptionTokenTransfer": {"message": "지금 자산을 사기범에게 보내고 있습니다. 계속하면 해당 자산을 잃게 됩니다."}, "blockaidAlertDescriptionWithdraw": {"message": "이 요청을 컨펌하면, 사기범이 자산을 인출하고 사용할 수 있게 됩니다. 그러한 자산은 돌려받을 수 없습니다."}, "blockaidDescriptionApproveFarming": {"message": "이 요청을 승인하면 스캠으로 알려진 타사가 회원님의 모든 자산을 가져갈 수 있습니다."}, "blockaidDescriptionBlurFarming": {"message": "이 요청을 승인하면, Blur에 있는 자산을 타인이 갈취할 수 있습니다."}, "blockaidDescriptionErrored": {"message": "오류로 인해 보안 알림을 확인할 수 없었습니다. 모든 관련 주소를 신뢰하는 경우에만 계속 진행하세요."}, "blockaidDescriptionMaliciousDomain": {"message": "악성 도메인과 인터렉션하고 있습니다. 이 요청을 승인하면 본인의 자산을 잃을 수도 있습니다."}, "blockaidDescriptionMightLoseAssets": {"message": "이 요청을 승인하면, 자산을 잃을 수도 있습니다."}, "blockaidDescriptionSeaportFarming": {"message": "이 요청을 승인하면 OpenSea에 있는 자산을 타인이 갈취할 수 있습니다."}, "blockaidDescriptionTransferFarming": {"message": "이 요청을 승인하면 스캠과 같은 타사가 회원님의 모든 자산을 가져갈 수 있습니다."}, "blockaidMessage": {"message": "개인정보 보호 - 제3자와 데이터를 공유하지 않습니다. Arbitrum, Avalanche, BNB Chain, 이더리움 메인넷, Linea, Optimism, Polygon, Base, Sepolia에서 사용할 수 있습니다."}, "blockaidTitleDeceptive": {"message": "사기성 요청입니다"}, "blockaidTitleMayNotBeSafe": {"message": "조심하세요"}, "blockaidTitleSuspicious": {"message": "의심스러운 요청입니다"}, "blockies": {"message": "Blockies"}, "borrowed": {"message": "차입함"}, "boughtFor": {"message": "매수:"}, "bridge": {"message": "브리지"}, "bridgeAllowSwappingOf": {"message": "브릿지를 위해 $3의 정확한 $1 $2에 접근 허용", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "bridgeApproval": {"message": "브리지를 위해 $1 승인", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be bridged. $1 is the symbol of a token that has been approved."}, "bridgeApprovalWarning": {"message": "지정된 금액인 $1 $2에 대한 접근을 허용합니다. 계약은 추가 자금에 접근하지 않습니다."}, "bridgeApprovalWarningForHardware": {"message": "브릿지를 위해 $1 $2에 대한 접근을 허용한 다음 $2(으)로의 브릿지를 승인해야 합니다. 두 차례의 별도 컨펌이 필요합니다."}, "bridgeBlockExplorerLinkCopied": {"message": "블록 탐색기 링크 복사 완료!"}, "bridgeCalculatingAmount": {"message": "계산 중..."}, "bridgeConfirmTwoTransactions": {"message": "하드웨어 지갑에서 2건의 트랜잭션을 컨펌해야 합니다."}, "bridgeCreateSolanaAccount": {"message": "솔라나 계정 만들기"}, "bridgeCreateSolanaAccountDescription": {"message": "솔라나 네트워크로 스왑하려면 계정과 수신 주소가 필요합니다."}, "bridgeCreateSolanaAccountTitle": {"message": "우선 솔라나 계정이 필요합니다."}, "bridgeDetailsTitle": {"message": "브릿지 세부 정보", "description": "Title for the modal showing details about a bridge transaction."}, "bridgeEnterAmount": {"message": "금액 선택"}, "bridgeEnterAmountAndSelectAccount": {"message": "금액을 입력하고 대상 계정을 선택하세요"}, "bridgeExplorerLinkViewOn": {"message": "$1에서 보기"}, "bridgeFetchNewQuotes": {"message": "새로 가져올까요?"}, "bridgeFrom": {"message": "브릿지 출처"}, "bridgeFromTo": {"message": "$3(으)로 $1 $2 브릿지", "description": "Tells a user that they need to confirm on their hardware wallet a bridge. $1 is amount of source token, $2 is the source network, and $3 is the destination network"}, "bridgeGasFeesSplit": {"message": "이전 화면에 표시된 네트워크 수수료에는 두 트랜잭션이 모두 포함된 것입니다. 이는 각각의 트랜잭션으로 분할됩니다."}, "bridgeNetCost": {"message": "순비용"}, "bridgeQuoteExpired": {"message": "견적이 만료되었습니다."}, "bridgeSelectDestinationAccount": {"message": "대상 계정 선택"}, "bridgeSelectNetwork": {"message": "네트워크 선택"}, "bridgeSelectTokenAmountAndAccount": {"message": "토큰, 금액, 대상 계정을 선택하세요"}, "bridgeSelectTokenAndAmount": {"message": "토큰 및 금액 선택"}, "bridgeSolanaAccountCreated": {"message": "솔라나 계정을 생성했습니다"}, "bridgeStatusComplete": {"message": "완료", "description": "Status text indicating a bridge transaction has successfully completed."}, "bridgeStatusFailed": {"message": "실패", "description": "Status text indicating a bridge transaction has failed."}, "bridgeStatusInProgress": {"message": "진행 중", "description": "Status text indicating a bridge transaction is currently processing."}, "bridgeStepActionBridgeComplete": {"message": "$2에서 $1 받음", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionBridgePending": {"message": "$2에서 $1 받는 중", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionSwapComplete": {"message": "$1을(를) $2(으)로 스왑 완료", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeStepActionSwapPending": {"message": "$1을(를) $2(으)로 스왑 중", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeTerms": {"message": "약관"}, "bridgeTimingMinutes": {"message": "$1분", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "bridgeTo": {"message": "브릿지 대상"}, "bridgeToChain": {"message": "$1(으)로 브릿지"}, "bridgeTokenCannotVerifyDescription": {"message": "이 토큰을 직접 추가했다면, 브리지를 진행하기 전에 자금에 대한 위험을 충분히 인지하고 계셔야 합니다."}, "bridgeTokenCannotVerifyTitle": {"message": "이 토큰은 검증할 수 없습니다."}, "bridgeTransactionProgress": {"message": "트랜잭션 $1/2"}, "bridgeTxDetailsBridging": {"message": "브릿징"}, "bridgeTxDetailsDelayedDescription": {"message": "다음으로 연락하기:"}, "bridgeTxDetailsDelayedDescriptionSupport": {"message": "NeoNix 지원"}, "bridgeTxDetailsDelayedTitle": {"message": "3시간 이상 걸렸나요?"}, "bridgeTxDetailsNonce": {"message": "논스"}, "bridgeTxDetailsStatus": {"message": "상태"}, "bridgeTxDetailsTimestamp": {"message": "타임스탬프"}, "bridgeTxDetailsTimestampValue": {"message": "$2에 $1", "description": "$1 is the date, $2 is the time"}, "bridgeTxDetailsTokenAmountOnChain": {"message": "$1 $2 위치:", "description": "$1 is the amount of the token, $2 is the ticker symbol of the token"}, "bridgeTxDetailsTotalGasFee": {"message": "총 가스비"}, "bridgeTxDetailsYouReceived": {"message": "받음:"}, "bridgeTxDetailsYouSent": {"message": "보냄:"}, "bridgeValidationInsufficientGasMessage": {"message": "이 브릿지의 가스비를 지불할 $1(이)가 부족합니다. 더 적은 금액을 입력하거나 더 많은 $1(을)를 매수하세요."}, "bridgeValidationInsufficientGasTitle": {"message": "가스비를 위해 더 많은 $1 필요"}, "bridging": {"message": "브릿징"}, "browserNotSupported": {"message": "지원되지 않는 브라우저입니다..."}, "buildContactList": {"message": "연락처 목록 작성하기"}, "builtAroundTheWorld": {"message": "NeoNix는 전 세계적으로 설계 및 구축되어 있습니다."}, "bulletpoint": {"message": "·"}, "busy": {"message": "바쁨"}, "buyAndSell": {"message": "매수/매도"}, "buyMoreAsset": {"message": "$1 추가 구매", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "buyNow": {"message": "지금 구매"}, "bytes": {"message": "바이트"}, "canToggleInSettings": {"message": "설정 -> 경고에서 이 알림을 다시 활성화할 수 있습니다."}, "cancel": {"message": "취소"}, "cancelPopoverTitle": {"message": "트랜잭션 취소"}, "cancelSpeedUpLabel": {"message": "이 가스비는 원금을 $1합니다.", "description": "$1 is text 'replace' in bold"}, "cancelSpeedUpTransactionTooltip": {"message": "트랜잭션을 $1하려면 가스비를 최소 10%를 인상해야 네트워크에서 인식될 수 있습니다.", "description": "$1 is string 'cancel' or 'speed up'"}, "cancelled": {"message": "취소됨"}, "chainId": {"message": "체인 ID"}, "chainIdDefinition": {"message": "이 네트워크의 트랜잭션에 서명하는 데 사용되는 체인 ID입니다."}, "chainIdExistsErrorMsg": {"message": "이 체인 ID는 현재 $1 네트워크에서 사용됩니다."}, "chainListReturnedDifferentTickerSymbol": {"message": "이 토큰 심볼이 입력한 네트워크 이름 또는 체인 ID와 일치하지 않습니다. 여러 인기 토큰이 비슷한 심볼을 사용합니다. 사기꾼은 이를 이용해 더 가격이 높은 토큰을 보내도록 속일 수 있습니다. 계속하기 전에 모든 사항을 확인하세요."}, "chooseYourNetwork": {"message": "네트워크 선택"}, "chooseYourNetworkDescription": {"message": "기본 설정과 구성을 사용할 경우, NeoNix는 Infura를 기본 원격 프로시저 호출(RPC) 제공업체로 사용하여 가능한 가장 안정적이고 공개성이 낮은 방식으로 이더리움 데이터에 접근할 수 있도록 합니다. 일부 제한된 경우에는 사용자에게 최상의 사용 경험을 제공하기 위해 다른 RPC 제공업체를 사용할 수 있습니다. 직접 RPC를 선택할 수도 있지만, 어떤 RPC를 사용하든 트랜잭션을 수행하려면 본인의 IP 주소와 이더리움 지갑 주소가 해당 RPC로 전송된다는 점을 유의하세요. Infura가 EVM 계정 데이터를 처리하는 방식에 대해 더 알고 싶다면 $1 내용을 확인하시고, Solana 계정에 대한 정보는 $2의 내용을 참고하세요.", "description": "$1 is a link to the privacy policy, $2 is a link to Solana accounts support"}, "chooseYourNetworkDescriptionCallToAction": {"message": "여기를 클릭하세요"}, "chromeRequiredForHardwareWallets": {"message": "하드웨어 지갑에 연결하려면 Google Chrome에서 NeoNix를 사용해야 합니다."}, "circulatingSupply": {"message": "순환 공급"}, "clear": {"message": "지우기"}, "clearActivity": {"message": "활동 및 논스 데이터 지우기"}, "clearActivityButton": {"message": "활동 탭 데이터 지우기"}, "clearActivityDescription": {"message": "이는 계정의 논스를 초기화하고 지갑의 활동 탭에 있는 데이터를 지웁니다. 이렇게 하면 현재 계정과 네트워크만 변경될 뿐 잔액과 입금 트랜잭션에는 영향을 미치지 않습니다."}, "click": {"message": "클릭"}, "clickToConnectLedgerViaWebHID": {"message": "WebHID를 통해 Ledger를 연결하려면 여기를 클릭하세요.", "description": "Text that can be clicked to open a browser popup for connecting the ledger device via webhid"}, "close": {"message": "닫기"}, "closeExtension": {"message": "확장 닫기"}, "closeWindowAnytime": {"message": "언제든 이 창을 닫을 수 있습니다."}, "coingecko": {"message": "CoinGecko"}, "collectionName": {"message": "컬렉션 이름"}, "comboNoOptions": {"message": "옵션을 찾을 수 없습니다", "description": "Default text shown in the combo field dropdown if no options."}, "concentratedSupplyDistributionDescription": {"message": "상위 보유자에게 대부분의 토큰 공급 권한이 있어 중앙집중식 가격 조작 위험이 있습니다"}, "concentratedSupplyDistributionTitle": {"message": "집중적 공급 분배"}, "configureSnapPopupDescription": {"message": "이제 이 스냅 구성을 위해 NeoNix 페이지를 떠나게 됩니다."}, "configureSnapPopupInstallDescription": {"message": "이제 이 스냅 설치를 위해 NeoNix 페이지를 떠나게 됩니다."}, "configureSnapPopupInstallTitle": {"message": "스냅 설치"}, "configureSnapPopupLink": {"message": "이 링크를 클릭하여 계속:"}, "configureSnapPopupTitle": {"message": "스냅 구성"}, "confirm": {"message": "컨펌"}, "confirmAccountTypeSmartContract": {"message": "스마트 계정"}, "confirmAccountTypeStandard": {"message": "일반 계정"}, "confirmAlertModalAcknowledgeMultiple": {"message": "경고를 인지했으며, 계속 진행합니다"}, "confirmAlertModalAcknowledgeSingle": {"message": "경고를 인지했으며, 계속 진행합니다"}, "confirmFieldPaymaster": {"message": "수수료 지불:"}, "confirmFieldTooltipPaymaster": {"message": "이 트랜잭션에 대한 수수료는 Paymaster 스마트 계약에서 지불합니다."}, "confirmGasFeeTokenBalance": {"message": "잔액:"}, "confirmGasFeeTokenInsufficientBalance": {"message": "자금 부족"}, "confirmGasFeeTokenNeoNixFee": {"message": "$1 수수료 포함"}, "confirmGasFeeTokenModalNativeToggleNeoNix": {"message": "이 트랜잭션을 완료하기 위해 NeoNix가 잔액을 충전하고 있습니다."}, "confirmGasFeeTokenModalNativeToggleWallet": {"message": "지갑에 있는 잔액으로 네트워크 수수료를 결제하세요."}, "confirmGasFeeTokenModalPayETH": {"message": "ETH로 결제"}, "confirmGasFeeTokenModalPayToken": {"message": "다른 토큰으로 결제"}, "confirmGasFeeTokenModalTitle": {"message": "토큰 선택"}, "confirmGasFeeTokenToast": {"message": "이 네트워크 수수료는 $1(으)로 지불됩니다"}, "confirmGasFeeTokenTooltip": {"message": "이 금액은 트랜잭션을 처리하기 위해 네트워크에 지불되는 비용입니다. 비ETH 토큰 또는 사전 충전된 ETH의 경우 $1의 NeoNix 수수료가 포함됩니다."}, "confirmInfoAccountNow": {"message": "지금"}, "confirmInfoSwitchingTo": {"message": "다음으로 전환 중:"}, "confirmNestedTransactionTitle": {"message": "트랜잭션 $1"}, "confirmPassword": {"message": "비밀번호 컨펌"}, "confirmRecoveryPhrase": {"message": "비밀복구구문 컨펌"}, "confirmSimulationApprove": {"message": "승인:"}, "confirmTitleAccountTypeSwitch": {"message": "계정 업데이트"}, "confirmTitleApproveTransactionNFT": {"message": "출금 요청"}, "confirmTitleDeployContract": {"message": "계약 배포"}, "confirmTitleDescApproveTransaction": {"message": "이 사이트에서 NFT 인출 권한을 요청합니다"}, "confirmTitleDescDelegationRevoke": {"message": "표준 계정으로 전환(EOA)으로 전환 중입니다."}, "confirmTitleDescDelegationUpgrade": {"message": "스마트 계정으로 전환 중입니다"}, "confirmTitleDescDeployContract": {"message": "이 사이트에서 계약을 배포하려고 합니다"}, "confirmTitleDescERC20ApproveTransaction": {"message": "이 사이트에서 토큰 인출 권한을 요청합니다"}, "confirmTitleDescPermitSignature": {"message": "해당 사이트에서 토큰 사용 승인을 요청합니다."}, "confirmTitleDescSIWESignature": {"message": "회원님이 이 계정을 소유하고 있음을 확인하기 위해 로그인을 요청하는 사이트가 있습니다."}, "confirmTitleDescSign": {"message": "컨펌 전에 요청 세부 정보를 검토하세요."}, "confirmTitlePermitTokens": {"message": "지출 한도 요청"}, "confirmTitleRevokeApproveTransaction": {"message": "권한 제거"}, "confirmTitleSIWESignature": {"message": "로그인 요청"}, "confirmTitleSetApprovalForAllRevokeTransaction": {"message": "권한 제거"}, "confirmTitleSignature": {"message": "서명 요청"}, "confirmTitleTransaction": {"message": "트랜젝션 요청"}, "confirmationAlertDetails": {"message": "자산을 보호하기 위해 요청을 거부하는 것이 좋습니다."}, "confirmationAlertModalTitleDescription": {"message": "자산이 위험할 수 있습니다"}, "confirmed": {"message": "컨펌됨"}, "confusableUnicode": {"message": "'$1'은(는) '$2'와(과) 유사합니다."}, "confusableZeroWidthUnicode": {"message": "너비가 0인 문자가 있습니다."}, "confusingEnsDomain": {"message": "ENS 이름에 혼동하기 쉬운 문자가 있습니다. 잠재적 사기를 막기 위해 ENS 이름을 확인하세요."}, "connect": {"message": "연결"}, "connectAccount": {"message": "계정 연결"}, "connectAccountOrCreate": {"message": "계정 연결 또는 새 계정 만들기"}, "connectAccounts": {"message": "계정 연결"}, "connectAnAccountHeader": {"message": "계정 연결"}, "connectManually": {"message": "현재 사이트에 직접 연결"}, "connectMoreAccounts": {"message": "더 많은 계정 연결"}, "connectSnap": {"message": "$1 연결", "description": "$1 is the snap for which a connection is being requested."}, "connectWithNeoNix": {"message": "NeoNix로 연결"}, "connectedAccounts": {"message": "연결된 계정"}, "connectedAccountsDescriptionPlural": {"message": "이 사이트에 계정 $1개가 연결되어 있습니다.", "description": "$1 is the number of accounts"}, "connectedAccountsDescriptionSingular": {"message": "이 사이트에 계정 1개가 연결되어 있습니다."}, "connectedAccountsEmptyDescription": {"message": "NeoNix가 이 사이트에 연결되어 있지 않습니다. web3 사이트를 연결하려면 사이트에서 연결 버튼을 찾아 클릭하세요."}, "connectedAccountsListTooltip": {"message": "$1은(는) 계정 잔액, 주소, 활동을 확인하고 연결된 계정에 대해 승인할 거래를 제안할 수 있습니다.", "description": "$1 is the origin name"}, "connectedAccountsToast": {"message": "연결 계정 업데이트됨"}, "connectedSites": {"message": "연결된 사이트"}, "connectedSitesAndSnaps": {"message": "연결된 사이트 및 Snap"}, "connectedSitesDescription": {"message": "$1 계정이 이 사이트에 연결되었습니다. 해당 계정 주소도 볼 수 있습니다.", "description": "$1 is the account name"}, "connectedSitesEmptyDescription": {"message": "$1 계정은 어떤 사이트에도 연결되어 있지 않습니다.", "description": "$1 is the account name"}, "connectedSnapAndNoAccountDescription": {"message": "NeoNix는 이 사이트와 연결되어 있지만, 아직 연결된 계정이 없습니다"}, "connectedSnaps": {"message": "연결된 Snap"}, "connectedWithAccount": {"message": "$1 계정 연결됨", "description": "$1 represents account length"}, "connectedWithAccountName": {"message": "$1 계정과 연결됨", "description": "$1 represents account name"}, "connectedWithNetwork": {"message": "$1 네트워크 연결됨", "description": "$1 represents network length"}, "connectedWithNetworkName": {"message": "$1(으)로 연결됨", "description": "$1 represents network name"}, "connecting": {"message": "연결 중"}, "connectingTo": {"message": "$1에 연결 중"}, "connectingToDeprecatedNetwork": {"message": "'$1' 네트워크는 단계적으로 지원 중단되므로 작동하지 않을 수 있습니다. 다른 네트워크를 사용해 보세요."}, "connectingToGoerli": {"message": "Goerli 테스트 네트워크에 연결 중"}, "connectingToLineaGoerli": {"message": "Linea Goerli 테스트 네트워크에 연결 중"}, "connectingToLineaMainnet": {"message": "Linea 메인넷에 연결 중"}, "connectingToLineaSepolia": {"message": "Linea Sepolia 테스트 네트워크에 연결 중"}, "connectingToMainnet": {"message": "이더리움 메인넷에 연결 중"}, "connectingToSepolia": {"message": "Sepolia 테스트 네트워크에 연결 중"}, "connectionDescription": {"message": "이 사이트를 NeoNix와 연결"}, "connectionFailed": {"message": "연결 실패"}, "connectionFailedDescription": {"message": "$1 불러오기에 실패했습니다. 네트워크를 확인하고 다시 시도하세요.", "description": "$1 is the name of the snap being fetched."}, "connectionPopoverDescription": {"message": "사이트에 연결하려면 연결 버튼을 선택하세요. NeoNix는 웹3 사이트에만 연결할 수 있습니다."}, "connectionRequest": {"message": "연결 요청"}, "contactUs": {"message": "문의하기"}, "contacts": {"message": "연락처"}, "contentFromSnap": {"message": "콘텐츠 출처: $1", "description": "$1 represents the name of the snap"}, "continue": {"message": "계속"}, "contract": {"message": "계약"}, "contractAddress": {"message": "계약 주소"}, "contractAddressError": {"message": "토큰의 계약 주소로 토큰을 보냅니다. 이로 인해 토큰이 손실될 수 있습니다."}, "contractDeployment": {"message": "계약 배포"}, "contractInteraction": {"message": "계약 인터렉션"}, "convertTokenToNFTDescription": {"message": "이 자산은 NFT입니다. NeoNix는 이제 NFT의 본래 기능에 따라 완전히 지원합니다. 이를 토큰 목록에서 제거하고 NFT로 추가하시겠습니까?"}, "convertTokenToNFTExistDescription": {"message": "이 자산이 NFT로 추가되었습니다. 토큰 목록에서 제거하시겠습니까?"}, "coolWallet": {"message": "CoolWallet"}, "copiedExclamation": {"message": "복사 완료!"}, "copyAddress": {"message": "주소를 클립보드에 복사"}, "copyAddressShort": {"message": "주소 복사"}, "copyPrivateKey": {"message": "개인 키 복사"}, "copyToClipboard": {"message": "클립보드에 복사"}, "copyTransactionId": {"message": "트랜잭션 ID 복사"}, "create": {"message": "생성"}, "createNewAccountHeader": {"message": "새 계정 만들기"}, "createPassword": {"message": "비밀번호 생성"}, "createSnapAccountDescription": {"message": "$1에서 NeoNix에 새로운 계정을 추가하려고 합니다."}, "createSnapAccountTitle": {"message": "계정 생성"}, "createSolanaAccount": {"message": "솔라나 계정 만들기"}, "creatorAddress": {"message": "크리에이터 주소"}, "crossChainSwapsLink": {"message": "NeoNix Portfolio로 네트워크 간 스왑"}, "crossChainSwapsLinkNative": {"message": "브릿지로 네트워크 간 스왑"}, "cryptoCompare": {"message": "CryptoCompare"}, "currencyConversion": {"message": "통화"}, "currencyRateCheckToggle": {"message": "잔액 및 토큰 가격을 비교할 수 있습니다"}, "currencyRateCheckToggleDescription": {"message": "잔액과 토큰 가격 디스플레이를 위해 $1 및 $2 API를 이용합니다. $3", "description": "$1 represents Coingecko, $2 represents CryptoCompare and $3 represents Privacy Policy"}, "currencySymbol": {"message": "통화 기호"}, "currencySymbolDefinition": {"message": "이 네트워크의 통화를 표시하는 티커 기호입니다."}, "currentAccountNotConnected": {"message": "현재 계정이 연결되어 있지 있습니다."}, "currentExtension": {"message": "현재 확장 페이지"}, "currentLanguage": {"message": "현재 언어"}, "currentNetwork": {"message": "현재 네트워크", "description": "Speicifies to token network filter to filter by current Network. Will render when network nickname is not available"}, "currentRpcUrlDeprecated": {"message": "해당 네트워크의 현재 rpc url의 지원이 중단되었습니다."}, "currentTitle": {"message": "현재:"}, "currentlyUnavailable": {"message": "이 네트워크에서 사용할 수 없음"}, "curveHighGasEstimate": {"message": "공격적 가스 추정치 그래프"}, "curveLowGasEstimate": {"message": "낮은 가스 추정치"}, "curveMediumGasEstimate": {"message": "시장 가스 추정치 그래프"}, "custom": {"message": "고급"}, "customGasSettingToolTipMessage": {"message": "$1 사용으로 가스 가격을 맞춤 설정하세요. 익숙하지 않은 경우 혼동될 수 있습니다. 자신의 책임하에 인터렉션하세요.", "description": "$1 is key 'advanced' (text: 'Advanced') separated here so that it can be passed in with bold font-weight"}, "customSlippage": {"message": "사용자 지정"}, "customSpendLimit": {"message": "맞춤 지출 한도"}, "customToken": {"message": "맞춤 토큰"}, "customTokenWarningInNonTokenDetectionNetwork": {"message": "이 네트워크에서는 아직 토큰 감지 기능을 사용할 수 없습니다. 토큰을 직접 가져오고 해당 토큰을 신뢰할 수 있는지 반드시 확인하세요. $1에 대해 알아보기"}, "customTokenWarningInTokenDetectionNetwork": {"message": "토큰을 생성하기 전에 해당 토큰을 신뢰할 수 있는지 반드시 확인하세요. $1에 대해 알아보기."}, "customTokenWarningInTokenDetectionNetworkWithTDOFF": {"message": "불러오기 전에 토큰의 신뢰성을 확인하세요. $1 상황을 피하는 방법을 알아보세요. 또한 $2 토큰 감지 기능을 활성화할 수 있습니다."}, "customerSupport": {"message": "고객 지원"}, "customizeYourNotifications": {"message": "알림 맞춤 설정"}, "customizeYourNotificationsText": {"message": "수신하려는 알림의 유형을 켜세요."}, "dappSuggested": {"message": "추천 사이트"}, "dappSuggestedGasSettingToolTipMessage": {"message": "$1에서 이 가격을 제안했습니다.", "description": "$1 is url for the dapp that has suggested gas settings"}, "dappSuggestedHigh": {"message": "추천 사이트"}, "dappSuggestedHighShortLabel": {"message": "사이트(높음)"}, "dappSuggestedShortLabel": {"message": "사이트"}, "dappSuggestedTooltip": {"message": "$1에서 이 가격을 추천했습니다.", "description": "$1 represents the Dapp's origin"}, "darkTheme": {"message": "어둡게"}, "data": {"message": "데이터"}, "dataCollectionForMarketing": {"message": "마케팅을 위한 데이터 수집"}, "dataCollectionForMarketingDescription": {"message": "MetaMetrics를 사용하여 사용자가 마케팅 커뮤니케이션과 어떻게 상호 작용하는지 파악할 것입니다. 관련 뉴스(예: 제품 기능 및 기타 자료)를 공유할 수 있습니다."}, "dataCollectionWarningPopoverButton": {"message": "확인"}, "dataCollectionWarningPopoverDescription": {"message": "마케팅 목적의 데이터 수집을 비활성화했습니다. 이는 이 장치에만 적용됩니다. 다른 장치에서 NeoNix를 사용하면 해당 장치에서도 데이터 수집을 비활성화해야 합니다."}, "dataUnavailable": {"message": "데이터 사용 불가"}, "dateCreated": {"message": "생성일"}, "dcent": {"message": "<PERSON><PERSON><PERSON>nt"}, "debitCreditPurchaseOptions": {"message": "직불카드 또는 신용카드 매수 옵션"}, "decimal": {"message": "토큰 소수점"}, "decimalsMustZerotoTen": {"message": "소수점 이하 자릿수는 0 이상, 36 이하여야 합니다."}, "decrypt": {"message": "암호 해독"}, "decryptCopy": {"message": "암호 해독된 메시지 복사"}, "decryptInlineError": {"message": "다음 오류 때문에 이 메시지를 해독할 수 없습니다: $1", "description": "$1 is error message"}, "decryptMessageNotice": {"message": "$1에서 작업 완료를 위해 이 메시지를 읽고자 합니다.", "description": "$1 is the web3 site name"}, "decryptNeoNix": {"message": "메시지 암호 해독"}, "decryptRequest": {"message": "암호 해독 요청"}, "defaultRpcUrl": {"message": "기본 RPC URL"}, "defaultSettingsSubTitle": {"message": "NeoNix의 기본 설정은 보안과 사용의 편의성 사이에서 최적의 균형을 맞추기 위한 설정입니다. 개인정보 보호 기능을 강화하려면 설정을 변경하세요."}, "defaultSettingsTitle": {"message": "기본 개인정보 보호 설정"}, "defi": {"message": "디파이"}, "defiTabErrorContent": {"message": "나중에 다시 방문해 주세요."}, "defiTabErrorTitle": {"message": "페이지를 로드할 수 없습니다."}, "delete": {"message": "삭제"}, "deleteContact": {"message": "연락처 삭제"}, "deleteMetaMetricsData": {"message": "MetaMetrics 데이터 삭제"}, "deleteMetaMetricsDataDescription": {"message": "이렇게 하면 이 기기에서 사용한 과거 MetaMetrics 데이터가 삭제됩니다. 이 데이터를 삭제해도 지갑과 계정에는 아무런 영향이 없습니다. 삭제는 최대 30일 정도 소요됩니다. 다음을 참고하세요: $1.", "description": "$1 will have text saying Privacy Policy "}, "deleteMetaMetricsDataErrorDesc": {"message": "분석 시스템 서버 문제로 지금 요청을 처리할 수 없습니다. 나중에 다시 시도하세요"}, "deleteMetaMetricsDataErrorTitle": {"message": "지금은 데이터를 삭제할 수 없습니다"}, "deleteMetaMetricsDataModalDesc": {"message": "모든 MetaMetrics 데이터를 제거합니다. 정말 제거하시겠습니까?"}, "deleteMetaMetricsDataModalTitle": {"message": "MetaMetrics 데이터를 삭제하시겠습니까?"}, "deleteMetaMetricsDataRequestedDescription": {"message": "$1에서 이 작업을 시작했습니다. 이 과정에는 최대 30일이 소요됩니다. 다음을 참고하세요: $2", "description": "$1 will be the date on which teh deletion is requested and $2 will have text saying Privacy Policy "}, "deleteNetworkIntro": {"message": "이 네트워크를 삭제하면 나중에 이 네트워크에 있는 자산을 보고 싶을 때 네트워크를 다시 추가해야 합니다"}, "deleteNetworkTitle": {"message": "$1 네트워크를 삭제하시겠습니까?", "description": "$1 represents the name of the network"}, "depositCrypto": {"message": "지갑 주소 또는 QR 코드를 사용하여 다른 계정에서 암호화폐를 입금합니다."}, "deprecatedGoerliNtwrkMsg": {"message": "이더리움 시스템 업데이트로 인해 Goerli 테스트 네트워크는 곧 단계적으로 지원 중단될 예정입니다."}, "deprecatedNetwork": {"message": "이 네트워크는 더 이상 지원되지 않습니다"}, "deprecatedNetworkButtonMsg": {"message": "확인"}, "deprecatedNetworkDescription": {"message": "연결하려는 네트워크는 NeoNix에서 더 이상 지원되지 않습니다. $1"}, "description": {"message": "설명"}, "descriptionFromSnap": {"message": "$1 설명", "description": "$1 represents the name of the snap"}, "destinationAccountPickerNoEligible": {"message": "적합한 계정을 찾을 수 없습니다"}, "destinationAccountPickerNoMatching": {"message": "일치하는 계정을 찾을 수 없습니다"}, "destinationAccountPickerReceiveAt": {"message": "수신 위치"}, "destinationAccountPickerSearchPlaceholderToMainnet": {"message": "수신 주소 또는 ENS"}, "destinationAccountPickerSearchPlaceholderToSolana": {"message": "수신 주소"}, "destinationTransactionIdLabel": {"message": "대상 Tx ID", "description": "Label for the destination transaction ID field."}, "details": {"message": "세부 정보"}, "developerOptions": {"message": "개발자 옵션"}, "disabledGasOptionToolTipMessage": {"message": "“$1” 유형은 오리지널 가스비를 최소 10% 인상해야 하는 기준에 미치지 못하므로 비활성화되었습니다.", "description": "$1 is gas estimate type which can be market or aggressive"}, "disconnect": {"message": "연결 해제"}, "disconnectAllAccounts": {"message": "모든 계정 연결 해제"}, "disconnectAllAccountsConfirmationDescription": {"message": "연결을 해제하시겠습니까? 사이트 기능을 이용하지 못하게 될 수도 있습니다."}, "disconnectAllAccountsText": {"message": "계정"}, "disconnectAllDescriptionText": {"message": "이 사이트에서 연결을 해제하면, 이 사이트를 다시 사용하기 위해 계정과 네트워크를 다시 연결해야 합니다."}, "disconnectAllSnapsText": {"message": "Snap"}, "disconnectMessage": {"message": "이렇게 하면 이 사이트와의 연결이 해제됩니다"}, "disconnectPrompt": {"message": "$1 연결 해제"}, "disconnectThisAccount": {"message": "이 계정 연결 해제"}, "disconnectedAllAccountsToast": {"message": "모든 계정이 $1에서 연결 해제됨", "description": "$1 is name of the dapp`"}, "disconnectedSingleAccountToast": {"message": "$1, $2에서 연결 해제됨", "description": "$1 is name of the name and $2 represents the dapp name`"}, "discover": {"message": "발견"}, "discoverSnaps": {"message": "Snap 알아보기", "description": "Text that links to the Snaps website. Displayed in a banner on Snaps list page in settings."}, "dismiss": {"message": "해지"}, "dismissReminderDescriptionField": {"message": "이 기능을 켜면 비밀복구구문 백업 알림 메시지를 해지할 수 있습니다. 단, 자금 손실을 방지하려면 비밀복구구문을 백업하는 것이 좋습니다."}, "dismissReminderField": {"message": "비밀복구구문 백업 알림 해지"}, "dismissSmartAccountSuggestionEnabledDescription": {"message": "이 옵션을 켜면 어떤 계정에서도 '스마트 계정으로 전환' 제안이 더 이상 표시되지 않습니다. 스마트 계정을 사용하면 더 빠른 트랜잭션을 더 낮은 네트워크 수수료로 이용할 수 있으며, 수수료 지불 방식도 더욱 유연해집니다."}, "dismissSmartAccountSuggestionEnabledTitle": {"message": "'스마트 계정으로 전환' 제안 끄기"}, "displayNftMedia": {"message": "NFT 미디어 표시"}, "displayNftMediaDescription": {"message": "NFT 미디어와 데이터를 표시하면 IP 주소가 OpenSea나 기타 제삼자에게 노출될 수 있습니다. 공격자는 이를 통해 회원님의 IP 주소와 이더리움 주소를 연결할 수 있습니다. NFT 자동 감지는 이 설정을 사용하며, 이 설정이 꺼져 있는 경우 사용할 수 없습니다."}, "doNotShare": {"message": "누구와도 공유하지 마세요"}, "domain": {"message": "도메인"}, "done": {"message": "완료"}, "dontShowThisAgain": {"message": "다시 표시 안 함"}, "downArrow": {"message": "하강 화살표"}, "downloadGoogleChrome": {"message": "Google Chrome 다운로드"}, "downloadNow": {"message": "지금 다운로드"}, "downloadStateLogs": {"message": "상태 로그 다운로드"}, "dragAndDropBanner": {"message": "네트워크를 드래그하여 순서를 변경할 수 있습니다. "}, "dropped": {"message": "중단됨"}, "duplicateContactTooltip": {"message": "이 연락처 이름이 기존 계정이나 연락처와 충돌합니다"}, "duplicateContactWarning": {"message": "중복된 연락처가 있습니다"}, "durationSuffixDay": {"message": "일", "description": "Shortened form of 'day'"}, "durationSuffixHour": {"message": "시간", "description": "Shortened form of 'hour'"}, "durationSuffixMillisecond": {"message": "밀리초", "description": "Shortened form of 'millisecond'"}, "durationSuffixMinute": {"message": "분", "description": "Shortened form of 'minute'"}, "durationSuffixMonth": {"message": "개월", "description": "Shortened form of 'month'"}, "durationSuffixSecond": {"message": "초", "description": "Shortened form of 'second'"}, "durationSuffixWeek": {"message": "주", "description": "Shortened form of 'week'"}, "durationSuffixYear": {"message": "년", "description": "Shortened form of 'year'"}, "earn": {"message": "수익 얻기"}, "edit": {"message": "편집"}, "editANickname": {"message": "닉네임 편집"}, "editAccounts": {"message": "계정 편집"}, "editAddressNickname": {"message": "주소 닉네임 편집"}, "editCancellationGasFeeModalTitle": {"message": "가스비 취소 편집"}, "editContact": {"message": "연락처 편집"}, "editGasFeeModalTitle": {"message": "가스비 편집"}, "editGasLimitOutOfBounds": {"message": "가스 한도는 $1입니다."}, "editGasLimitOutOfBoundsV2": {"message": "가스 한도는 $1보다 크고 $2 미만이어야 합니다.", "description": "$1 is the minimum limit for gas and $2 is the maximum limit"}, "editGasLimitTooltip": {"message": "가스 한도는 사용하려는 가스의 최대 단위입니다. 가스 단위는 \"최대 우선 요금\" 및 \"최대 요금\"의 승수입니다."}, "editGasMaxBaseFeeGWEIImbalance": {"message": "최대 기본 요금이 우선 요금보다 낮을 수 없습니다."}, "editGasMaxBaseFeeHigh": {"message": "최대 기본 요금이 필요 이상으로 높습니다."}, "editGasMaxBaseFeeLow": {"message": "최대 기본 요금이 현재 네트워크 조건에 비해 낮습니다."}, "editGasMaxFeeHigh": {"message": "최대 요금이 필요 이상으로 높습니다."}, "editGasMaxFeeLow": {"message": "최대 요금이 네트워크 조건에 비해 너무 낮습니다."}, "editGasMaxFeePriorityImbalance": {"message": "최대 요금이 최대 우선 요금보다 낮을 수 없습니다."}, "editGasMaxPriorityFeeBelowMinimum": {"message": "최대 우선 요금은 0GWEI보다 커야 합니다."}, "editGasMaxPriorityFeeBelowMinimumV2": {"message": "우선 요금은 0보다 커야 합니다."}, "editGasMaxPriorityFeeHigh": {"message": "최대 우선 요금이 필요 이상으로 높습니다. 필요 이상으로 지급될 수 있습니다."}, "editGasMaxPriorityFeeHighV2": {"message": "우선 요금이 필요 이상으로 높습니다. 필요 이상으로 지급될 수 있습니다"}, "editGasMaxPriorityFeeLow": {"message": "최대 우선 요금이 현재 네트워크 조건에 비해 낮습니다."}, "editGasMaxPriorityFeeLowV2": {"message": "우선 요금이 현재 네트워크 조건에 비해 낮습니다."}, "editGasPriceTooLow": {"message": "가스 가격은 0보다 커야 합니다."}, "editGasPriceTooltip": {"message": "이 네트워크에서는 트랜잭션을 제출할 때 \"가스 가격\"을 필수 입력해야 합니다. 가스 가격은 가스 단위당 지급할 금액입니다."}, "editGasSubTextFeeLabel": {"message": "최대 요금"}, "editGasTitle": {"message": "우선 순위 편집"}, "editGasTooLow": {"message": "알 수 없는 처리 시간"}, "editInPortfolio": {"message": "Portfolio에서 수정"}, "editNetworkLink": {"message": "원본 네트워크 편집"}, "editNetworksTitle": {"message": "네트워크 편집"}, "editNonceField": {"message": "논스 편집"}, "editNonceMessage": {"message": "이는 고급 기능으로, 주의해서 사용해야 합니다."}, "editPermission": {"message": "권한 편집"}, "editPermissions": {"message": "권한 편집"}, "editSpeedUpEditGasFeeModalTitle": {"message": "가스비 가속 편집"}, "editSpendingCap": {"message": "지출 한도 편집"}, "editSpendingCapAccountBalance": {"message": "계정 잔액: $1 $2"}, "editSpendingCapDesc": {"message": "회원님을 대신하여 지출해도 부담되지 않는 금액을 입력하세요."}, "editSpendingCapError": {"message": "지출 한도는 소수점 이하 $1 자리를 초과할 수 없습니다. 계속하려면 소수점 이하 숫자를 제거하세요."}, "editSpendingCapSpecialCharError": {"message": "숫자만 입력"}, "enableAutoDetect": {"message": " 자동 감지 활성화"}, "enableFromSettings": {"message": " 설정에서 이 기능을 활성화합니다."}, "enableSnap": {"message": "활성화"}, "enableToken": {"message": "$1 활성화", "description": "$1 is a token symbol, e.g. ETH"}, "enabled": {"message": "활성화됨"}, "enabledNetworks": {"message": "활성화된 네트워크"}, "encryptionPublicKeyNotice": {"message": "$1에서 회원님의 공개 암호화 키를 요구합니다. 동의를 받으면 이 사이트에서 암호화된 메시지를 작성하여 회원님에게 전송할 수 있습니다.", "description": "$1 is the web3 site name"}, "encryptionPublicKeyRequest": {"message": "암호화 공개 키 요구"}, "endpointReturnedDifferentChainId": {"message": "입력한 RPC URL이 체인 ID($1)와 다릅니다.", "description": "$1 is the return value of eth_chainId from an RPC endpoint"}, "enhancedTokenDetectionAlertMessage": {"message": "$1에서 향상된 토큰 감지 기능을 사용할 수 있습니다. $2"}, "ensDomainsSettingDescriptionIntroduction": {"message": "NeoNix를 사용하면 브라우저의 주소창에서 ENS 도메인을 바로 볼 수 있습니다. 방법은 다음과 같습니다."}, "ensDomainsSettingDescriptionOutroduction": {"message": "이 기능을 사용하면 IPFS 제삼자 서비스에 회원님의 IP 주소가 노출된다는 사실을 명심하세요."}, "ensDomainsSettingDescriptionPart1": {"message": "NeoNix에서는 이더리움의 ENS 계약을 확인하여 해당 ENS 이름과 연결되어 있는 코드를 찾습니다."}, "ensDomainsSettingDescriptionPart2": {"message": "해당 코드가 IPFS와 링크되어 있으면 관련 콘텐츠(주로 웹사이트)를 볼 수 있습니다."}, "ensDomainsSettingTitle": {"message": "주소창에 ENS 도메인 표시하기"}, "ensUnknownError": {"message": "ENS를 조회하지 못했습니다."}, "enterANameToIdentifyTheUrl": {"message": "URL을 식별할 이름을 입력하세요"}, "enterChainId": {"message": "체인 ID 입력"}, "enterMaxSpendLimit": {"message": "최대 지출 한도 입력"}, "enterNetworkName": {"message": "네트워크 이름 입력"}, "enterOptionalPassword": {"message": "선택적 비밀번호를 입력하세요"}, "enterPasswordContinue": {"message": "계속하려면 비밀번호를 입력하세요"}, "enterRpcUrl": {"message": "RPC URL 입력"}, "enterSymbol": {"message": "심볼 입력"}, "enterTokenNameOrAddress": {"message": "토큰 이름 입력 또는 주소 붙여넣기"}, "enterYourPassword": {"message": "비밀번호 입력"}, "errorCode": {"message": "코드: $1", "description": "Displayed error code for debugging purposes. $1 is the error code"}, "errorGettingSafeChainList": {"message": "안전 체인 목록을 가져오는 동안 오류가 발생했습니다. 주의하여 계속 진행하세요."}, "errorMessage": {"message": "메시지: $1", "description": "Displayed error message for debugging purposes. $1 is the error message"}, "errorName": {"message": "코드: $1", "description": "Displayed error name for debugging purposes. $1 is the error name"}, "errorPageContactSupport": {"message": "고객 지원 문의", "description": "Button for contact MM support"}, "errorPageDescribeUsWhatHappened": {"message": "오류에 대해 설명해 주세요", "description": "<PERSON><PERSON> for submitting report to sentry"}, "errorPageInfo": {"message": "귀하의 정보를 표시할 수 없습니다. 걱정하지 마세요, 지갑과 자금은 안전합니다.", "description": "Information banner shown in the error page"}, "errorPageMessageTitle": {"message": "오류 메시지", "description": "Title for description, which is displayed for debugging purposes"}, "errorPageSentryFormTitle": {"message": "오류에 대해 설명해 주세요", "description": "In sentry feedback form, The title at the top of the feedback form."}, "errorPageSentryMessagePlaceholder": {"message": "버그를 재현할 수 있는 방법 등 자세한 정보를 알려주시면 문제를 해결하는 데 도움이 됩니다.", "description": "In sentry feedback form, The placeholder for the feedback description input field."}, "errorPageSentrySuccessMessageText": {"message": "감사합니다! 곧 확인하겠습니다.", "description": "In sentry feedback form, The message displayed after a successful feedback submission."}, "errorPageTitle": {"message": "NeoNix 오류 발생", "description": "Title of generic error page"}, "errorPageTryAgain": {"message": "다시 시도", "description": "<PERSON><PERSON> for try again"}, "errorStack": {"message": "스택:", "description": "Title for error stack, which is displayed for debugging purposes"}, "errorWhileConnectingToRPC": {"message": "사용자 맞춤 네트워크 연결 중에 오류가 발생했습니다."}, "errorWithSnap": {"message": "$1 오류", "description": "$1 represents the name of the snap"}, "estimatedFee": {"message": "예상 수수료"}, "estimatedFeeTooltip": {"message": "네트워크에서 트랜잭션을 처리하기 위해 지불한 금액입니다."}, "ethGasPriceFetchWarning": {"message": "현재 주요 가스 견적 서비스를 사용할 수 없으므로 백업 가스 가격을 제공합니다."}, "ethereumProviderAccess": {"message": "이더리움 공급업체에 $1 엑세스 허용", "description": "The parameter is the name of the requesting origin"}, "ethereumPublicAddress": {"message": "이더리움 공개 주소"}, "etherscan": {"message": "Etherscan"}, "etherscanView": {"message": "Etherscan에서 계정 보기"}, "etherscanViewOn": {"message": "Etherscan에서 보기"}, "existingChainId": {"message": "입력한 정보는 기존 체인 ID와 연결되어 있습니다."}, "expandView": {"message": "보기 확장"}, "experimental": {"message": "실험적"}, "exploreweb3": {"message": "웹3 탐색"}, "exportYourData": {"message": "데이터 내보내기"}, "exportYourDataButton": {"message": "다운로드"}, "exportYourDataDescription": {"message": "계약 및 환경설정 등의 데이터를 내보낼 수 있습니다."}, "extendWalletWithSnaps": {"message": "커뮤니티에서 만들어진 Snap을 알아보고 웹3 경험을 개인 맞춤하세요.", "description": "Banner description displayed on Snaps list page in Settings when less than 6 Snaps is installed."}, "externalAccount": {"message": "외부 계정"}, "externalExtension": {"message": "외부 확장"}, "externalNameSourcesSetting": {"message": "추천 닉네임"}, "externalNameSourcesSettingDescription": {"message": "Etherscan, Infura, Lens Protocol과 같은 제삼자에게서 회원님이 상호작용하는 주소에 대한 추천 닉네임을 가져올 것입니다. 이러한 제삼자는 해당 주소와 회원님의 IP 주소를 볼 수 있습니다. 회원님의 계정 주소는 제삼자에게 노출되지 않습니다."}, "failed": {"message": "실패"}, "failedToFetchChainId": {"message": "체인 ID를 가져올 수 없습니다. RPC URL이 올바른가요?"}, "failover": {"message": "장애 조치"}, "failoverRpcUrl": {"message": "장애 조치 RPC URL"}, "failureMessage": {"message": "문제가 발생했습니다. 작업을 완료할 수 없습니다."}, "fast": {"message": "빠름"}, "feeDetails": {"message": "수수료 세부 정보"}, "fileImportFail": {"message": "파일 가져오기가 작동하지 않나요? 여기를 클릭하세요.", "description": "Helps user import their account from a JSON file"}, "flaskWelcomeUninstall": {"message": "이 확장 프로그램을 삭제해야 합니다", "description": "This request is shown on the Flask Welcome screen. It is intended for non-developers, and will be bolded."}, "flaskWelcomeWarning1": {"message": "Flask는 개발자가 불안정한 신규 API를 실험하기 위한 것입니다. 개발자나 베타 테스터가 아니면 $1하세요.", "description": "This is a warning shown on the Flask Welcome screen, intended to encourage non-developers not to proceed any further. $1 is the bolded message 'flaskWelcomeUninstall'"}, "flaskWelcomeWarning2": {"message": "이 확장 프로그램은 안전성이나 안정성을 보장하지 않습니다. Flask에서 제공하는 새로운 API는 피싱 공격에 대비한 강화 과정을 거치지 않았습니다. 즉, Flask가 필요한 모든 사이트나 스냅은 자산을 훔치려는 악의적 시도일 수 있습니다.", "description": "This explains the risks of using NeoNix Flask"}, "flaskWelcomeWarning3": {"message": "모든 Flask의 API는 실험 단계입니다. 따라서 예고 없이 변경, 제거되거나 안정적인 NeoNix로 마이그레이션되지 않고 Flask에 무기한 남아 있을 수 있습니다. 사용 시 위험은 본인 책임입니다.", "description": "This message warns developers about unstable Flask APIs"}, "flaskWelcomeWarning4": {"message": "Flask를 사용할 때는 정규 NeoNix 익스텐션을 비활성화하세요.", "description": "This message calls to pay attention about multiple versions of NeoNix running on the same site (Flask + Prod)"}, "flaskWelcomeWarningAcceptButton": {"message": "본인은 이 위험을 감수합니다", "description": "this text is shown on a button, which the user presses to confirm they understand the risks of using Flask"}, "floatAmountToken": {"message": "토큰 금액은 정수여야 합니다"}, "followUsOnTwitter": {"message": "트위터에서 팔로우하세요"}, "forbiddenIpfsGateway": {"message": "금지된 IPFS 게이트웨이: CID 게이트웨이를 지정하세요."}, "forgetDevice": {"message": "이 장치 삭제"}, "forgotPassword": {"message": "비밀번호를 잊으셨나요?"}, "form": {"message": "양식"}, "from": {"message": "발신"}, "fromAddress": {"message": "발신: $1", "description": "$1 is the address to include in the From label. It is typically shortened first using shortenAddress"}, "fromTokenLists": {"message": "가져올 토큰 목록: $1"}, "function": {"message": "기능: $1"}, "fundingMethod": {"message": "자금 조달 방법"}, "gas": {"message": "가스"}, "gasDisplayAcknowledgeDappButtonText": {"message": "제안된 가스비 편집"}, "gasDisplayDappWarning": {"message": "$1에서 이 가스비를 제안했습니다. 이를 무시하면 트랜잭션에 문제가 발생할 수 있습니다. 질문이 있는 경우 $1에 문의하세요.", "description": "$1 represents the Dapp's origin"}, "gasFee": {"message": "가스비"}, "gasLimit": {"message": "가스 한도"}, "gasLimitRecommended": {"message": "권장 가스비 한도는 $1입니다. 가스 한도가 이보다 낮으면 실패할 수 있습니다."}, "gasLimitTooLow": {"message": "가스 한도는 21000 이상이어야 합니다."}, "gasLimitV2": {"message": "가스 한도"}, "gasOption": {"message": "가스 옵션"}, "gasPriceExcessive": {"message": "가스비가 불필요하게 높게 설정되었습니다. 가격을 낮추는 것을 고려해 보세요."}, "gasPriceFetchFailed": {"message": "네트워크 오류로 인해 가스 가격 견적 추산을 실패했습니다."}, "gasTimingHoursShort": {"message": "$1 시간", "description": "$1 represents a number of hours"}, "gasTimingLow": {"message": "느림"}, "gasTimingMinutesShort": {"message": "$1 분", "description": "$1 represents a number of minutes"}, "gasTimingSecondsShort": {"message": "$1 초", "description": "$1 represents a number of seconds"}, "gasUsed": {"message": "가스 사용됨"}, "general": {"message": "일반"}, "generalCameraError": {"message": "카메라에 액세스할 수 없습니다. 다시 시도하세요."}, "generalCameraErrorTitle": {"message": "오류가 발생했습니다...."}, "generalDescription": {"message": "여러 기기의 설정을 동기화하고, 네트워크 환경을 선택하며, 토큰 데이터를 추적하세요"}, "genericExplorerView": {"message": "$1에서 계정 보기"}, "goToSite": {"message": "사이트로 이동"}, "goerli": {"message": "Goerli 테스트 네트워크"}, "gotIt": {"message": "확인"}, "grantExactAccess": {"message": "정확한 접근 허용"}, "gwei": {"message": "GWEI"}, "hardware": {"message": "하드웨어"}, "hardwareWalletConnected": {"message": "하드웨어 지갑 연결됨"}, "hardwareWalletLegacyDescription": {"message": "(레거시)", "description": "Text representing the MEW path"}, "hardwareWalletSubmissionWarningStep1": {"message": "$1(이)가 연결되어 있고 이더리움 앱을 선택했는지 확인하세요."}, "hardwareWalletSubmissionWarningStep2": {"message": "$1 장치에서 '스마트 계약 데이터' 또는 '블라인드 서명'을 활성화하세요."}, "hardwareWalletSubmissionWarningTitle": {"message": "제출을 클릭하기 전:"}, "hardwareWalletSupportLinkConversion": {"message": "여기를 클릭"}, "hardwareWallets": {"message": "하드웨어 지갑 연결"}, "hardwareWalletsInfo": {"message": "하드웨어 지갑을 통합하면 외부 서버에 API 호출을 사용하며, 이 서버는 사용자의 IP 주소와 사용자와 인터렉션하는 스마트 계약의 주소를 볼 수 있습니다."}, "hardwareWalletsMsg": {"message": "NeoNix와 함께 사용할 하드웨어 지갑을 선택하세요."}, "here": {"message": "여기", "description": "as in -click here- for more information (goes with troubleTokenBalances)"}, "hexData": {"message": "헥스 데이터"}, "hiddenAccounts": {"message": "숨긴 계정"}, "hide": {"message": "숨기기"}, "hideAccount": {"message": "계정 숨기기"}, "hideAdvancedDetails": {"message": "고급 세부 정보 숨기기"}, "hideSentitiveInfo": {"message": "민감한 정보 숨기기"}, "hideTokenPrompt": {"message": "토큰을 숨기겠습니까?"}, "hideTokenSymbol": {"message": "$1 숨기기", "description": "$1 is the symbol for a token (e.g. 'DAI')"}, "hideZeroBalanceTokens": {"message": "잔액 없는 토큰 숨기기"}, "high": {"message": "공격적"}, "highGasSettingToolTipMessage": {"message": "변동성이 크고 심지어 휘발성이 높은 시장입니다. $1 이용으로 인기 NFT 드롭 등으로 인한 네트워크 트래픽 급증에 대응하세요.", "description": "$1 is key 'high' (text: 'Aggressive') separated here so that it can be passed in with bold font-weight"}, "highLowercase": {"message": "높음"}, "highestCurrentBid": {"message": "현재 최고 입찰"}, "highestFloorPrice": {"message": "최고 바닥 가격"}, "history": {"message": "기록"}, "holdToRevealContent1": {"message": "비밀복구구문이 있으면 $1 기능을 사용할 수 있습니다", "description": "$1 is a bolded text with the message from 'holdToRevealContent2'"}, "holdToRevealContent2": {"message": "지갑과 자금 모두에 액세스할 수 있습니다.", "description": "Is the bolded text in 'holdToRevealContent1'"}, "holdToRevealContent3": {"message": "이는 누구와도 공유하지 마세요. $1 $2", "description": "$1 is a message from 'holdToRevealContent4' and $2 is a text link with the message from 'holdToRevealContent5'"}, "holdToRevealContent4": {"message": "NeoNix 지원팀은 이러한 정보를 절대로 묻지 않습니다,", "description": "Part of 'holdToRevealContent3'"}, "holdToRevealContent5": {"message": "오히려 피싱 사기꾼들이 요구할 수 있으니 주의가 필요합니다.", "description": "The text link in 'holdToRevealContent3'"}, "holdToRevealContentPrivateKey1": {"message": "개인 키가 있으면 $1 기능을 사용할 수 있습니다", "description": "$1 is a bolded text with the message from 'holdToRevealContentPrivateKey2'"}, "holdToRevealContentPrivateKey2": {"message": "지갑과 자금 모두에 액세스할 수 있습니다.", "description": "Is the bolded text in 'holdToRevealContentPrivateKey2'"}, "holdToRevealLockedLabel": {"message": "눌러서 잠긴 서클 공개"}, "holdToRevealPrivateKey": {"message": "눌러서 개인 키 공개"}, "holdToRevealPrivateKeyTitle": {"message": "개인 키를 안전하게 보관하세요"}, "holdToRevealSRP": {"message": "눌러서 SRP 공개"}, "holdToRevealSRPTitle": {"message": "SRP를 안전하게 보관하세요"}, "holdToRevealUnlockedLabel": {"message": "눌러서 잠금해제된 서클 공개"}, "honeypotDescription": {"message": "이 토큰은 허니팟 위험이 있을 수 있습니다. 상호작용 전에 신중히 조사하여 잠재적인 손실을 예방하세요."}, "honeypotTitle": {"message": "허니팟"}, "howNetworkFeesWorkExplanation": {"message": "트랜잭션을 처리하기 위해 필요한 예상 수수료입니다. 최대 수수료는 $1입니다."}, "howQuotesWork": {"message": "견적 방식"}, "howQuotesWorkExplanation": {"message": "이 견적은 검색한 견적 중 최고의 수익률을 제공합니다. 이는 브릿지 수수료와 $1% NeoNix 수수료를 포함한 스왑 비율에서 가스비를 뺀 것을 기준으로 합니다. 가스비는 네트워크 혼잡도와 트랜잭션의 복잡성에 따라 달라집니다."}, "id": {"message": "ID"}, "ignoreAll": {"message": "모두 무시"}, "ignoreTokenWarning": {"message": "토큰을 숨기면 지갑에 표시되지 않습니다. 하지만 이를 검색하여 추가할 수 있습니다."}, "imToken": {"message": "imToken"}, "import": {"message": "가져오기", "description": "Button to import an account from a selected file"}, "importAccountError": {"message": "계정 가져오기 오류"}, "importAccountErrorIsSRP": {"message": "비밀복구구문 (또는 니모닉)을 입력하셨습니다. 계정을 이곳으로 불러오려면 64자의 16진수 문자열인 개인 키를 입력해야 합니다."}, "importAccountErrorNotAValidPrivateKey": {"message": "유효한 개인 키가 아닙니다. 16진수 문자열을 입력했지만 64자리가 아닙니다."}, "importAccountErrorNotHexadecimal": {"message": "유효한 개인 키가 아닙니다. 반드시 64자의 16진수 문자열인 개인 키를 입력해야 합니다."}, "importAccountJsonLoading1": {"message": "이 JSON 파일을 가져오려면 몇 분 정도 걸리며 NeoNix가 정지됩니다."}, "importAccountJsonLoading2": {"message": "죄송합니다. 향후에는 더 빠르게 개선하겠습니다."}, "importAccountMsg": {"message": "가져온 계정은 NeoNix 계정 비밀복구구문과 연결하지 못합니다. 가져온 계정에 대해 자세히 알아보기"}, "importNFT": {"message": "NFT 가져오기"}, "importNFTAddressToolTip": {"message": "예를 들어 OpenSea의 NFT 페이지 세부 정보에는 '계약 주소'라는 파란색 하이퍼링크 값이 있습니다. 이를 클릭하면 Etherscan의 계약 주소로 이동합니다. 해당 페이지의 왼쪽 상단에는 '계약(Contract)'이라고 표시된 아이콘이 있고 오른쪽으로 문자와 숫자로 구성된 긴 문자열이 있어야 합니다. 이것이 바로 NFT를 생성한 계약 주소입니다. 주소 오른쪽의 '복사' 아이콘을 클릭하면 이를 클립보드에 저장할 수 있습니다."}, "importNFTPage": {"message": "NFT 페이지 가져오기"}, "importNFTTokenIdToolTip": {"message": "NFT의 ID는 고유한 식별자이므로 동일한 NFT는 존재하지 않습니다. 다시 말하지만, OpenSea에서 이 번호는 '세부 정보'에서 찾아볼 수 있습니다. 이를 기록하거나 클립보드에 복사해 두세요."}, "importNWordSRP": {"message": "$1단어 복구 구문이 있습니다", "description": "$1 is the number of words in the recovery phrase"}, "importPrivateKey": {"message": "개인 키"}, "importSRPDescription": {"message": "12개 또는 24개의 단어로 된 비밀복구구문으로 기존 지갑을 가져오세요."}, "importSRPNumberOfWordsError": {"message": "비밀복구구문은 12개 또는 24개의 단어로 구성되어 있습니다"}, "importSRPWordError": {"message": "$1 단어가 잘못되었거나 철자가 틀렸습니다.", "description": "$1 is the word that is incorrect or misspelled"}, "importSRPWordErrorAlternative": {"message": "$1 및 $2 단어가 잘못되었거나 철자가 틀렸습니다.", "description": "$1 and $2 are multiple words that are mispelled."}, "importSecretRecoveryPhrase": {"message": "비밀복구구문 가져오기"}, "importSecretRecoveryPhraseUnknownError": {"message": "알 수 없는 오류가 발생했습니다."}, "importSelectedTokens": {"message": "선택한 토큰을 불러올까요?"}, "importSelectedTokensDescription": {"message": "선택한 토큰만 지갑에 표시됩니다. 숨긴 토큰은 토큰 검색을 통해 나중에 언제든지 불러올 수 있습니다."}, "importTokenQuestion": {"message": "토큰을 가져올까요?"}, "importTokenWarning": {"message": "기존 토큰의 가짜 버전을 포함하여 누구나 어떤 이름으로든 토큰을 만들 수 있습니다. 추가 및 트랜잭션은 사용자의 책임입니다."}, "importTokensCamelCase": {"message": "토큰 가져오기"}, "importTokensError": {"message": "토큰을 가져올 수 없습니다. 나중에 다시 시도하세요."}, "importWallet": {"message": "지갑 가져오기"}, "importWalletOrAccountHeader": {"message": "지갑 또는 계정 가져오기"}, "importWalletSuccess": {"message": "비밀복구구문 $1 가져옴", "description": "$1 is the index of the secret recovery phrase"}, "importWithCount": {"message": "$1 불러오기", "description": "$1 will the number of detected tokens that are selected for importing, if all of them are selected then $1 will be all"}, "imported": {"message": "가져옴", "description": "status showing that an account has been fully loaded into the keyring"}, "inYourSettings": {"message": "설정에서"}, "included": {"message": "포함됨"}, "includesXTransactions": {"message": "$1건의 트랜잭션 포함"}, "infuraBlockedNotification": {"message": "NeoNix이 블록체인 호스트에 연결할 수 없습니다. $1 오류 가능성을 검토하세요.", "description": "$1 is a clickable link with with text defined by the 'here' key"}, "initialTransactionConfirmed": {"message": "최초 트랜잭션을 네트워크에서 컨펌했습니다. 돌아가려면 컨펌을 클릭하세요."}, "insightsFromSnap": {"message": "$1 인사이트", "description": "$1 represents the name of the snap"}, "install": {"message": "설치"}, "installOrigin": {"message": "출처 설치"}, "installRequest": {"message": "NeoNix에 추가"}, "installedOn": {"message": "$1에 설치됨", "description": "$1 is the date when the snap has been installed"}, "insufficientBalance": {"message": "잔액이 부족합니다."}, "insufficientFunds": {"message": "자금이 부족합니다."}, "insufficientFundsForGas": {"message": "가스 자금 부족"}, "insufficientLockedLiquidityDescription": {"message": "유동성 토큰을 적절히 락업하거나 소각하지 않아 토큰이 급격한 유동성 이탈에 취약하므로 시장 불안정을 초래할 수 있습니다."}, "insufficientLockedLiquidityTitle": {"message": "유동성 락업 부족"}, "insufficientTokens": {"message": "토큰이 부족합니다."}, "interactWithSmartContract": {"message": "스마트 계약"}, "interactingWith": {"message": "다음과 상호 작용:"}, "interactingWithTransactionDescription": {"message": "지금 상호작용하고 있는 계약입니다. 세부 정보를 확인하여 사기를 방지하세요."}, "interaction": {"message": "상호작용"}, "invalidAddress": {"message": "잘못된 주소"}, "invalidAddressRecipient": {"message": "수신 주소가 올바르지 않음"}, "invalidAssetType": {"message": "이 자산은 NFT이므로 NFT 탭에 있는 NFT 가져오기 페이지에서 다시 추가해야 합니다"}, "invalidChainIdTooBig": {"message": "잘못된 체인 ID. 체인 ID가 너무 큽니다."}, "invalidCustomNetworkAlertContent1": {"message": "맞춤 네트워크 '$1'의 체인 ID를 다시 입력해야 합니다.", "description": "$1 is the name/identifier of the network."}, "invalidCustomNetworkAlertContent2": {"message": "악성 또는 결함이 있는 네트워크 공급업체의 공격을 방어하려면, 이제 모든 맞춤 네트워크에 체인 ID를 사용해야 합니다."}, "invalidCustomNetworkAlertContent3": {"message": "설정 > 네트워크로 이동한 후 체인 ID를 입력하세요. $1에서 가장 인기 있는 네트워크의 체인 ID를 찾을 수 있습니다.", "description": "$1 is a link to https://chainid.network"}, "invalidCustomNetworkAlertTitle": {"message": "잘못된 맞춤 네트워크"}, "invalidHexData": {"message": "잘못된 헥스 데이터"}, "invalidHexNumber": {"message": "잘못된 16진수입니다."}, "invalidHexNumberLeadingZeros": {"message": "잘못된 16진수입니다. 앞에 있는 0을 모두 제거하세요."}, "invalidIpfsGateway": {"message": "잘못된 IPFS 게이트웨이: 값은 올바른 URL이어야 합니다."}, "invalidNumber": {"message": "숫자가 올바르지 않습니다. 십진수나 '0x'로 시작하는 16진수를 입력하세요."}, "invalidNumberLeadingZeros": {"message": "숫자가 올바르지 않습니다. 앞에 있는 0을 모두 제거하세요."}, "invalidRPC": {"message": "잘못된 RPC URL"}, "invalidSeedPhrase": {"message": "잘못된 비밀복구구문"}, "invalidSeedPhraseCaseSensitive": {"message": "입력 오류: 비밀복구구문은 대소문자를 구분해야 합니다."}, "ipfsGateway": {"message": "IPFS 게이트웨이"}, "ipfsGatewayDescription": {"message": "NeoNix는 타사 서비스를 이용하여 IPFS에 저장된 NFT 이미지를 표시하고, 브라우저 주소창에 입력한 ENS 주소 관련 정보도 표시하며, 다른 토큰의 아이콘도 가져옵니다. 이 기능을 이용하면 IP 주소가 해당 서비스에 노출될 수 있습니다."}, "ipfsToggleModalDescriptionOne": {"message": "당사는 타사 서비스를 사용하여 IPFS에 저장된 NFT 이미지를 표시하고, 브라우저 주소창에 입력된 ENS 주소와 관련된 정보를 표시하며, 다양한 토큰의 아이콘을 가져옵니다. 이러한 서비스를 사용하면 귀하의 IP 주소가 해당 서비스에 노출될 수 있습니다."}, "ipfsToggleModalDescriptionTwo": {"message": "컨펌을 선택하면 IPFS 해상도가 활성화됩니다. 언제든지 $1에서 비활성화할 수 있습니다.", "description": "$1 is the method to turn off ipfs"}, "ipfsToggleModalSettings": {"message": "설정 > 보안 및 개인정보"}, "isSigningOrSubmitting": {"message": "이전 트랜잭션이 아직 서명 또는 제출 중입니다."}, "jazzAndBlockies": {"message": "Jazzicons와 Blockies는 계정을 한눈에 식별할 수 있게 도와주는 두 가지 고유한 아이콘 스타일입니다."}, "jazzicons": {"message": "Jazzicons"}, "jsonFile": {"message": "JSON 파일", "description": "format for importing an account"}, "keyringAccountName": {"message": "계정 이름"}, "keyringAccountPublicAddress": {"message": "공개 주소"}, "keyringSnapRemovalResult1": {"message": "$1 제거 $2", "description": "Displays the result after removal of a keyring snap. $1 is the snap name, $2 is whether it is successful or not"}, "keyringSnapRemovalResultNotSuccessful": {"message": "실패 ", "description": "Displays the `not` word in $2."}, "keyringSnapRemoveConfirmation": {"message": "$1을(를) 입력하여 이 스냅을 제거할지 확인합니다:", "description": "Asks user to input the name nap prior to deleting the snap. $1 is the snap name"}, "keystone": {"message": "Keystone"}, "knownAddressRecipient": {"message": "알려진 계약 주소입니다."}, "knownTokenWarning": {"message": "이 작업은 지갑에 목록에 등재되어 피싱에 사용될 수 있는 토큰을 편집합니다. 해당 토큰이 나타내는 내용을 변경하려는 경우에만 작업을 승인하세요. $1에 대해 알아보기"}, "l1Fee": {"message": "L1 수수료"}, "l1FeeTooltip": {"message": "L1 가스비"}, "l2Fee": {"message": "L2 수수료"}, "l2FeeTooltip": {"message": "L2 가스비"}, "lastConnected": {"message": "마지막 연결"}, "lastSold": {"message": "최근 판매"}, "lavaDomeCopyWarning": {"message": "안전을 위해 지금은 이 텍스트를 선택할 수 없습니다."}, "layer1Fees": {"message": "레이어 1 요금"}, "layer2Fees": {"message": "레이어 2 요금"}, "learnHow": {"message": "방법 확인하기"}, "learnMore": {"message": "자세히 알아보기"}, "learnMoreAboutGas": {"message": "가스에 대해 $1하시겠습니까?", "description": "$1 will be replaced by the learnMore translation key"}, "learnMoreAboutPrivacy": {"message": "개인정보 보호 모범 사례에 대해 자세히 알아보세요."}, "learnMoreAboutSolanaAccounts": {"message": "솔라나 계정에 대해 더 자세히 알아보기"}, "learnMoreKeystone": {"message": "자세히 알아보기"}, "learnMoreUpperCase": {"message": "자세히 알아보기"}, "learnMoreUpperCaseWithDot": {"message": "자세히 알아보세요."}, "learnScamRisk": {"message": "사기 및 보안 위험"}, "leaveNeoNix": {"message": "NeoNix를 나가시겠습니까?"}, "leaveNeoNixDesc": {"message": "NeoNix 외부의 사이트를 방문하려고 합니다. 계속하기 전에 URL을 다시 한번 확인하세요."}, "ledgerAccountRestriction": {"message": "새 계정을 추가하려면 먼저 기존의 최종 계정을 사용해야 합니다."}, "ledgerConnectionInstructionCloseOtherApps": {"message": "장치에 연결된 다른 소프트웨어를 닫은 다음 여기를 클릭하여 새로 고침하세요."}, "ledgerConnectionInstructionHeader": {"message": "컨펌을 클릭하기 전:"}, "ledgerConnectionInstructionStepFour": {"message": "Ledger 장치에서 \"스마트 계약 데이터\" 또는 \"블라인드 서명\"을 활성화하세요"}, "ledgerConnectionInstructionStepThree": {"message": "Ledger 장치를 연결하고 이더리움 앱을 선택하세요."}, "ledgerDeviceOpenFailureMessage": {"message": "Ledger 장치를 열지 못했습니다. Ledger가 다른 소프트웨어에 연결되어 있을 수 있습니다. Ledger Live 또는 Ledger 장치에 연결된 다른 응용 프로그램을 닫고 다시 연결하세요."}, "ledgerErrorConnectionIssue": {"message": "Ledger를 다시 연결하고, ETH 앱을 열어 다시 시도하세요."}, "ledgerErrorDevicedLocked": {"message": "Ledger가 잠겨있습니다. 잠금 해제하고 다시 시도하세요."}, "ledgerErrorEthAppNotOpen": {"message": "문제를 해결하려면 기기에서 ETH 앱을 열고 다시 시도하세요."}, "ledgerErrorTransactionDataNotPadded": {"message": "이더리움 트랜잭션의 입력 데이터가 충분히 패딩되지 않았습니다."}, "ledgerLiveApp": {"message": "Ledger Live 앱"}, "ledgerLocked": {"message": "Ledger 장치에 연결할 수 없습니다. 장치의 잠금이 해제되어 있고 이더리움 앱이 열려 있는지 확인하세요."}, "ledgerMultipleDevicesUnsupportedInfoDescription": {"message": "새 장치를 연결하려면 이전 장치의 연결을 해제하세요."}, "ledgerMultipleDevicesUnsupportedInfoTitle": {"message": "한 번에 하나의 Ledger만 연결할 수 있습니다"}, "ledgerTimeout": {"message": "Ledger Live의 응답 시간이 너무 길거나 연결 시간을 초과했습니다. Ledger Live 앱이 열려 있고 장치의 잠금이 해제되어 있는지 확인하세요."}, "ledgerWebHIDNotConnectedErrorMessage": {"message": "Ledger 장치가 연결되지 않았습니다. Ledger를 연결하려면 '계속'을 다시 클릭하고 HID 연결을 승인하세요.", "description": "An error message shown to the user during the hardware connect flow."}, "levelArrow": {"message": "수평 화살표"}, "lightTheme": {"message": "라이트"}, "likeToImportToken": {"message": "이 토큰을 가져올까요?"}, "likeToImportTokens": {"message": "이 토큰을 추가하시겠습니까?"}, "lineaGoerli": {"message": "Linea Goerli 테스트 네트워크"}, "lineaMainnet": {"message": "Linea 메인넷"}, "lineaSepolia": {"message": "Linea Sepolia 테스트 네트워크"}, "link": {"message": "링크"}, "linkCentralizedExchanges": {"message": "Coinbase나 Binance 계정을 연결하여 무료로 암호화폐를 NeoNix로 전송하세요."}, "links": {"message": "링크"}, "loadMore": {"message": "더 불러오기"}, "loading": {"message": "로드 중..."}, "loadingScreenSnapMessage": {"message": "Snap에서 트랜잭션을 완료하세요."}, "loadingTokenList": {"message": "토큰 목록 불러오는 중"}, "localhost": {"message": "Localhost 8545"}, "lock": {"message": "잠금"}, "lockNeoNix": {"message": "NeoNix 고정"}, "lockTimeInvalid": {"message": "잠금 시간은 반드시 0에서 10080 사이여야 합니다"}, "logo": {"message": "$1 로고", "description": "$1 is the name of the ticker"}, "low": {"message": "낮음"}, "lowEstimatedReturnTooltipMessage": {"message": "시작 금액의 $1% 이상을 수수료로 지불하게 됩니다. 받는 금액과 네트워크 수수료를 확인하세요."}, "lowEstimatedReturnTooltipTitle": {"message": "높은 비용"}, "lowGasSettingToolTipMessage": {"message": "$1 사용을 통해 더 저렴한 가격을 기다리세요. 가격 예측이 힘들기 때문에 시간 추정은 더욱 부정확합니다.", "description": "$1 is key 'low' separated here so that it can be passed in with bold font-weight"}, "lowLowercase": {"message": "낮음"}, "mainnet": {"message": "이더리움 메인넷"}, "mainnetToken": {"message": "이 주소는 알려진 이더리움 메인넷 토큰 주소와 일치합니다. 추가하려는 토큰의 계약 주소와 네트워크를 다시 확인하세요."}, "makeAnotherSwap": {"message": "새 스왑 생성"}, "makeSureNoOneWatching": {"message": "다른 사람이 이 화면을 보고 있지는 않은지 확인하세요.", "description": "Warning to users to be care while creating and saving their new Secret Recovery Phrase"}, "manageDefaultSettings": {"message": "기본 개인정보 보호 설정 관리"}, "manageInstitutionalWallets": {"message": "Institutional 지갑 관리"}, "manageInstitutionalWalletsDescription": {"message": "이 옵션을 켜면 Institutional 지갑이 활성화됩니다"}, "manageNetworksMenuHeading": {"message": "네트워크 관리"}, "managePermissions": {"message": "권한 관리"}, "marketCap": {"message": "시가 총액"}, "marketDetails": {"message": "시장 상세 정보"}, "max": {"message": "최대"}, "maxBaseFee": {"message": "최대 기본 요금"}, "maxFee": {"message": "최대 요금"}, "maxFeeTooltip": {"message": "트랜잭션에 지불하기 위해 제공되는 최대 수수료입니다."}, "maxPriorityFee": {"message": "최대 우선 요금"}, "medium": {"message": "시장"}, "mediumGasSettingToolTipMessage": {"message": "현재 시장 가격으로 빠르게 처리할 수 있도록 $1을(를) 사용하세요.", "description": "$1 is key 'medium' (text: 'Market') separated here so that it can be passed in with bold font-weight"}, "memo": {"message": "메모"}, "message": {"message": "메시지"}, "NeoNixConnectStatusParagraphOne": {"message": "이제 NeoNix에서 계정 연결을 더 효과적으로 제어할 수 있습니다."}, "NeoNixConnectStatusParagraphThree": {"message": "클릭하여 연결된 계정을 관리하세요."}, "NeoNixConnectStatusParagraphTwo": {"message": "방문 중인 웹사이트가 현재 선택한 계정에 연결되어 있다면 연결 상태 버튼이 표시됩니다."}, "metaMetricsIdNotAvailableError": {"message": "MetaMetrics에 가입한 적이 없으므로 여기에는 삭제할 데이터가 없습니다"}, "metadataModalSourceTooltip": {"message": "$1 스냅은 npm이 호스팅합니다. 이 Snap의 고유 식별자는 $2 입니다.", "description": "$1 is the snap name and $2 is the snap NPM id."}, "NeoNixNotificationsAreOff": {"message": "현재 지갑 알림이 꺼져 있습니다."}, "NeoNixSwapsOfflineDescription": {"message": "NeoNix Swaps 점검 중입니다. 나중에 다시 확인하세요."}, "NeoNixVersion": {"message": "NeoNix 버전"}, "methodData": {"message": "메소드"}, "methodDataTransactionDesc": {"message": "디코딩된 입력 데이터를 기반으로 실행되는 함수입니다."}, "methodNotSupported": {"message": "이 계정에서는 지원되지 않습니다."}, "metrics": {"message": "메트릭"}, "millionAbbreviation": {"message": "M", "description": "Shortened form of 'million'"}, "mismatchedChainLinkText": {"message": "네트워크 세부 정보 검증", "description": "Serves as link text for the 'mismatched<PERSON><PERSON><PERSON>' key. This text will be embedded inside the translation for that key."}, "mismatchedChainRecommendation": {"message": "계속 진행하기 전에 $1(을)를 확인하시기 바랍니다.", "description": "$1 is a clickable link with text defined by the 'mismatchedChainLinkText' key. The link will open to instructions for users to validate custom network details."}, "mismatchedNetworkName": {"message": "기록에 따르면 네트워크 이름이 이 체인 ID와 일치하지 않습니다."}, "mismatchedNetworkSymbol": {"message": "제출한 화폐 기호가 이 체인 ID의 화폐 기호와 일치하지 않습니다."}, "mismatchedRpcChainId": {"message": "사용자 맞춤 네트워크의 체인 ID가 제출된 체인 ID와 일치하지 않습니다."}, "mismatchedRpcUrl": {"message": "기록에 따르면 제출한 RPC URL 값이 이 체인 ID의 알려진 공급업체와 일치하지 않습니다."}, "missingSetting": {"message": "설정을 찾으세요?"}, "missingSettingRequest": {"message": "여기에서 요청하세요"}, "more": {"message": "그 외"}, "moreAccounts": {"message": "$1개의 계정 추가", "description": "$1 is the number of accounts"}, "moreNetworks": {"message": "$1개의 네트워크 추가", "description": "$1 is the number of networks"}, "moreQuotes": {"message": "더 많은 견적"}, "multichainAddEthereumChainConfirmationDescription": {"message": "NeoNix에 이 네트워크를 추가하고 이 사이트에 사용 권한을 허용합니다."}, "multichainQuoteCardBridgingLabel": {"message": "브릿징"}, "multichainQuoteCardQuoteLabel": {"message": "견적"}, "multichainQuoteCardTimeLabel": {"message": "시간"}, "multipleSnapConnectionWarning": {"message": "$1에서 $2 Snap 사용을 원합니다.", "description": "$1 is the dapp and $2 is the number of snaps it wants to connect to."}, "mustSelectOne": {"message": "토큰을 1개 이상 선택해야 합니다."}, "name": {"message": "이름"}, "nameAddressLabel": {"message": "주소", "description": "Label above address field in name component modal."}, "nameAlreadyInUse": {"message": "이미 사용 중인 이름입니다"}, "nameInstructionsNew": {"message": "이 주소를 알고 있다면 나중에 알아볼 수 있도록 닉네임을 지정하세요.", "description": "Instruction text in name component modal when value is not recognised."}, "nameInstructionsRecognized": {"message": "이 주소에는 기본 닉네임이 있지만, 편집하거나 다른 추천 닉네임을 찾아볼 수 있습니다.", "description": "Instruction text in name component modal when value is recognized but not saved."}, "nameInstructionsSaved": {"message": "이 전에 이 주소에 닉네임을 추가한 적이 있습니다. 편집하거나 다른 추천 닉네임을 볼 수 있습니다.", "description": "Instruction text in name component modal when value is saved."}, "nameLabel": {"message": "닉네임", "description": "Label above name input field in name component modal."}, "nameModalMaybeProposedName": {"message": "가능한 닉네임: $1", "description": "$1 is the proposed name"}, "nameModalTitleNew": {"message": "알 수 없는 주소", "description": "Title of the modal created by the name component when value is not recognised."}, "nameModalTitleRecognized": {"message": "인식된 주소", "description": "Title of the modal created by the name component when value is recognized but not saved."}, "nameModalTitleSaved": {"message": "저장된 주소", "description": "Title of the modal created by the name component when value is saved."}, "nameProviderProposedBy": {"message": "추천인: $1", "description": "$1 is the name of the provider"}, "nameProvider_ens": {"message": "이더리움 네임 서비스(ENS)"}, "nameProvider_etherscan": {"message": "Etherscan"}, "nameProvider_lens": {"message": "Lens Protocol"}, "nameProvider_token": {"message": "NeoNix"}, "nameSetPlaceholder": {"message": "닉네임을 선택하세요...", "description": "Placeholder text for name input field in name component modal."}, "nativeNetworkPermissionRequestDescription": {"message": "$1에서 다음 승인을 요청합니다:", "description": "$1 represents dapp name"}, "nativeTokenScamWarningConversion": {"message": "네트워크 세부 정보 편집"}, "nativeTokenScamWarningDescription": {"message": "토큰의 네이티브 심볼이 체인 ID와 연관된 네트워크의 토큰의 네이티브 심볼과 일치하지 않습니다. $1을(를) 입력하셨지만 예상되는 토큰의 네이티브 심볼은 $2입니다. 올바른 체인에 연결되어 있는지 확인해 주세요.", "description": "$1 represents the currency name, $2 represents the expected currency symbol"}, "nativeTokenScamWarningDescriptionExpectedTokenFallback": {"message": "다른 선택", "description": "graceful fallback for when token symbol isn't found"}, "nativeTokenScamWarningTitle": {"message": "비통상적 네이티브 토큰 심볼", "description": "Title for nativeTokenScamWarningDescription"}, "needHelp": {"message": "도움이 필요하신가요? $1에 문의하세요.", "description": "$1 represents `needHelpLinkText`, the text which goes in the help link"}, "needHelpFeedback": {"message": "피드백 공유"}, "needHelpLinkText": {"message": "NeoNix 지원"}, "needHelpSubmitTicket": {"message": "티켓 제출"}, "needImportFile": {"message": "반드시 가져올 파일을 선택해야 합니다.", "description": "User is important an account and needs to add a file to continue"}, "negativeETH": {"message": "음수 ETH 양은 전송할 수 없습니다."}, "negativeOrZeroAmountToken": {"message": "마이너스 또는 금액이 0인 자산은 보낼 수 없습니다."}, "network": {"message": "네트워크:"}, "networkChanged": {"message": "네트워크 변경됨"}, "networkChangedMessage": {"message": "이제 $1 네트워크에서 트랜잭션 중입니다.", "description": "$1 is the name of the network"}, "networkDetails": {"message": "네트워크 세부 정보"}, "networkFee": {"message": "네트워크 수수료"}, "networkIsBusy": {"message": "네트워크 사용량이 많습니다. 가스비가 높고 견적의 정확도도 떨어집니다."}, "networkMenu": {"message": "네트워크 메뉴"}, "networkMenuHeading": {"message": "네트워크 선택"}, "networkName": {"message": "네트워크 이름"}, "networkNameArbitrum": {"message": "Arbitrum"}, "networkNameAvalanche": {"message": "Avalanche"}, "networkNameBSC": {"message": "BSC"}, "networkNameBase": {"message": "기본"}, "networkNameBitcoin": {"message": "비트코인"}, "networkNameDefinition": {"message": "이 네트워크와 연결된 이름입니다."}, "networkNameEthereum": {"message": "이더리움"}, "networkNameGoerli": {"message": "<PERSON><PERSON><PERSON>"}, "networkNameLinea": {"message": "Linea"}, "networkNameOpMainnet": {"message": "OP 메인넷"}, "networkNamePolygon": {"message": "Polygon"}, "networkNameSolana": {"message": "솔라나"}, "networkNameTestnet": {"message": "테스트넷"}, "networkNameZkSyncEra": {"message": "zkSync Era"}, "networkOptions": {"message": "네트워크 옵션"}, "networkPermissionToast": {"message": "네트워크 권한이 업데이트됨"}, "networkProvider": {"message": "네트워크 공급업체"}, "networkStatus": {"message": "네트워크 상태"}, "networkStatusBaseFeeTooltip": {"message": "기본 요금은 네트워크에 의해 설정되며 13-14초마다 변경됩니다. 당사의 $1 및 $2 옵션이 갑작스러운 증가를 설명해줍니다.", "description": "$1 and $2 are bold text for Medium and Aggressive respectively."}, "networkStatusPriorityFeeTooltip": {"message": "우선 요금의 범위(일명 \"채굴자 팁\"). 이것은 채굴자에게 전달되어 트랜잭션의 우선 순위를 정하도록 장려합니다."}, "networkStatusStabilityFeeTooltip": {"message": "가스비가 지난 72시간에 비해 $1 상태입니다.", "description": "$1 is networks stability value - stable, low, high"}, "networkSwitchConnectionError": {"message": "$1 연결이 불가능합니다", "description": "$1 represents the network name"}, "networkURL": {"message": "네트워크 URL"}, "networkURLDefinition": {"message": "이 네트워크에 액세스하는 데 사용되는 URL입니다."}, "networkUrlErrorWarning": {"message": "공격자는 사이트 주소를 약간 변경하여 유사 사이트를 만들기도 합니다. 계속하기 전에 정상적인 사이트와 상호 작용하고 있는지 확인하세요. Punycode 버전: $1", "description": "$1 replaced by RPC URL for network"}, "networks": {"message": "네트워크"}, "networksSmallCase": {"message": "네트워크"}, "nevermind": {"message": "괜찮습니다"}, "new": {"message": "신규!"}, "newAccount": {"message": "새 계정"}, "newAccountNumberName": {"message": "계정 $1", "description": "Default name of next account to be created on create account screen"}, "newContact": {"message": "새 연락처"}, "newContract": {"message": "새 계약"}, "newNFTDetectedInImportNFTsMessageStrongText": {"message": "설정 > 보안 및 개인정보"}, "newNFTDetectedInImportNFTsMsg": {"message": "OpenSea를 사용하여 NFT를 확인하려면 $1에서 ‘NFT 미디어 표시’ 기능을 켜세요.", "description": "$1 is used for newNFTDetectedInImportNFTsMessageStrongText"}, "newNFTDetectedInNFTsTabMessage": {"message": "NeoNix를 통해 자동으로 NFT를 감지하여 지갑에 표시할 수 있습니다."}, "newNFTsAutodetected": {"message": "NFT 자동 감지"}, "newNetworkAdded": {"message": "“$1”(을)를 성공적으로 추가했습니다!"}, "newNetworkEdited": {"message": "“$1”(을)를 성공적으로 편집했습니다!"}, "newNftAddedMessage": {"message": "NFT를 성공적으로 추가했습니다!"}, "newPassword": {"message": "새 비밀번호(8자 이상)"}, "newPrivacyPolicyActionButton": {"message": "자세히 보기"}, "newPrivacyPolicyTitle": {"message": "개인정보 처리방침이 개정되었습니다"}, "newRpcUrl": {"message": "새 RPC URL"}, "newTokensImportedMessage": {"message": "$1 토큰을 성공적으로 불러왔습니다.", "description": "$1 is the string of symbols of all the tokens imported"}, "newTokensImportedTitle": {"message": "불러온 토큰"}, "next": {"message": "다음"}, "nftAddFailedMessage": {"message": "소유권 정보가 일치하지 않아 NFT를 추가할 수 없습니다. 올바른 정보를 입력했는지 확인하세요."}, "nftAddressError": {"message": "이 토큰은 NFT입니다. $1에 추가하세요", "description": "$1 is a clickable link with text defined by the 'importNFTPage' key"}, "nftAlreadyAdded": {"message": "NFT가 이미 추가되었습니다."}, "nftAutoDetectionEnabled": {"message": "NFT 자동 감지 활성화 완료"}, "nftDisclaimer": {"message": "면책 조항: NeoNix는 소스 URL에서 미디어 파일을 가져옵니다. 이러한 URL은 때때로 NFT가 민팅된 마켓플레이스에 의해 변경되기도 합니다."}, "nftOptions": {"message": "NFT 옵션"}, "nftTokenIdPlaceholder": {"message": "토큰 ID 입력"}, "nftWarningContent": {"message": "향후 소유할 수 있는 모든 $1에 대한 액세스를 허용하는 것입니다. 이 허용을 취소하지 않는 한, 상대방이 언제든지 허락 없이 지갑에서 NFT를 전송할 수 있습니다. $2", "description": "$1 is nftWarningContentBold bold part, $2 is Learn more link"}, "nftWarningContentBold": {"message": "내 모든 $1 NFT", "description": "$1 is name of the collection"}, "nftWarningContentGrey": {"message": "주의하여 진행하세요."}, "nfts": {"message": "NFT"}, "nftsPreviouslyOwned": {"message": "이전 소유"}, "nickname": {"message": "닉네임"}, "noAccountsFound": {"message": "검색어에 해당하는 계정이 없습니다."}, "noActivity": {"message": "아직 활동 내역이 없습니다"}, "noConnectedAccountTitle": {"message": "NeoNix가 이 사이트와 연결되어 있지 않습니다"}, "noConnectionDescription": {"message": "사이트에 연결하려면 \"연결\" 버튼을 찾아 클릭하세요. NeoNix는 웹3의 사이트에만 연결할 수 있습니다."}, "noConversionRateAvailable": {"message": "사용 가능한 환율 없음"}, "noDeFiPositions": {"message": "아직 포지션이 없습니다"}, "noDomainResolution": {"message": "도메인에 대한 해결 방법이 제공되지 않았습니다."}, "noHardwareWalletOrSnapsSupport": {"message": "Snap 및 대부분의 하드웨어 지갑은 현재 사용 중인 브라우저 버전에서 작동하지 않습니다."}, "noNFTs": {"message": "아직 NFT가 없음"}, "noNetworksFound": {"message": "검색어에 해당하는 네트워크가 없습니다."}, "noOptionsAvailableMessage": {"message": "현재 이 거래 경로를 사용할 수 없습니다. 금액, 네트워크, 토큰을 변경하면 최적의 옵션을 찾아드립니다."}, "noSnaps": {"message": "설치된 스냅이 없습니다"}, "noThanks": {"message": "괜찮습니다"}, "noTransactions": {"message": "트랜잭션이 없습니다"}, "noWebcamFound": {"message": "컴퓨터의 웹캠을 찾을 수 없습니다. 다시 시도하세요."}, "noWebcamFoundTitle": {"message": "웹캠을 찾을 수 없음"}, "nonContractAddressAlertDesc": {"message": "계약이 아닌 주소로 호출 데이터를 보내고 있습니다. 이를 통해 자금을 잃을 수 있습니다. 계속하기 전에 올바른 주소와 네트워크를 사용하고 있는지 확인하세요."}, "nonContractAddressAlertTitle": {"message": "잠재적 실수"}, "nonce": {"message": "논스"}, "none": {"message": "없음"}, "notBusy": {"message": "바쁘지 않음"}, "notCurrentAccount": {"message": "올바른 계정인가요? 현재 지갑에서 선택된 계정과 다릅니다."}, "notEnoughBalance": {"message": "잔액 부족"}, "notEnoughGas": {"message": "가스 부족"}, "notNow": {"message": "나중에"}, "notificationDetail": {"message": "세부 정보"}, "notificationDetailBaseFee": {"message": "기본 수수료(GWEI)"}, "notificationDetailGasLimit": {"message": "가스 한도(단위)"}, "notificationDetailGasUsed": {"message": "사용한 가스(단위)"}, "notificationDetailMaxFee": {"message": "가스당 최대 수수료"}, "notificationDetailNetwork": {"message": "네트워크"}, "notificationDetailNetworkFee": {"message": "네트워크 수수료"}, "notificationDetailPriorityFee": {"message": "우선 수수료(GWEI)"}, "notificationItemCheckBlockExplorer": {"message": "BlockExplorer에서 확인하기"}, "notificationItemCollection": {"message": "컬렉션"}, "notificationItemConfirmed": {"message": "컨펌"}, "notificationItemError": {"message": "현재 수수료를 조회할 수 없습니다"}, "notificationItemFrom": {"message": "발신:"}, "notificationItemLidoStakeReadyToBeWithdrawn": {"message": "출금 준비 완료"}, "notificationItemLidoStakeReadyToBeWithdrawnMessage": {"message": "이제 언스테이킹한 $1을(를) 출금할 수 있습니다"}, "notificationItemLidoWithdrawalRequestedMessage": {"message": "$1 언스테이킹 요청이 전송되었습니다"}, "notificationItemNFTReceivedFrom": {"message": "수취한 NFT 출처:"}, "notificationItemNFTSentTo": {"message": "전송한 NFT 수취 대상:"}, "notificationItemNetwork": {"message": "네트워크"}, "notificationItemRate": {"message": "환율(수수료 포함)"}, "notificationItemReceived": {"message": "받음"}, "notificationItemReceivedFrom": {"message": "발신처:"}, "notificationItemSent": {"message": "전송 완료"}, "notificationItemSentTo": {"message": "수신처:"}, "notificationItemStakeCompleted": {"message": "스테이킹 완료"}, "notificationItemStaked": {"message": "스테이킹됨"}, "notificationItemStakingProvider": {"message": "스테이킹 공급자"}, "notificationItemStatus": {"message": "상태"}, "notificationItemSwapped": {"message": "스왑"}, "notificationItemSwappedFor": {"message": "대상:"}, "notificationItemTo": {"message": "수신:"}, "notificationItemTransactionId": {"message": "트랜잭션 ID"}, "notificationItemUnStakeCompleted": {"message": "언스테이킹 완료"}, "notificationItemUnStaked": {"message": "언스테이킹됨"}, "notificationItemUnStakingRequested": {"message": "언스테이킹 요청 완료"}, "notificationTransactionFailedMessage": {"message": "$1 트랜잭션에 실패했습니다! $2", "description": "Content of the browser notification that appears when a transaction fails"}, "notificationTransactionFailedTitle": {"message": "실패한 트랜잭션", "description": "Title of the browser notification that appears when a transaction fails"}, "notificationTransactionSuccessMessage": {"message": "$1 트랜잭션이 컨펌되었습니다!", "description": "Content of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessTitle": {"message": "컨펌된 트랜잭션", "description": "Title of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessView": {"message": "$1에서 보기", "description": "Additional content in a notification that appears when a transaction is confirmed and has a block explorer URL."}, "notifications": {"message": "알림"}, "notificationsFeatureToggle": {"message": "지갑 알림 활성화", "description": "Experimental feature title"}, "notificationsFeatureToggleDescription": {"message": "자금 또는 NFT 보내기/받기와 기능 알림 등의 지갑 알림을 활성화합니다.", "description": "Description of the experimental notifications feature"}, "notificationsMarkAllAsRead": {"message": "모두 읽음으로 표시"}, "notificationsPageEmptyTitle": {"message": "표시할 항목이 없습니다"}, "notificationsPageErrorContent": {"message": "이 페이지를 다시 방문해 주세요."}, "notificationsPageErrorTitle": {"message": "오류가 발생했습니다"}, "notificationsPageNoNotificationsContent": {"message": "아직 알림을 받지 못했습니다."}, "notificationsSettingsBoxError": {"message": "문제가 발생했습니다. 다시 시도해 주세요."}, "notificationsSettingsPageAllowNotifications": {"message": "알림을 통해 지갑에서 무슨 일이 일어나고 있는지 계속 확인하세요. 알림 기능을 사용하려면 프로필을 사용해 기기 간에 일부 설정을 동기화해야 합니다. $1"}, "notificationsSettingsPageAllowNotificationsLink": {"message": "이 기능을 사용하는 동안 개인정보가 어떻게 보호되는지 알아보세요."}, "numberOfNewTokensDetectedPlural": {"message": "계정에서 $1개의 새로운 토큰이 발견됨", "description": "$1 is the number of new tokens detected"}, "numberOfNewTokensDetectedSingular": {"message": "계정에서 1개의 새 토큰을 찾았습니다"}, "numberOfTokens": {"message": "토큰 수"}, "ofTextNofM": {"message": "/"}, "off": {"message": "끄기"}, "offlineForMaintenance": {"message": "점검을 위한 오프라인 상태"}, "ok": {"message": "확인"}, "on": {"message": "켜기"}, "onboardedMetametricsAccept": {"message": "동의합니다"}, "onboardedMetametricsDisagree": {"message": "아니요, 괜찮습니다"}, "onboardedMetametricsKey1": {"message": "최신 개발"}, "onboardedMetametricsKey2": {"message": "제품 특징"}, "onboardedMetametricsKey3": {"message": "기타 관련 프로모션 자료"}, "onboardedMetametricsLink": {"message": "MetaMetrics"}, "onboardedMetametricsParagraph1": {"message": "$1 외에도 데이터를 활용하여 사용자가 마케팅 커뮤니케이션과 상호 작용하는 방식을 이해하고자 합니다.", "description": "$1 represents the 'onboardedMetametricsLink' locale string"}, "onboardedMetametricsParagraph2": {"message": "이를 통해 사용자와 공유하는 다음과 같은 콘텐츠를 최적화할 수 있습니다."}, "onboardedMetametricsParagraph3": {"message": "사용자가 제공한 데이터는 절대 판매하지 않습니다. 데이터 수집은 언제든지 거부할 수 있습니다."}, "onboardedMetametricsTitle": {"message": "사용자 경험을 개선할 수 있도록 도와주세요"}, "onboardingAdvancedPrivacyIPFSDescription": {"message": "IPFS 게이트웨이를 사용하면 타사 호스팅 데이터에 액세스하여 이를 볼 수 있습니다. 사용자 지정 IPFS 게이트웨이를 추가하셔도 좋고 기본 설정을 계속 사용하셔도 됩니다."}, "onboardingAdvancedPrivacyIPFSInvalid": {"message": "유효한 URL을 입력하세요"}, "onboardingAdvancedPrivacyIPFSTitle": {"message": "사용자 지정 IPFS 게이트웨이 추가"}, "onboardingAdvancedPrivacyIPFSValid": {"message": "PFS 게이트웨이 URL이 유효합니다"}, "onboardingAdvancedPrivacyNetworkDescription": {"message": "기본 설정과 구성을 사용할 경우, NeoNix는 Infura를 기본 원격 프로시저 호출(RPC) 제공업체로 사용하여 가능한 가장 안정적이고 공개성이 낮은 방식으로 이더리움 데이터에 접근할 수 있도록 합니다. 일부 제한된 경우에는 사용자에게 최상의 사용 경험을 제공하기 위해 다른 RPC 제공업체를 사용할 수 있습니다. 직접 RPC를 선택할 수도 있지만, 어떤 RPC를 사용하든 트랜잭션을 수행하려면 본인의 IP 주소와 이더리움 지갑 주소가 해당 RPC로 전송된다는 점을 유의하세요. Infura가 EVM 계정 데이터를 처리하는 방식에 대해 더 알고 싶다면 $1의 내용을, Solana 계정에 대한 정보는 $2의 내용을 참고하세요."}, "onboardingAdvancedPrivacyNetworkDescriptionCallToAction": {"message": "여기를 클릭하세요"}, "onboardingAdvancedPrivacyNetworkTitle": {"message": "네트워크 선택"}, "onboardingCreateWallet": {"message": "새 지갑 생성"}, "onboardingImportWallet": {"message": "기존 지갑 가져오기"}, "onboardingMetametricsAgree": {"message": "동의함"}, "onboardingMetametricsDescription": {"message": "NeoNix를 개선하기 위해 기본적인 사용 및 진단 데이터를 수집하고자 합니다. NeoNix는 제공받은 데이터를 절대 판매하지 않습니다."}, "onboardingMetametricsInfuraTerms": {"message": "이 데이터를 다른 목적으로 사용하기로 결정하면 알려드리겠습니다. 자세한 내용은 $1을(를) 참고하세요. 언제든지 설정으로 이동하여 해제할 수 있습니다.", "description": "$1 represents `onboardingMetametricsInfuraTermsPolicy`"}, "onboardingMetametricsInfuraTermsPolicy": {"message": "개인정보 처리방침"}, "onboardingMetametricsNeverCollect": {"message": "$1 앱의 클릭 수와 조회수는 저장되지만, 공개 주소와 같은 기타 세부 정보는 저장되지 않습니다.", "description": "$1 represents `onboardingMetametricsNeverCollectEmphasis`"}, "onboardingMetametricsNeverCollectEmphasis": {"message": "개인 정보:"}, "onboardingMetametricsNeverCollectIP": {"message": "$1 NeoNix는 사용자의 국가나 지역과 같은 일반적인 위치를 찾기 위해 일시적으로 사용자의 IP 주소를 사용하지만, 이 정보는 저장되지 않습니다.", "description": "$1 represents `onboardingMetametricsNeverCollectIPEmphasis`"}, "onboardingMetametricsNeverCollectIPEmphasis": {"message": "일반:"}, "onboardingMetametricsNeverSellData": {"message": "$1 언제든지 설정을 통해 사용 데이터를 공유할지 삭제할지 결정할 수 있습니다.", "description": "$1 represents `onboardingMetametricsNeverSellDataEmphasis`"}, "onboardingMetametricsNeverSellDataEmphasis": {"message": "선택 사항:"}, "onboardingMetametricsTitle": {"message": "NeoNix 개선에 도움을 주세요"}, "onboardingMetametricsUseDataCheckbox": {"message": "해당 데이터를 사용하여 사용자가 마케팅 커뮤니케이션과 어떻게 상호 작용하는지 파악할 것입니다. 관련 뉴스(예: 제품 기능 및 기타 자료)를 공유할 수 있습니다."}, "onboardingPinExtensionDescription": {"message": "쉽게 액세스하여 트랜잭션을 컨펌할 있도록 브라우저에 NeoNix를 고정해 놓으세요."}, "onboardingPinExtensionDescription2": {"message": "확장을 클릭하여 NeoNix를 열면 클릭 한 번으로 지갑에 액세스할 수 있습니다."}, "onboardingPinExtensionDescription3": {"message": "브라우저 확장 아이콘을 클릭하여 즉시 액세스하세요.", "description": "$1 is the browser name"}, "onboardingPinExtensionTitle": {"message": "NeoNix 설치가 완료되었습니다!"}, "onekey": {"message": "OneKey"}, "only": {"message": "전용"}, "onlyConnectTrust": {"message": "신뢰하는 사이트만 연결하세요. $1", "description": "Text displayed above the buttons for connection confirmation. $1 is the link to the learn more web page."}, "openFullScreenForLedgerWebHid": {"message": "전체 화면으로 이동하여 Ledger를 연결하세요.", "description": "Shown to the user on the confirm screen when they are viewing NeoNix in a popup window but need to connect their ledger via webhid."}, "openInBlockExplorer": {"message": "블록 탐색기 열기"}, "optional": {"message": "옵션"}, "options": {"message": "옵션"}, "origin": {"message": "원본"}, "originChanged": {"message": "사이트 변경됨"}, "originChangedMessage": {"message": "현재 $1의 요청을 검토 중입니다.", "description": "$1 is the name of the origin"}, "osTheme": {"message": "시스템"}, "other": {"message": "기타"}, "otherSnaps": {"message": "기타 스냅", "description": "Used in the 'permission_rpc' message."}, "others": {"message": "기타"}, "outdatedBrowserNotification": {"message": "현재 사용 중인 브라우저가 최신 버전이 아닙니다. 브라우저를 업데이트하지 않으면 보안 패치와 NeoNix의 새 기능을 이용할 수 없습니다."}, "overrideContentSecurityPolicyHeader": {"message": "Content-Security-Policy 헤더 무시"}, "overrideContentSecurityPolicyHeaderDescription": {"message": "이 옵션은 디앱의 Content-Security-Policy 헤더로 인해 확장 프로그램이 제대로 로드되지 않는 Firefox의 알려진 문제를 해결하기 위한 것입니다. 특정 웹 페이지와의 호환성을 위해 필요한 경우가 아니라면 이 옵션을 비활성화하지 않는 것이 좋습니다."}, "padlock": {"message": "패드락"}, "participateInMetaMetrics": {"message": "MetaMetrics에 참여"}, "participateInMetaMetricsDescription": {"message": "MetaMetrics에 참여하여 NeoNix 개선에 도움을 주세요."}, "password": {"message": "비밀번호"}, "passwordNotLongEnough": {"message": "비밀번호가 짧습니다."}, "passwordStrength": {"message": "비밀번호 강도: $1", "description": "Return password strength to the user when user wants to create password."}, "passwordStrengthDescription": {"message": "장치가 도난되거나 해킹되는 경우에 대비해 강력한 암호를 설정하면 지갑의 보안을 향상시킬 수 있습니다."}, "passwordTermsWarning": {"message": "NeoNix가 이 비밀번호를 복구할 수 없음을 이해합니다. $1"}, "passwordsDontMatch": {"message": "비밀번호가 일치하지 않습니다."}, "pastePrivateKey": {"message": "여기에 비공개 키 문자열을 붙여넣으세요.", "description": "For importing an account from a private key"}, "pending": {"message": "보류 중"}, "pendingConfirmationAddNetworkAlertMessage": {"message": "네트워크를 업데이트하면 이 사이트에서 대기 중인 트랜잭션 $1건이 취소됩니다.", "description": "Number of transactions."}, "pendingConfirmationSwitchNetworkAlertMessage": {"message": "네트워크를 전환하면 이 사이트에서 대기 중인 트랜잭션 $1건이 취소됩니다.", "description": "Number of transactions."}, "pendingTransactionAlertMessage": {"message": "이전 트랜잭션이 완료될 때까지 이 트랜잭션은 진행되지 않습니다. $1", "description": "$1 represents the words 'how to cancel or speed up a transaction' in a hyperlink"}, "pendingTransactionAlertMessageHyperlink": {"message": "트랜잭션을 취소하거나 속도를 높이는 방법을 알아보세요.", "description": "The text for the hyperlink in the pending transaction alert message"}, "permissionDetails": {"message": "권한 세부 정보"}, "permissionFor": {"message": "승인:"}, "permissionFrom": {"message": "승인자:"}, "permissionRequested": {"message": "지금 요청됨"}, "permissionRequestedForAccounts": {"message": "$1 승인 지금 요청됨", "description": "Permission cell status for requested permission including accounts, rendered as AvatarGroup which is $1."}, "permissionRevoked": {"message": "이 업데이트에서 취소됨"}, "permissionRevokedForAccounts": {"message": "이 업데이트에서 $1 취소됨", "description": "Permission cell status for revoked permission including accounts, rendered as AvatarGroup which is $1."}, "permission_accessNamedSnap": {"message": "$1에 연결", "description": "The description for the `wallet_snap` permission. $1 is the human-readable name of the snap."}, "permission_accessNetwork": {"message": "인터넷에 액세스합니다.", "description": "The description of the `endowment:network-access` permission."}, "permission_accessNetworkDescription": {"message": "$1의 인터넷 액세스를 허용합니다. 제삼자 서버와 데이터를 주고받는 데 모두 사용할 수 있습니다.", "description": "An extended description of the `endowment:network-access` permission. $1 is the snap name."}, "permission_accessSnap": {"message": "$1 스냅에 연결", "description": "The description for the `wallet_snap` permission. $1 is the name of the snap."}, "permission_accessSnapDescription": {"message": "웹사이트나 스냅이 $1 스냅과 인터렉션하는 것을 허용합니다.", "description": "The description for the `wallet_snap_*` permission. $1 is the name of the Snap."}, "permission_assets": {"message": "NeoNix 계정 자산을 표시합니다.", "description": "The description for the `endowment:assets` permission."}, "permission_assetsDescription": {"message": "NeoNix 클라이언트에 자산 정보를 제공하도록 $1을(를) 허용하세요. 자산은 온체인 또는 오프체인에 있을 수 있습니다.", "description": "An extended description for the `endowment:assets` permission. $1 is the name of the Snap."}, "permission_cronjob": {"message": "정기적 활동 예약 및 실행", "description": "The description for the `snap_cronjob` permission"}, "permission_cronjobDescription": {"message": "$1에게 고정된 시간이나 날짜, 간격으로 주기적으로 실행되는 작업을 수행하도록 허용합니다. 시간에 민감한 상호작용이나 알림을 트리거하는 데 사용할 수 있습니다.", "description": "An extended description for the `snap_cronjob` permission. $1 is the snap name."}, "permission_dialog": {"message": "NeoNix 대화창 표시", "description": "The description for the `snap_dialog` permission"}, "permission_dialogDescription": {"message": "$1 스냅이 NeoNix 팝업을 만들고 여기에 맞춤 텍스트와 입력란, 그리고 버튼을 표시하여 승인 또는 거부에 활용하도록 허용합니다.\n이는 경고, 컨펌, Snap에 필요한 옵트인 플로우 등을 만드는 데 사용할 수 있습니다.", "description": "An extended description for the `snap_dialog` permission. $1 is the snap name."}, "permission_ethereumAccounts": {"message": "주소, 계정 잔액, 활동 및 승인할 트랜잭션 추천 보기", "description": "The description for the `eth_accounts` permission"}, "permission_ethereumProvider": {"message": "이더리움 공급자에 액세스합니다.", "description": "The description for the `endowment:ethereum-provider` permission"}, "permission_ethereumProviderDescription": {"message": "$1 스냅이 NeoNix와 직접 소통하여 블록체인에서 데이터를 읽고 메시지 및 트랜잭션을 제안하도록 허용합니다.", "description": "An extended description for the `endowment:ethereum-provider` permission. $1 is the snap name."}, "permission_getEntropy": {"message": "$1에 대해 고유한 키를 임의로 파생합니다.", "description": "The description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getEntropyDescription": {"message": "$1 스냅이 노출을 피하여 $1에 고유한 임의의 키를 파생하도록 허용합니다. 이 키는 NeoNix 계정과 별개이며 개인 키 또는 비밀복구구문과 관련이 없습니다. 다른 Snap은 이 정보에 액세스할 수 없습니다.", "description": "An extended description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getLocale": {"message": "원하는 언어로 변경하세요.", "description": "The description for the `snap_getLocale` permission"}, "permission_getLocaleDescription": {"message": "NeoNix 설정에서 원하는 언어로 $1 스냅을 사용하세요. 이 기능을 사용하면 선택한 언어로 $1의 콘텐츠를 현지화하고 표시할 수 있습니다.", "description": "An extended description for the `snap_getLocale` permission. $1 is the snap name."}, "permission_getPreferences": {"message": "선호하는 언어 및 명목화폐와 같은 정보를 확인하세요.", "description": "The description for the `snap_getPreferences` permission"}, "permission_getPreferencesDescription": {"message": "NeoNix 설정에서 $1이(가) 회원님이 선호하는 언어 및 명목화폐와 같은 정보에 접근할 수 있도록 허용하세요. 이렇게 하면 $1이(가) 회원님의 선호도에 맞는 콘텐츠를 표시하는 데 도움이 됩니다. ", "description": "An extended description for the `snap_getPreferences` permission. $1 is the snap name."}, "permission_homePage": {"message": "사용자 지정 화면 표시", "description": "The description for the `endowment:page-home` permission"}, "permission_homePageDescription": {"message": "$1에서 NeoNix의 사용자 지정 홈 화면 구성에 참여하도록 허용합니다. 이는 사용자 인터페이스, 구성, 대시보드에 사용할 수 있습니다.", "description": "An extended description for the `endowment:page-home` permission. $1 is the snap name."}, "permission_keyring": {"message": "이더리움 계정 추가 및 제어 요청 허용", "description": "The description for the `endowment:keyring` permission"}, "permission_keyringDescription": {"message": "$1 스냅이 계정 추가 또는 제거 요청을 받고 해당 계정을 대신하여 서명하고 트랜잭션할 수 있도록 합니다.", "description": "An extended description for the `endowment:keyring` permission. $1 is the snap name."}, "permission_lifecycleHooks": {"message": "라이프사이클 훅 사용", "description": "The description for the `endowment:lifecycle-hooks` permission"}, "permission_lifecycleHooksDescription": {"message": "$1 스냅이 라이프사이클 훅을 사용하도록 허용하여 라이프사이클 동안 코드를 특정 횟수만큼 실행합니다.", "description": "An extended description for the `endowment:lifecycle-hooks` permission. $1 is the snap name."}, "permission_manageAccounts": {"message": "이더리움 계정 추가 및 제어", "description": "The description for `snap_manageAccounts` permission"}, "permission_manageAccountsDescription": {"message": "$1 스냅이 이더리움 계정을 추가 또는 제거하도록 허용한 후, 해당 계정을 이용하여 트랜잭션하고 서명합니다.", "description": "An extended description for the `snap_manageAccounts` permission. $1 is the snap name."}, "permission_manageBip32Keys": {"message": "$1 계정 관리", "description": "The description for the `snap_getBip32Entropy` permission. $1 is a derivation path, e.g. 'm/44'/0'/0' (secp256k1)'."}, "permission_manageBip44AndBip32KeysDescription": {"message": "$1 스냅이 요청된 네트워크에서 계정과 자산을 관리하도록 허용합니다. 해당 계정은 비밀복구구문(공개하지 않음)을 사용하여 파생되고 백업됩니다. 키를 파생할 수 있는 기능을 통해 $1 스냅은 이더리움(EVM)뿐 아니라 다양한 블록체인 프로토콜을 지원할 수 있습니다.", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions. $1 is the snap name."}, "permission_manageBip44Keys": {"message": "$1 계정 관리", "description": "The description for the `snap_getBip44Entropy` permission. $1 is the name of a protocol, e.g. 'Filecoin'."}, "permission_manageState": {"message": "장치의 데이터를 저장하고 관리합니다.", "description": "The description for the `snap_manageState` permission"}, "permission_manageStateDescription": {"message": "$1 스냅이 암호화를 통해 데이터를 안전하게 저장, 업데이트, 검색하도록 허용합니다. 다른 Snap은 이 정보에 액세스할 수 없습니다.", "description": "An extended description for the `snap_manageState` permission. $1 is the snap name."}, "permission_nameLookup": {"message": "도메인 및 주소 조회를 제공합니다.", "description": "The description for the `endowment:name-lookup` permission."}, "permission_nameLookupDescription": {"message": "Snap이 주소 및 도메인 조회를 가져와 NeoNix UI의 다른 부분에 표시하도록 허용합니다.", "description": "An extended description for the `endowment:name-lookup` permission."}, "permission_notifications": {"message": "알림을 표시합니다.", "description": "The description for the `snap_notify` permission"}, "permission_notificationsDescription": {"message": "$1 스냅이 NeoNix에서 알림을 표시하도록 허용합니다. Snap이 작업이 필요하거나 기한이 있는 정보를 짧은 알림으로 트리거할 수 있습니다.", "description": "An extended description for the `snap_notify` permission. $1 is the snap name."}, "permission_protocol": {"message": "하나 이상의 체인에 대한 프로토콜 데이터를 제공하세요.", "description": "The description for the `endowment:protocol` permission."}, "permission_protocolDescription": {"message": "NeoNix에 예상 가스비나 토큰 정보와 같은 프로토콜 데이터를 제공하려면 $1을(를) 허용하세요.", "description": "An extended description for the `endowment:protocol` permission. $1 is the name of the Snap."}, "permission_rpc": {"message": "$1 측이 $2 스냅과 직접 소통하도록 허용합니다.", "description": "The description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescription": {"message": "$$1 측이 $2 스냅으로 메시지를 전송하고 $2에서 응답을 받도록 허용합니다.", "description": "An extended description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescriptionOriginList": {"message": "$1 및 $2", "description": "A list of allowed origins where $2 is the last origin of the list and $1 is the rest of the list separated by ','."}, "permission_signatureInsight": {"message": "서명 인사이트 모달을 표시합니다.", "description": "The description for the `endowment:signature-insight` permission"}, "permission_signatureInsightDescription": {"message": "$1 스냅이 승인 전에 서명 요청에 대한 인사이트가 포함된 모달을 표시하도록 허용합니다. 피싱 방지 및 보안 솔루션에 사용할 수 있습니다.", "description": "An extended description for the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_signatureInsightOrigin": {"message": "서명 요청 웹사이트의 출처 보기", "description": "The description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission"}, "permission_signatureInsightOriginDescription": {"message": "$1 스냅이 서명 요청을 시작하는 웹사이트의 출처(URI) 를 볼 수 있도록 허용합니다. 피싱 방지와 보안 솔루션에 사용할 수 있습니다.", "description": "An extended description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_transactionInsight": {"message": "트랜잭션 인사이트를 가져와 표시합니다.", "description": "The description for the `endowment:transaction-insight` permission"}, "permission_transactionInsightDescription": {"message": "$1 스냅이 트랜잭션을 디코딩하고 NeoNix UI에서 인사이트를 표시하도록 허용합니다. 피싱 방지와 보안 솔루션에 사용할 수 있습니다.", "description": "An extended description for the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_transactionInsightOrigin": {"message": "트랜잭션을 추천하는 웹사이트 출처 보기", "description": "The description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission"}, "permission_transactionInsightOriginDescription": {"message": "$1 스냅이 트랜잭션을 제안하는 웹사이트의 출처(URI)를 볼 수 있도록 허용합니다. 피싱 방지와 보안 솔루션에 사용할 수 있습니다.", "description": "An extended description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_unknown": {"message": "알 수 없는 권한: $1", "description": "$1 is the name of a requested permission that is not recognized."}, "permission_viewBip32PublicKeys": {"message": "$1 공개 키 보기($2).", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a derivation path, e.g. 'm/44'/0'/0''. $2 is the elliptic curve name, e.g. 'secp256k1'."}, "permission_viewBip32PublicKeysDescription": {"message": "$2 스냅이 $1에 대한 공개 키(및 주소)를 볼 수 있도록 허용합니다. 이를 통해 계정이나 자산에 대한 통제권이 부여되지는 않습니다.", "description": "An extended description for the `snap_getBip32PublicKey` permission. $1 is a derivation path (name). $2 is the snap name."}, "permission_viewNamedBip32PublicKeys": {"message": "$1에 관한 공개 키 보기", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a name for the derivation path, e.g., 'Ethereum accounts'."}, "permission_walletSwitchEthereumChain": {"message": "활성화된 네트워크 사용", "description": "The label for the `wallet_switchEthereumChain` permission"}, "permission_webAssembly": {"message": "WebAssembly 지원", "description": "The description of the `endowment:webassembly` permission."}, "permission_webAssemblyDescription": {"message": "$1 스냅이 WebAssembly를 통해 하위 레벨 실행 환경에 액세스하도록 허용합니다.", "description": "An extended description of the `endowment:webassembly` permission. $1 is the snap name."}, "permissions": {"message": "권한"}, "permissionsPageEmptyContent": {"message": "표시할 항목이 없습니다"}, "permissionsPageEmptySubContent": {"message": "여기에서 설치된 Snap 또는 연결된 사이트의 권한을 확인할 수 있습니다."}, "permitSimulationChange_approve": {"message": "지출 한도"}, "permitSimulationChange_bidding": {"message": "내 입찰"}, "permitSimulationChange_listing": {"message": "내 리스트"}, "permitSimulationChange_nft_listing": {"message": "리스팅 가격"}, "permitSimulationChange_receive": {"message": "받음:"}, "permitSimulationChange_revoke2": {"message": "철회"}, "permitSimulationChange_transfer": {"message": "보냄:"}, "permitSimulationDetailInfo": {"message": "내 계정에서 이만큼의 토큰을 사용할 수 있도록 승인합니다."}, "permittedChainToastUpdate": {"message": "$1은(는) $2에 액세스 할 수 있습니다."}, "personalAddressDetected": {"message": "개인 주소가 발견되었습니다. 토큰 계약 주소를 입력하세요."}, "pinToTop": {"message": "맨 위에 고정"}, "pleaseConfirm": {"message": "컨펌하세요"}, "plusMore": {"message": "+ $1개 추가", "description": "$1 is the number of additional items"}, "plusXMore": {"message": "+ 그 외 $1개", "description": "$1 is a number of additional but unshown items in a list- this message will be shown in place of those items"}, "popularNetworkAddToolTip": {"message": "이러한 네트워크 중 일부는 제삼자에 의존합니다. 이러한 연결은 안정성이 떨어지거나 제삼자가 활동을 추적할 수 있습니다.", "description": "Learn more link"}, "popularNetworks": {"message": "인기 네트워크"}, "portfolio": {"message": "포트폴리오"}, "preparingSwap": {"message": "스왑 준비 중..."}, "prev": {"message": "이전"}, "price": {"message": "가격"}, "priceUnavailable": {"message": "가격 사용 불가"}, "primaryType": {"message": "기본 유형"}, "priorityFee": {"message": "우선 요금"}, "priorityFeeProperCase": {"message": "우선 요금"}, "privacy": {"message": "개인정보 보호"}, "privacyMsg": {"message": "개인정보처리방침"}, "privateKey": {"message": "비공개 키", "description": "select this type of file to use to import an account"}, "privateKeyCopyWarning": {"message": "$1 비공개 키", "description": "$1 represents the account name"}, "privateKeyHidden": {"message": "개인 키 입력이 표시되지 않습니다", "description": "Explains that the private key input is hidden"}, "privateKeyShow": {"message": "개인 키 입력 표시/숨기기", "description": "Describes a toggle that is used to show or hide the private key input"}, "privateKeyShown": {"message": "이 개인 키 입력은 표시됩니다", "description": "Explains that the private key input is being shown"}, "privateKeyWarning": {"message": "경고: 이 키를 노출하지 마세요. 비공개 키가 있는 사람이라면 누구든 회원님의 계정에 있는 자산을 훔칠 수 있습니다."}, "privateNetwork": {"message": "비공개 네트워크"}, "proceedWithTransaction": {"message": "계속 진행"}, "productAnnouncements": {"message": "제품 공지"}, "proposedApprovalLimit": {"message": "제안된 승인 한도"}, "provide": {"message": "제공"}, "publicAddress": {"message": "공개 주소"}, "pushPlatformNotificationsFundsReceivedDescription": {"message": "$1 $2을(를) 받았습니다"}, "pushPlatformNotificationsFundsReceivedDescriptionDefault": {"message": "토큰을 받았습니다"}, "pushPlatformNotificationsFundsReceivedTitle": {"message": "자금 수령 완료"}, "pushPlatformNotificationsFundsSentDescription": {"message": "$1 $2을(를) 보냈습니다"}, "pushPlatformNotificationsFundsSentDescriptionDefault": {"message": "토큰을 보냈습니다"}, "pushPlatformNotificationsFundsSentTitle": {"message": "자금 전송 완료"}, "pushPlatformNotificationsNftReceivedDescription": {"message": "새 NFT를 받았습니다"}, "pushPlatformNotificationsNftReceivedTitle": {"message": "NFT 수령 완료"}, "pushPlatformNotificationsNftSentDescription": {"message": "NFT를 보냈습니다"}, "pushPlatformNotificationsNftSentTitle": {"message": "NFT 전송 완료"}, "pushPlatformNotificationsStakingLidoStakeCompletedDescription": {"message": "Lido 스테이킹에 성공했습니다"}, "pushPlatformNotificationsStakingLidoStakeCompletedTitle": {"message": "스테이킹 완료"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnDescription": {"message": "Lido 스테이킹이 인출 준비가 되었습니다"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnTitle": {"message": "스테이킹 인출 준비 완료"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedDescription": {"message": "Lido 인출에 성공했습니다"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedTitle": {"message": "인출 완료"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedDescription": {"message": "Lido 인출 요청이 제출되었습니다"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedTitle": {"message": "인출 요청 완료"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedDescription": {"message": "RocketPool 스테이킹에 성공했습니다"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedTitle": {"message": "스테이킹 완료"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedDescription": {"message": "RocketPool 언스테이킹에 성공했습니다"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedTitle": {"message": "언스테이킹 완료"}, "pushPlatformNotificationsSwapCompletedDescription": {"message": "NeoNix 스왑에 성공했습니다"}, "pushPlatformNotificationsSwapCompletedTitle": {"message": "스왑 완료"}, "queued": {"message": "대기열에 지정됨"}, "quoteRate": {"message": "견적 비율"}, "quotedReceiveAmount": {"message": "$1 받은 금액"}, "quotedTotalCost": {"message": "$1 총비용"}, "rank": {"message": "순위"}, "rateIncludesMMFee": {"message": "요율에 $1% 수수료 포함"}, "reAddAccounts": {"message": "다른 계정을 다시 추가"}, "reAdded": {"message": "다시 추가 완료"}, "readdToken": {"message": "나중에 계정 옵션 메뉴의 '토큰 추가'로 이동하면 이 토큰을 다시 추가할 수 있습니다."}, "receive": {"message": "받기"}, "receiveCrypto": {"message": "암호화폐 받기"}, "recipientAddressPlaceholderNew": {"message": "공개 주소(0x) 또는 도메인 이름 입력"}, "recommendedGasLabel": {"message": "권장됨"}, "recoveryPhraseReminderBackupStart": {"message": "여기에서 시작"}, "recoveryPhraseReminderConfirm": {"message": "확인"}, "recoveryPhraseReminderHasBackedUp": {"message": "비밀복구구문은 언제나 보안이 유지되고 알려지지 않은 곳에 보관해야 합니다."}, "recoveryPhraseReminderHasNotBackedUp": {"message": "비밀복구구문을 다시 백업해야 합니까?"}, "recoveryPhraseReminderItemOne": {"message": "절대로 다른 사람과 비밀복구구문을 공유하지 마십시오"}, "recoveryPhraseReminderItemTwo": {"message": "NeoNix 팀에서는 절대로 비밀복구구문을 묻지 않습니다"}, "recoveryPhraseReminderSubText": {"message": "비밀복구구문은 모든 계정을 관리합니다."}, "recoveryPhraseReminderTitle": {"message": "자신의 자금을 지키세요"}, "redeposit": {"message": "재입금"}, "refreshList": {"message": "새로 고침 목록"}, "reject": {"message": "거부"}, "rejectAll": {"message": "모두 거부"}, "rejectRequestsDescription": {"message": "$1 요청 전부를 거절하려고 합니다."}, "rejectRequestsN": {"message": "$1 요청 거절"}, "rejectTxsDescription": {"message": "트랜잭션 $1개를 모두 거부합니다."}, "rejectTxsN": {"message": "트랜잭션 $1개 거부"}, "rejected": {"message": "거부됨"}, "remove": {"message": "제거"}, "removeAccount": {"message": "계정 제거"}, "removeAccountDescription": {"message": "이 계정이 지갑에서 제거됩니다. 계속하기 전에 가져온 이 계정에 대한 원본 비밀복구구문이나 비공개 키가 있는지 확인하세요. 계정 드롭다운에서 계정을 가져오거나 다시 만들 수 있습니다. "}, "removeKeyringSnap": {"message": "이 Snap을 제거하면 이 계정들이 NeoNix에서 제거됩니다:"}, "removeKeyringSnapToolTip": {"message": "Snap은 계정을 제어하며 Snap을 제거하면 계정도 NeoNix에서 제거되지만, 블록체인에는 남게 됩니다."}, "removeNFT": {"message": "NFT 제거"}, "removeNftErrorMessage": {"message": "이 NFT를 제거할 수 없습니다."}, "removeNftMessage": {"message": "NFT가 성공적으로 제거되었습니다!"}, "removeSnap": {"message": "스냅 제거"}, "removeSnapAccountDescription": {"message": "계속하면 이 계정을 NeoNix에서 사용할 수 없습니다."}, "removeSnapAccountTitle": {"message": "계정 제거"}, "removeSnapConfirmation": {"message": "$1(을)를 제거하시겠습니까?", "description": "$1 represents the name of the snap"}, "removeSnapDescription": {"message": "이렇게 하면 스냅과 데이터가 삭제되고 허용된 권한이 취소됩니다."}, "replace": {"message": "대체"}, "reportIssue": {"message": "문제 신고"}, "requestFrom": {"message": "요청자:"}, "requestFromInfo": {"message": "서명이 필요한 사이트입니다."}, "requestFromInfoSnap": {"message": "이는 서명을 요청하는 Snap입니다."}, "requestFromTransactionDescription": {"message": "컨펌이 필요한 사이트입니다."}, "requestingFor": {"message": "요청 중:"}, "requestingForAccount": {"message": "$1 요청 중", "description": "Name of Account"}, "requestingForNetwork": {"message": "$1 요청 중", "description": "Name of Network"}, "required": {"message": "필요"}, "reset": {"message": "재설정"}, "resetWallet": {"message": "지갑 초기화"}, "resetWalletSubHeader": {"message": "NeoNix는 사용자의 비밀번호를 보관하지 않습니다. 계정 잠금을 해제하는 데 문제가 있는 경우, 지갑 설정 시 사용한 비밀복구구문을 이용하여 지갑을 초기화해야 합니다."}, "resetWalletUsingSRP": {"message": "이 작업을 하면 정리해둔 계정 목록과 함께 현재 지갑과 비밀복구구문이 이 기기에서 삭제됩니다. 비밀복구구문으로 초기화하면 해당 비밀복구구문과 연관된 계정 목록이 표시됩니다. 새 목록에는 잔액이 남아있는 계정이 자동으로 나타납니다. 이전에 생성한 $1도 가능합니다. 가져온 사용자 정의 계정은 $2이어야 하며 계정에 추가한 사용자 정의 토큰도 $3이어야 합니다."}, "resetWalletWarning": {"message": "계속하기 전에 비밀복구구문이 정확한지 확인하세요. 이 작업은 취소할 수 없습니다."}, "restartNeoNix": {"message": "NeoNix 재시작"}, "restore": {"message": "복구"}, "restoreUserData": {"message": "사용자 데이터 복구"}, "resultPageError": {"message": "오류"}, "resultPageErrorDefaultMessage": {"message": "작업에 실패했습니다."}, "resultPageSuccess": {"message": "성공"}, "resultPageSuccessDefaultMessage": {"message": "작업을 완료했습니다."}, "retryTransaction": {"message": "트랜잭션 재시도"}, "reusedTokenNameWarning": {"message": "여기에 있는 토큰은 사용자가 주시 중인 다른 토큰의 기호를 재사용하기 때문에 혼동되거나 속기 쉽습니다."}, "revealSecretRecoveryPhrase": {"message": "비밀복구구문 공개"}, "revealSeedWords": {"message": "비밀복구구문 공개"}, "revealSeedWordsDescription1": {"message": "$1 활용으로 $2 기능을 사용할 수 있습니다", "description": "This is a sentence consisting of link using 'revealSeedWordsSRPName' as $1 and bolded text using 'revealSeedWordsDescription3' as $2."}, "revealSeedWordsDescription2": {"message": "NeoNix는 $1입니다. 이는 회원님이 SRP의 소유자라는 의미입니다.", "description": "$1 is text link with the message from 'revealSeedWordsNonCustodialWallet'"}, "revealSeedWordsDescription3": {"message": "지갑과 자금에 모두 액세스하세요.\n"}, "revealSeedWordsNonCustodialWallet": {"message": "비수탁형 지갑"}, "revealSeedWordsQR": {"message": "QR"}, "revealSeedWordsSRPName": {"message": "비밀복구구문 (SRP)"}, "revealSeedWordsText": {"message": "문자"}, "revealSeedWordsWarning": {"message": "다른 사람이 보고 있지 않은지 확인하세요. $1", "description": "$1 is bolded text using the message from 'revealSeedWordsWarning2'"}, "revealSeedWordsWarning2": {"message": "NeoNix 지원팀은 이러한 정보를 절대로 묻지 않습니다,", "description": "The bolded texted in the second part of 'revealSeedWordsWarning'"}, "revealSensitiveContent": {"message": "민감한 콘텐츠 공개"}, "review": {"message": "검토"}, "reviewAlert": {"message": "경고 검토"}, "reviewAlerts": {"message": "경고 검토하기"}, "reviewPendingTransactions": {"message": "대기 중인 트랜잭션 검토"}, "reviewPermissions": {"message": "권한 검토"}, "revokePermission": {"message": "권한 철회"}, "revokePermissionTitle": {"message": "$1 권한 제거", "description": "The token symbol that is being revoked"}, "revokeSimulationDetailsDesc": {"message": "계정에서 토큰을 사용할 수 있는 다른 사람의 권한을 제거합니다."}, "reward": {"message": "보상"}, "rpcNameOptional": {"message": "RPC 이름(선택)"}, "rpcUrl": {"message": "RPC URL"}, "safeTransferFrom": {"message": "다음에서 안전하게 송금:"}, "save": {"message": "저장"}, "scanInstructions": {"message": "QR 코드를 카메라 앞에 놓으세요"}, "scanQrCode": {"message": "QR 코드 스캔"}, "scrollDown": {"message": "화면을 아래로 내리세요"}, "search": {"message": "검색"}, "searchAccounts": {"message": "계정 검색"}, "searchNfts": {"message": "NFT 검색"}, "searchTokens": {"message": "토큰 검색"}, "searchTokensByNameOrAddress": {"message": "이름이나 주소로 토큰 검색"}, "secretRecoveryPhrase": {"message": "비밀복구구문"}, "secretRecoveryPhrasePlusNumber": {"message": "비밀복구구문 $1", "description": "The $1 is the order of the Secret Recovery Phrase"}, "secureWallet": {"message": "보안 지갑"}, "security": {"message": "보안"}, "securityAlert": {"message": "$1 및 $2의 보안 경고"}, "securityAlerts": {"message": "보안 경고"}, "securityAlertsDescription": {"message": "이 기능은 트랜잭션 및 서명 요청을 적극적으로 검토하여 비정상적이거나 악의적인 활동을 경고합니다. $1", "description": "Link to learn more about security alerts"}, "securityAndPrivacy": {"message": "보안 및 프라이버시"}, "securityDescription": {"message": "안전하지 않은 네트워크에 가입할 가능성을 줄이고 계정을 보호하세요"}, "securityMessageLinkForNetworks": {"message": "네트워크 사기 및 보안 위험"}, "securityProviderPoweredBy": {"message": "$1 제공", "description": "The security provider that is providing data"}, "seeAllPermissions": {"message": "모든 권한 보기", "description": "Used for revealing more content (e.g. permission list, etc.)"}, "seeDetails": {"message": "세부 정보 보기"}, "seedPhraseIntroTitle": {"message": "지갑 보호하기"}, "seedPhraseReq": {"message": "비밀복구구문은 12, 15, 18, 21 또는 24개의 단어로 구성됩니다"}, "select": {"message": "선택"}, "selectAccountToConnect": {"message": "연결할 계정 선택"}, "selectAccounts": {"message": "계정 선택"}, "selectAccountsForSnap": {"message": "이 스냅을 사용할 계정을 선택하세요"}, "selectAll": {"message": "모두 선택"}, "selectAnAccount": {"message": "계정 선택"}, "selectAnAccountAlreadyConnected": {"message": "이 계정은 이미 NeoNix와 연결되어 있습니다."}, "selectEnableDisplayMediaPrivacyPreference": {"message": "NFT 미디어 표시 켜기"}, "selectHdPath": {"message": "HD 경로 선택"}, "selectNFTPrivacyPreference": {"message": "NFT 자동 감기 지능 켜기"}, "selectPathHelp": {"message": "원하는 계정이 표시되지 않는다면 HD 경로 또는 현재 선택한 네트워크를 전환해 보세요."}, "selectRpcUrl": {"message": "RPC URL 선택"}, "selectSecretRecoveryPhrase": {"message": "비밀복구구문 선택"}, "selectType": {"message": "유형 선택"}, "selectedAccountMismatch": {"message": "다른 계정 선택됨"}, "selectingAllWillAllow": {"message": "모두 선택하면 이 사이트에서 회원님의 현재 계정을 모두 볼 수 있습니다. 이 사이트를 신뢰하는지 확인하세요."}, "send": {"message": "보내기"}, "sendBugReport": {"message": "버그 리포트 전송"}, "sendNoContactsConversionText": {"message": "여기를 클릭"}, "sendNoContactsDescription": {"message": "연락처를 사용하면 다른 계정으로 여러 번 트랜잭션을 안전하게 전송할 수 있습니다. 연락처를 만들려면, $1", "description": "$1 represents the action text 'click here'"}, "sendNoContactsTitle": {"message": "아직 연락처가 없습니다"}, "sendSelectReceiveAsset": {"message": "수취할 자산 선택"}, "sendSelectSendAsset": {"message": "전송할 자산 선택"}, "sendSpecifiedTokens": {"message": "$1 보내기", "description": "Symbol of the specified token"}, "sendSwapSubmissionWarning": {"message": "이 버튼을 클릭하면 즉시 스왑 트랜젝션이 시작됩니다. 계속하기 전에 트랜젝션 세부 정보를 검토하세요."}, "sendTokenAsToken": {"message": "$2(으)로 $1 전송하기", "description": "Used in the transaction display list to describe a swap and send. $1 and $2 are the symbols of tokens in involved in the swap."}, "sendingAsset": {"message": "$1 전송하기"}, "sendingDisabled": {"message": "ERC-1155 NFT 자산은 아직 지원되지 않습니다."}, "sendingNativeAsset": {"message": "$1 보내기", "description": "$1 represents the native currency symbol for the current network (e.g. ETH or BNB)"}, "sendingToTokenContractWarning": {"message": "경고: 자금 손실이 발생할 수 있는 토큰 계약으로 전송하게 됩니다. $1", "description": "$1 is a clickable link with text defined by the 'learnMoreUpperCase' key. The link will open to a support article regarding the known contract address warning"}, "sepolia": {"message": "Sepolia 테스트 네트워크"}, "setApprovalForAll": {"message": "모두 승인 설정"}, "setApprovalForAllRedesignedTitle": {"message": "인출 요청"}, "setApprovalForAllTitle": {"message": "$1 무제한 지출 승인", "description": "The token symbol that is being approved"}, "settingAddSnapAccount": {"message": "스냅 계정 추가"}, "settings": {"message": "설정"}, "settingsSearchMatchingNotFound": {"message": "검색 결과가 없습니다."}, "settingsSubHeadingSignaturesAndTransactions": {"message": "서명 및 트랜잭션 요청"}, "show": {"message": "보기"}, "showAccount": {"message": "계정 표시"}, "showAdvancedDetails": {"message": "고급 세부 정보 표시"}, "showExtensionInFullSizeView": {"message": "확장 브라우저를 전체 화면으로 보기"}, "showExtensionInFullSizeViewDescription": {"message": "이 옵션을 이용하면 확장 아이콘을 클릭할 때 기본으로 전체 화면이 나타납니다."}, "showFiatConversionInTestnets": {"message": "테스트넷에 전환 표시"}, "showFiatConversionInTestnetsDescription": {"message": "이 항목을 선택하면 테스트넷에 명목 전환이 표시됩니다."}, "showHexData": {"message": "16진수 데이터 표시"}, "showHexDataDescription": {"message": "이 항목을 선택하면 보내기 화면에 16진수 데이터 필드가 표시됩니다."}, "showLess": {"message": "간략히 보기"}, "showMore": {"message": "더 보기"}, "showNativeTokenAsMainBalance": {"message": "네이티브 토큰을 기본 잔액으로 표시"}, "showNft": {"message": "NFT 표시"}, "showPermissions": {"message": "권한 표시"}, "showPrivateKey": {"message": "개인 키 표시"}, "showSRP": {"message": "비밀복구구문 표시"}, "showTestnetNetworks": {"message": "테스트 네트워크 보기"}, "showTestnetNetworksDescription": {"message": "네트워크 목록에서 표시하려는 테스트 네트워크를 선택하세요."}, "sign": {"message": "서명"}, "signatureRequest": {"message": "서명 요청"}, "signature_decoding_bid_nft_tooltip": {"message": "입찰이 수락되면 NFT가 지갑에 반영됩니다."}, "signature_decoding_list_nft_tooltip": {"message": "NFT가 판매되는 경우에만 변경됩니다."}, "signed": {"message": "서명완료"}, "signing": {"message": "서명"}, "signingInWith": {"message": "다음으로 로그인:"}, "signingWith": {"message": "다음으로 로그인:"}, "simulationApproveHeading": {"message": "인출"}, "simulationDetailsApproveDesc": {"message": "다른 사람에게 내 계정에서 NFT를 인출할 수 있는 권한을 부여합니다."}, "simulationDetailsERC20ApproveDesc": {"message": "다른 사람에게 계정에서 이 금액을 사용할 수 있는 권한을 부여합니다."}, "simulationDetailsFiatNotAvailable": {"message": "이용할 수 없음"}, "simulationDetailsIncomingHeading": {"message": "받음:"}, "simulationDetailsNoChanges": {"message": "변경 사항 없음"}, "simulationDetailsOutgoingHeading": {"message": "보냄:"}, "simulationDetailsRevokeSetApprovalForAllDesc": {"message": "계정에서 NFT를 인출할 수 있는 다른 사람의 권한을 제거합니다."}, "simulationDetailsSetApprovalForAllDesc": {"message": "다른 사람에게 계정에서 NFT를 인출할 수 있도록 권한을 부여합니다."}, "simulationDetailsTitle": {"message": "예상 변동 사항"}, "simulationDetailsTitleTooltip": {"message": "예상 변동 사항은 이 트랜잭션을 진행할 경우 발생하는 결과를 예측한 것입니다. 이는 예측일 뿐 결과를 보장하지는 않습니다."}, "simulationDetailsTotalFiat": {"message": "합계 = $1", "description": "$1 is the total amount in fiat currency on one side of the transaction"}, "simulationDetailsTransactionReverted": {"message": "이 트랜잭션은 실패할 가능성이 높습니다"}, "simulationDetailsUnavailable": {"message": "사용할 수 없음"}, "simulationErrorMessageV2": {"message": "가스를 추정할 수 없었습니다. 계약에 오류가 있을 수 있으며 이 트랜잭션이 실패할 수 있습니다."}, "simulationsSettingDescription": {"message": "컨펌 전에 트랜잭션 및 서명으로 인한 잔액 변동을 추정하려면 이 기능을 켜세요. 이는 최종 결과를 보장하지 않습니다. $1"}, "simulationsSettingSubHeader": {"message": "예상 잔액 변동"}, "singleNetwork": {"message": "네트워크 1개"}, "siweIssued": {"message": "발행됨"}, "siweNetwork": {"message": "네트워크"}, "siweRequestId": {"message": "요청 ID"}, "siweResources": {"message": "리소스"}, "siweURI": {"message": "URL"}, "skipAccountSecurity": {"message": "계정 보안을 건너뛸까요?"}, "skipAccountSecurityDetails": {"message": "본인은 본인의 비밀복구구문을 백업하지 않는 한 본인의 계정과 모든 자산을 잃을 수 있다는 사실을 이해합니다."}, "slideBridgeDescription": {"message": "지갑 내에서 9개 체인 간 이동"}, "slideBridgeTitle": {"message": "브릿지할 준비가 되셨나요?"}, "slideCashOutDescription": {"message": "암호화폐를 현금으로 매도"}, "slideCashOutTitle": {"message": "NeoNix로 현금화"}, "slideDebitCardDescription": {"message": "일부 지역에서 사용 가능"}, "slideDebitCardTitle": {"message": "NeoNix 직불카드"}, "slideFundWalletDescription": {"message": "시작하려면 토큰을 추가하거나 전송하세요"}, "slideFundWalletTitle": {"message": "지갑에 자금 추가"}, "slideMultiSrpDescription": {"message": "NeoNix에서 여러 지갑을 가져와 사용하세요"}, "slideMultiSrpTitle": {"message": "여러 비밀복구구문 추가"}, "slideRemoteModeDescription": {"message": "콜드 월렛을 무선으로 사용하세요"}, "slideRemoteModeTitle": {"message": "콜드 스토리지, 빠른 접근"}, "slideSmartAccountUpgradeDescription": {"message": "같은 주소, 더욱 스마트한 기능"}, "slideSmartAccountUpgradeTitle": {"message": "스마트 계정 사용을 시작하세요"}, "slideSolanaDescription": {"message": "시작하려면 솔라나 계정을 생성하세요"}, "slideSolanaTitle": {"message": "이제 솔라나가 지원됩니다"}, "slideSweepStakeDescription": {"message": "NFT를 민팅하고 당첨 기회를 잡으세요"}, "slideSweepStakeTitle": {"message": "$5000 USDC 경품 이벤트에 참여하세요!"}, "smartAccountAccept": {"message": "스마트 계정 사용"}, "smartAccountBetterTransaction": {"message": "더욱 빠른 트랜잭션, 더욱 낮은 수수료"}, "smartAccountBetterTransactionDescription": {"message": "여러 트랜잭션을 한꺼번에 처리하여 비용과 시간을 절약하세요."}, "smartAccountFeaturesDescription": {"message": "계정 주소는 그대로 유지되며, 언제든지 다시 전환할 수 있습니다"}, "smartAccountLabel": {"message": "스마트 계정"}, "smartAccountPayToken": {"message": "언제든지, 원하는 토큰으로 결제하세요"}, "smartAccountPayTokenDescription": {"message": "보유 중인 토큰으로 네트워크 수수료를 결제하세요."}, "smartAccountReject": {"message": "스마트 계정을 사용하지 마세요"}, "smartAccountRequestFor": {"message": "요청 계정:"}, "smartAccountSameAccount": {"message": "같은 계정, 더 스마트한 기능."}, "smartAccountSplashTitle": {"message": "스마트 계정을 사용하시겠어요?"}, "smartAccountUpgradeBannerDescription": {"message": "같은 주소. 더욱 스마트한 기능."}, "smartAccountUpgradeBannerTitle": {"message": "스마트 계정으로 전환"}, "smartContracts": {"message": "스마트 계약"}, "smartSwapsErrorNotEnoughFunds": {"message": "스마트 스왑 자금 부족"}, "smartSwapsErrorUnavailable": {"message": "스마트 스왑을 잠시 사용할 수 없습니다."}, "smartTransactionCancelled": {"message": "트랜잭션이 취소되었습니다"}, "smartTransactionCancelledDescription": {"message": "트랜잭션이 완료되지 않았습니다. 가스비 지출이 없도록 트랜잭션을 취소했습니다."}, "smartTransactionError": {"message": "트랜잭션 실패"}, "smartTransactionErrorDescription": {"message": "시장 상황이 갑자기 변하면 실패할 수 있습니다. 문제가 지속되면 NeoNix 고객 지원으로 문의하세요."}, "smartTransactionPending": {"message": "트랜잭션이 제출되었습니다"}, "smartTransactionSuccess": {"message": "트랜잭션 완료"}, "smartTransactions": {"message": "스마트 트랜잭션"}, "smartTransactionsEnabledDescription": {"message": " 와 MEV 보호. 이제 기본적으로 켜져 있습니다."}, "smartTransactionsEnabledLink": {"message": "더 높은 성공률"}, "smartTransactionsEnabledTitle": {"message": "더욱 스마트한 트랜잭션"}, "snapAccountCreated": {"message": "계정 생성됨"}, "snapAccountCreatedDescription": {"message": "새 계정을 사용할 준비가 되었습니다!"}, "snapAccountCreationFailed": {"message": "계정 생성 실패"}, "snapAccountCreationFailedDescription": {"message": "$1에서 계정을 만들지 못했습니다.", "description": "$1 is the snap name"}, "snapAccountRedirectFinishSigningTitle": {"message": "서명 완료"}, "snapAccountRedirectSiteDescription": {"message": "$1의 지침을 따르세요"}, "snapAccountRemovalFailed": {"message": "계정 삭제 실패"}, "snapAccountRemovalFailedDescription": {"message": "$1에서 계정을 삭제하지 못했습니다.", "description": "$1 is the snap name"}, "snapAccountRemoved": {"message": "계정 제거됨"}, "snapAccountRemovedDescription": {"message": "이 계정은 더 이상 NeoNix에서 사용할 수 없습니다."}, "snapAccounts": {"message": "계정 Snap"}, "snapAccountsDescription": {"message": "제삼자 Snap이 제어하는 계정입니다."}, "snapConnectTo": {"message": "$1에 연결", "description": "$1 is the website URL or a Snap name. Used for Snaps pre-approved connections."}, "snapConnectionPermissionDescription": {"message": "승인 없이 $1이(가) $2에 자동으로 연결되도록 하세요.", "description": "Used for Snap pre-approved connections. $1 is the Snap name, $2 is a website URL."}, "snapConnectionWarning": {"message": "$1에서 $2 사용을 원합니다.", "description": "$2 is the snap and $1 is the dapp requesting connection to the snap."}, "snapDetailWebsite": {"message": "웹사이트"}, "snapHomeMenu": {"message": "Snap 홈 메뉴"}, "snapInstallRequest": {"message": "$1 설치는 다음과 같은 권한을 허용합니다.", "description": "$1 is the snap name."}, "snapInstallSuccess": {"message": "설치 완료"}, "snapInstallWarningCheck": {"message": "$1 스냅이 다음을 수행할 수 있는 권한을 요청합니다:", "description": "Warning message used in popup displayed on snap install. $1 is the snap name."}, "snapInstallWarningHeading": {"message": "주의하여 진행하세요"}, "snapInstallWarningPermissionDescriptionForBip32View": {"message": "$1 스냅이 사용자의 공개 키(및 주소)를 볼 수 있도록 허용합니다. 이를 통해 계정이나 자산에 대한 통제권이 부여되지는 않습니다.", "description": "An extended description for the `snap_getBip32PublicKey` permission used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionDescriptionForEntropy": {"message": "$1 Snap이 요청된 네트워크에서 계정과 자산을 관리하도록 허용합니다. 해당 계정은 비밀복구구문(공개하지 않음)을 사용하여 파생되고 백업됩니다. 키를 파생할 수 있는 기능을 통해 $1 스냅은 이더리움(EVM)뿐 아니라 다양한 블록체인 프로토콜을 지원할 수 있습니다.", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionNameForEntropy": {"message": "$1 계정 관리", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallWarningPermissionNameForViewPublicKey": {"message": "$1에 관한 공개 키 보기", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallationErrorDescription": {"message": "$1 스냅을 설치할 수 없습니다.", "description": "Error description used when snap installation fails. $1 is the snap name."}, "snapInstallationErrorTitle": {"message": "설치 실패", "description": "Error title used when snap installation fails."}, "snapResultError": {"message": "오류"}, "snapResultSuccess": {"message": "성공"}, "snapResultSuccessDescription": {"message": "$1 사용 가능"}, "snapUIAssetSelectorTitle": {"message": "자산 선택"}, "snapUpdateAlertDescription": {"message": "$1의 최신 버전 받기", "description": "Description used in Snap update alert banner when snap update is available. $1 is the Snap name."}, "snapUpdateAvailable": {"message": "업데이트 가능"}, "snapUpdateErrorDescription": {"message": "$1 스냅을 업데이트할 수 없습니다.", "description": "Error description used when snap update fails. $1 is the snap name."}, "snapUpdateErrorTitle": {"message": "업데이트 실패", "description": "Error title used when snap update fails."}, "snapUpdateRequest": {"message": "$1 업데이트는 다음과 같은 권한을 허용합니다.", "description": "$1 is the Snap name."}, "snapUpdateSuccess": {"message": "업데이트 완료"}, "snapUrlIsBlocked": {"message": "이 Snap이 차단된 사이트로 이동하려 합니다. $1."}, "snaps": {"message": "스냅"}, "snapsConnected": {"message": "Snap 연결됨"}, "snapsNoInsight": {"message": "표시할 인사이트가 없습니다"}, "snapsPrivacyWarningFirstMessage": {"message": "귀하는 Consensys $1에 정의된 바와 같이 달리 명시되지 않는 한 설치하는 모든 Snap이 타사 서비스임을 인정합니다. 타사 서비스 이용은 해당 타사 서비스 제공업체가 정한 별도의 약관이 적용됩니다. Consensys는 특정인이 특정한 이유로 Snap을 사용하는 것을 권장하지 않습니다. 타사 서비스에 액세스, 신뢰, 사용하는 것은 본인의 책임입니다. Consensys는 타사 서비스 사용으로 인해 귀하의 계정에 발생하는 손실에 대한 모든 책임과 의무를 부인합니다.", "description": "First part of a message in popup modal displayed when installing a snap for the first time. $1 is terms of use link."}, "snapsPrivacyWarningSecondMessage": {"message": "타사와 공유하는 모든 정보는 해당 타사의 개인정보 처리방침에 따라 직접 수집됩니다. 더 자세한 내용은 해당 회사의 개인정보 처리방침을 참고하시기 바랍니다.", "description": "Second part of a message in popup modal displayed when installing a snap for the first time."}, "snapsPrivacyWarningThirdMessage": {"message": "Consensys는 귀하가 타사와 공유한 정보에 접근할 수 없습니다.", "description": "Third part of a message in popup modal displayed when installing a snap for the first time."}, "snapsSettings": {"message": "Snap 설정"}, "snapsTermsOfUse": {"message": "이용약관"}, "snapsToggle": {"message": "스냅은 활성화된 상태에서만 작동합니다."}, "snapsUIError": {"message": "$1 작성자에게 연락하여 향후 지원을 요청하세요.", "description": "This is shown when the insight snap throws an error. $1 is the snap name"}, "solanaAccountRequested": {"message": "이 사이트에서는 솔라나 계정이 필요합니다."}, "solanaAccountRequired": {"message": "이 사이트에 연결하려면 솔라나 계정이 필요합니다."}, "solanaImportAccounts": {"message": "솔라나 계정 가져오기"}, "solanaImportAccountsDescription": {"message": "다른 지갑에서 솔라나 계정을 가져오려면 비밀복구구문을 입력하세요."}, "solanaMoreFeaturesComingSoon": {"message": "더 많은 기능이 곧 추가됩니다"}, "solanaMoreFeaturesComingSoonDescription": {"message": "NFT, 하드웨어 지갑 지원 등 여러 가지가 곧 추가됩니다."}, "solanaOnNeoNix": {"message": "NeoNix 플랫폼에서의 솔라나 이용"}, "solanaSendReceiveSwapTokens": {"message": "토큰 송금, 받기, 스왑"}, "solanaSendReceiveSwapTokensDescription": {"message": "SOL, USDC 등의 토큰을 전송 및 트랜잭션하세요."}, "someNetworks": {"message": "네트워크 $1개"}, "somethingDoesntLookRight": {"message": "무언가 잘못되었나요? $1", "description": "A false positive message for users to contact support. $1 is a link to the support page."}, "somethingIsWrong": {"message": "문제가 발생했습니다. 페이지를 다시 로드하세요."}, "somethingWentWrong": {"message": "페이지를 로드할 수 없습니다."}, "sortBy": {"message": "정렬 기준:"}, "sortByAlphabetically": {"message": "알파벳순(A-Z)"}, "sortByDecliningBalance": {"message": "잔액 내림차순($1 최고-최저)", "description": "Indicates a descending order based on token fiat balance. $1 is the preferred currency symbol"}, "source": {"message": "소스"}, "spamModalBlockedDescription": {"message": "이 사이트는 1분간 차단됩니다."}, "spamModalBlockedTitle": {"message": "이 사이트를 일시적으로 차단했습니다"}, "spamModalDescription": {"message": "여러 요청이 반복될 경우 사이트를 일시적으로 차단할 수 있습니다."}, "spamModalTemporaryBlockButton": {"message": "이 사이트 일시적으로 차단"}, "spamModalTitle": {"message": "여러 요청이 감지되었습니다"}, "speed": {"message": "속도"}, "speedUp": {"message": "가속화"}, "speedUpCancellation": {"message": "이 취소 가속화"}, "speedUpExplanation": {"message": "현재 네트워크 상태를 기반으로 가스비를 업데이트하고 최소 10% 인상했습니다(네트워크에서 요구함)."}, "speedUpPopoverTitle": {"message": "트랜잭션 가속화"}, "speedUpTooltipText": {"message": "새로운 가스비"}, "speedUpTransaction": {"message": "이 트랜잭션 가속화"}, "spendLimitInsufficient": {"message": "지출 한도 부족"}, "spendLimitInvalid": {"message": "지출 한도가 올바르지 않습니다. 지출 한도는 양수여야 합니다."}, "spendLimitPermission": {"message": "지출 한도 권한"}, "spendLimitRequestedBy": {"message": "$1에서 요청한 지출 한도", "description": "Origin of the site requesting the spend limit"}, "spendLimitTooLarge": {"message": "지출 한도가 너무 큼"}, "spender": {"message": "사용자"}, "spenderTooltipDesc": {"message": "NFT를 인출할 수 있는 주소입니다."}, "spenderTooltipERC20ApproveDesc": {"message": "이 주소는 회원님을 대신하여 토큰을 사용할 수 있는 주소입니다."}, "spendingCap": {"message": "지출 한도"}, "spendingCaps": {"message": "지출 한도"}, "srpInputNumberOfWords": {"message": "제 구문은 $1개의 단어로 이루어져 있습니다", "description": "This is the text for each option in the dropdown where a user selects how many words their secret recovery phrase has during import. The $1 is the number of words (either 12, 15, 18, 21, or 24)."}, "srpListName": {"message": "비밀복구구문 $1", "description": "$1 is the order of the Secret Recovery Phrase"}, "srpListNumberOfAccounts": {"message": "계정 $1개", "description": "$1 is the number of accounts in the list"}, "srpListSelectionDescription": {"message": "새 계정이 생성될 때 사용될 비밀복구구문"}, "srpListSingleOrZero": {"message": "계정 $1개", "description": "$1 is the number of accounts in the list, it is either 1 or 0"}, "srpPasteFailedTooManyWords": {"message": "단어가 24개를 초과하여 붙여넣기에 실패했습니다. 비밀복구구문은 24개 이하의 단어로 이루어집니다.", "description": "Description of SRP paste error when the pasted content has too many words"}, "srpPasteTip": {"message": "비밀복구구문 전체를 아무 입력란에 붙여넣을 수 있습니다", "description": "Our secret recovery phrase input is split into one field per word. This message explains to users that they can paste their entire secrete recovery phrase into any field, and we will handle it correctly."}, "srpSecurityQuizGetStarted": {"message": "시작하기"}, "srpSecurityQuizImgAlt": {"message": "중앙의 열쇠 구멍에 있는 눈과 세 개의 유동 비밀번호 필드"}, "srpSecurityQuizIntroduction": {"message": "비밀복구구문을 찾으려면 두 가지 질문에 올바르게 답해야 합니다"}, "srpSecurityQuizQuestionOneQuestion": {"message": "비밀복구구문을 분실하시면 NeoNix가..."}, "srpSecurityQuizQuestionOneRightAnswer": {"message": "도와드릴 수 없습니다"}, "srpSecurityQuizQuestionOneRightAnswerDescription": {"message": "따라서 이를 적거나, 금속 등에 새기거나, 여러 비밀 장소에 보관하여 절대로 잃어버리지 않도록 하세요. 한 번 잃어버리면 영원히 찾을 수 없습니다."}, "srpSecurityQuizQuestionOneRightAnswerTitle": {"message": "맞습니다! 아무도 본인의 비밀복구구문을 복구할 수 없습니다"}, "srpSecurityQuizQuestionOneWrongAnswer": {"message": "찾아드릴 수 있습니다"}, "srpSecurityQuizQuestionOneWrongAnswerDescription": {"message": "비밀복구구문은 한 번 잃어버리면 영원히 찾을 수 없습니다. 누가 뭐라고 해도 아무도 이를 찾아드리지 못합니다."}, "srpSecurityQuizQuestionOneWrongAnswerTitle": {"message": "아닙니다! 아무도 본인의 비밀복구구문을 복구할 수 없습니다"}, "srpSecurityQuizQuestionTwoQuestion": {"message": "누군가, 심지어 고객 센터 직원이라고 해도 여러분의 비밀복구구문을 물어본다면..."}, "srpSecurityQuizQuestionTwoRightAnswer": {"message": "이는 반드시 사기입니다"}, "srpSecurityQuizQuestionTwoRightAnswerDescription": {"message": "비밀복구구문이 필요하다고 하는 사람은 모두 거짓말쟁이입니다. 그런 자들과 비밀복구구문을 공유하면 자산을 도둑맞게 됩니다."}, "srpSecurityQuizQuestionTwoRightAnswerTitle": {"message": "맞습니다! 비밀복구구문은 아무와도 공유하면 안 됩니다"}, "srpSecurityQuizQuestionTwoWrongAnswer": {"message": "그들에게 이것을 주어야 합니다"}, "srpSecurityQuizQuestionTwoWrongAnswerDescription": {"message": "비밀복구구문이 필요하다고 하는 사람은 모두 거짓말쟁이입니다. 그런 자들과 비밀복구구문을 공유하면 자산을 도둑맞게 됩니다."}, "srpSecurityQuizQuestionTwoWrongAnswerTitle": {"message": "맞습니다! 비밀복구구문은 절대로 아무와도 공유하면 안 됩니다"}, "srpSecurityQuizTitle": {"message": "보안 질문"}, "srpToggleShow": {"message": "비밀복구구문 중에서 이 단어 공개하기/숨기기", "description": "Describes a toggle that is used to show or hide a single word of the secret recovery phrase"}, "srpWordHidden": {"message": "이 단어는 숨겨져 있습니다", "description": "Explains that a word in the secret recovery phrase is hidden"}, "srpWordShown": {"message": "이 단어는 공개되어 있습니다", "description": "Explains that a word in the secret recovery phrase is being shown"}, "stable": {"message": "안정적"}, "stableLowercase": {"message": "안정적"}, "stake": {"message": "스테이크"}, "staked": {"message": "스테이킹됨"}, "standardAccountLabel": {"message": "표준 계정"}, "startEarning": {"message": "수익 창출 시작"}, "stateLogError": {"message": "상태 로그를 가져오는 도중 오류가 발생했습니다."}, "stateLogFileName": {"message": "NeoNix 상태 로그"}, "stateLogs": {"message": "상태 로그"}, "stateLogsDescription": {"message": "상태 로그에 공개 계정 주소와 전송된 트랜잭션이 있습니다."}, "status": {"message": "상태"}, "statusNotConnected": {"message": "연결되지 않음"}, "statusNotConnectedAccount": {"message": "계정 연결되지 않음"}, "step1LatticeWallet": {"message": "Lattice1을 연결하세요."}, "step1LatticeWalletMsg": {"message": "Lattice1이 장치 설정을 마치고 온라인 상태가 되면 NeoNix를 연결할 수 있습니다. 장치의 잠금을 해제하고 장치 ID를 준비하세요.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step1LedgerWallet": {"message": "Ledger 앱 다운로드"}, "step1LedgerWalletMsg": {"message": "$1의 잠금을 해제하려면 다운로드, 설정 및 비밀번호를 입력하세요.", "description": "$1 represents the `ledgerLiveApp` localization value"}, "step1TrezorWallet": {"message": "Trezor 지갑 연결"}, "step1TrezorWalletMsg": {"message": "Trezor 지갑을 컴퓨터에 바로 연결하세요. 반드시 올바른 비밀구문을 사용하세요.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step2LedgerWallet": {"message": "Ledger 지갑 연결"}, "step2LedgerWalletMsg": {"message": "지갑을 컴퓨터에 바로 연결하고 잠금 해제한 후 이더리움 앱을 여세요..", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "stillGettingMessage": {"message": "아직 이 메시지가 표시되시나요?"}, "strong": {"message": "강함"}, "stxCancelled": {"message": "스왑이 실패했을 것입니다"}, "stxCancelledDescription": {"message": "트랜잭션 실패가 예상되었습니다. 불필요한 가스비 지출을 방지하기 위해 트랜잭션이 취소되었습니다."}, "stxCancelledSubDescription": {"message": "스왑을 다시 진행하세요. 다음에도 유사한 위험이 발생한다면 보호해 드리겠습니다."}, "stxFailure": {"message": "스왑 실패"}, "stxFailureDescription": {"message": "시장 상황이 갑자기 변하면 실패할 수 있습니다. 문제가 지속되면 $1(으)로 문의하세요.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.NeoNix.io"}, "stxOptInSupportedNetworksDescription": {"message": "스마트 트랜잭션을 활성화하면 지원되는 네트워크에서 더 안전하고 신뢰할 수 있는 트랜잭션을 할 수 있습니다. $1"}, "stxPendingPrivatelySubmittingSwap": {"message": "스왑을 비공개로 제출하는 중..."}, "stxPendingPubliclySubmittingSwap": {"message": "스왑을 공개로 제출하는 중..."}, "stxSuccess": {"message": "스왑 완료!"}, "stxSuccessDescription": {"message": "현재 $1(을)를 사용할 수 있습니다.", "description": "$1 is a token symbol, e.g. ETH"}, "stxSwapCompleteIn": {"message": "스왑 완료까지 남은 시간:", "description": "'<' means 'less than', e.g. <PERSON><PERSON><PERSON> will complete in < 2:59"}, "stxTryingToCancel": {"message": "트랜잭션을 취소하려고 합니다..."}, "stxUnknown": {"message": "알 수 없는 상태"}, "stxUnknownDescription": {"message": "트랜잭션이 성공했지만 어떤 트랜잭션인지 확인되지 않습니다. 이 스왑이 처리되는 동안 다른 트랜잭션이 제출되었기 때문일 수 있습니다."}, "stxUserCancelled": {"message": "스왑 취소됨"}, "stxUserCancelledDescription": {"message": "트랜잭션이 취소되어 가스비를 지급하지 않았습니다."}, "submit": {"message": "제출"}, "submitted": {"message": "제출됨"}, "suggestedBySnap": {"message": "제안인: $1", "description": "$1 is the snap name"}, "suggestedCurrencySymbol": {"message": "제안된 통화 심볼:"}, "suggestedTokenName": {"message": "추천 이름:"}, "supplied": {"message": "예치함"}, "support": {"message": "지원"}, "supportCenter": {"message": "지원 센터 방문하기"}, "supportMultiRpcInformation": {"message": "이제 단일 네트워크에 대해 여러 개의 RPC를 지원합니다. 충돌하는 정보를 해결하기 위해 최근의 RPC를 기본값으로 선택했습니다."}, "surveyConversion": {"message": "설문조사에 참여하세요"}, "surveyTitle": {"message": "NeoNix의 미래를 그리세요"}, "swap": {"message": "스왑"}, "swapAdjustSlippage": {"message": "슬리피지 조정"}, "swapAggregator": {"message": "애그리게이터"}, "swapAllowSwappingOf": {"message": "$1 스왑 허용", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "swapAmountReceived": {"message": "보장 금액"}, "swapAmountReceivedInfo": {"message": "수신하는 최소 금액입니다. 슬리패지에 따라 추가 금액을 받을 수도 있습니다."}, "swapAndSend": {"message": "스왑 및 전송"}, "swapAnyway": {"message": "스왑 계속 진행"}, "swapApproval": {"message": "스왑을 위해 $1 승인", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be swapped.. $1 is the symbol of a token that has been approved."}, "swapApproveNeedMoreTokens": {"message": "이 스왑을 완료하려면 $1개의 추가 $2이(가) 필요합니다.", "description": "Tells the user how many more of a given token they need for a specific swap. $1 is an amount of tokens and $2 is the token symbol."}, "swapAreYouStillThere": {"message": "아직 이용 중인가요?"}, "swapAreYouStillThereDescription": {"message": "계속하기 원하시면 최신 견적을 보여드리겠습니다"}, "swapConfirmWithHwWallet": {"message": "하드웨어 지갑으로 컨펌합니다."}, "swapContinueSwapping": {"message": "스왑 계속"}, "swapContractDataDisabledErrorDescription": {"message": "Ledger의 이더리움 앱에서 \"설정 \"으로 이동하여 계약 데이터를 허용한 후, 스왑을 다시 시도하세요."}, "swapContractDataDisabledErrorTitle": {"message": "Ledger에서 계약 데이터를 사용할 수 없습니다."}, "swapCustom": {"message": "맞춤"}, "swapDecentralizedExchange": {"message": "분산형 교환"}, "swapDirectContract": {"message": "직접 계약"}, "swapEditLimit": {"message": "한도 편집"}, "swapEnableDescription": {"message": "이는 NeoNix에게 $1 스왑 권한을 부여하는 것으로, 필수 사항입니다.", "description": "Gives the user info about the required approval transaction for swaps. $1 will be the symbol of a token being approved for swaps."}, "swapEnableTokenForSwapping": {"message": "스왑하려면 $1이(가) 필요합니다.", "description": "$1 is for the 'enableToken' key, e.g. 'enable ETH'"}, "swapEnterAmount": {"message": "금액 입력"}, "swapEstimatedNetworkFees": {"message": "예상 네트워크 수수료"}, "swapEstimatedNetworkFeesInfo": {"message": "스왑을 완료하는 데 사용될 예상 네트워크 수수료입니다. 실제 금액은 네트워크 상태에 따라 달라질 수 있습니다."}, "swapFailedErrorDescriptionWithSupportLink": {"message": "트랜잭션이 실패할 경우 언제든 문의하세요. 오류가 해결되지 않는다면 고객 지원 $1에 문의하세요.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.NeoNix.io"}, "swapFailedErrorTitle": {"message": "스왑 실패"}, "swapFetchingQuote": {"message": "견적 가져오기"}, "swapFetchingQuoteNofN": {"message": "$2 중 $1 견적 가져오기", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapFetchingQuotes": {"message": "견적을 가져오는 중..."}, "swapFetchingQuotesErrorDescription": {"message": "음.... 문제가 발생했습니다. 다시 시도해 보고 오류가 해결되지 않는다면 고객 지원에 문의하세요."}, "swapFetchingQuotesErrorTitle": {"message": "견적을 가져오는 중 오류 발생"}, "swapFromTo": {"message": "$1을(를) $2(으)로 스왑", "description": "Tells a user that they need to confirm on their hardware wallet a swap of 2 tokens. $1 is a source token and $2 is a destination token"}, "swapGasFeesDetails": {"message": "가스비는 예상치이며 네트워크 트래픽 및 트랜잭션 복잡성에 따라 변동됩니다."}, "swapGasFeesExplanation": {"message": "NeoNix는 가스비로 수익을 얻지 않습니다. 이 가스비는 추정치이며 네트워크의 혼잡도와 거래의 복잡성에 따라 변할 수 있습니다. 자세히 알아보기 $1.", "description": "$1 is a link (text in link can be found at 'swapGasFeesSummaryLinkText')"}, "swapGasFeesExplanationLinkText": {"message": "여기", "description": "Text for link in swapGasFeesExplanation"}, "swapGasFeesLearnMore": {"message": "가스비 자세히 알아보기"}, "swapGasFeesSplit": {"message": "이전 화면의 가스비는 이 두 트랜잭션으로 나뉩니다."}, "swapGasFeesSummary": {"message": "가스비는 $1 네트워크에서 트랜잭션을 처리하는 암호화폐 채굴자에게 지급됩니다. NeoNix는 가스비로 수익을 창출하지 않습니다.", "description": "$1 is the selected network, e.g. Ethereum or BSC"}, "swapGasIncludedTooltipExplanation": {"message": "이 견적에는 보내거나 받는 토큰 금액을 조정하는 방식으로 가스비가 포함됩니다. 활동 목록에서 별도의 거래로 ETH를 받을 수 있습니다."}, "swapGasIncludedTooltipExplanationLinkText": {"message": "가스비에 대해 더 자세히 알아보기"}, "swapHighSlippage": {"message": "높은 슬리피지"}, "swapIncludesGasAndNeoNixFee": {"message": "가스비 및 $1%의 NeoNix 수수료가 포함됩니다", "description": "Provides information about the fee that NeoNix takes for swaps. $1 is a decimal number."}, "swapIncludesMMFee": {"message": "$1%의 NeoNix 요금이 포함됩니다.", "description": "Provides information about the fee that NeoNix takes for swaps. $1 is a decimal number."}, "swapIncludesMMFeeAlt": {"message": "견적에는 $1%의 NeoNix 수수료가 반영됩니다", "description": "Provides information about the fee that NeoNix takes for swaps using the latest copy. $1 is a decimal number."}, "swapIncludesNeoNixFeeViewAllQuotes": {"message": "$1% NeoNix 요금 - $2 포함", "description": "Provides information about the fee that NeoNix takes for swaps. $1 is a decimal number and $2 is a link to view all quotes."}, "swapLearnMore": {"message": "스왑 자세히 알아보기"}, "swapLiquiditySourceInfo": {"message": "저희는 여러 유동성 공급원(거래소, 애그리게이터, 투자전문기관)을 검색하여 환율과 네트워크 수수료를 비교해 드립니다."}, "swapLowSlippage": {"message": "낮은 슬리피지"}, "swapMaxSlippage": {"message": "최대 슬리패지"}, "swapNeoNixFee": {"message": "NeoNix 수수료"}, "swapNeoNixFeeDescription": {"message": "$1%의 수수료는 자동으로 각 견적에 반영됩니다. 이는 NeoNix의 유동성 공급자 정보 집계용 소프트웨어 사용 라이선스 비용입니다.", "description": "Provides information about the fee that NeoNix takes for swaps. $1 is a decimal number."}, "swapNQuotesWithDot": {"message": "$1 견적.", "description": "$1 is the number of quotes that the user can select from when opening the list of quotes on the 'view quote' screen"}, "swapNewQuoteIn": {"message": "$1의 새 견적", "description": "Tells the user the amount of time until the currently displayed quotes are update. $1 is a time that is counting down from 1:00 to 0:00"}, "swapNoTokensAvailable": {"message": "$1 단어와 일치하는 토큰이 없습니다", "description": "Tells the user that a given search string does not match any tokens in our token lists. $1 can be any string of text"}, "swapOnceTransactionHasProcess": {"message": "$1토큰은 이 트랜잭션이 처리되면 회원님의 계정에 추가됩니다.", "description": "This message communicates the token that is being transferred. It is shown on the awaiting swap screen. The $1 will be a token symbol."}, "swapPriceDifference": {"message": "$1 $2(~$3)을(를) $4 $5(~$6)(으)로 스왑하려고 합니다.", "description": "This message represents the price slippage for the swap.  $1 and $4 are a number (ex: 2.89), $2 and $5 are symbols (ex: ETH), and $3 and $6 are fiat currency amounts."}, "swapPriceDifferenceTitle": {"message": "~$1%의 가격 차이", "description": "$1 is a number (ex: 1.23) that represents the price difference."}, "swapPriceUnavailableDescription": {"message": "시장 가격 데이터가 부족하여 가격 영향을 파악할 수 없습니다. 스왑하기 전에 받게 될 토큰 수가 만족스러운지 컨펌하시기 바랍니다."}, "swapPriceUnavailableTitle": {"message": "진행하기 전에 요율 확인"}, "swapProcessing": {"message": "처리 중"}, "swapQuoteDetails": {"message": "견적 세부 정보"}, "swapQuoteNofM": {"message": "$1/$2개", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapQuoteSource": {"message": "견적 소스"}, "swapQuotesExpiredErrorDescription": {"message": "새 견적을 요청해 최신 요율을 확인하세요."}, "swapQuotesExpiredErrorTitle": {"message": "견적 시간 초과"}, "swapQuotesNotAvailableDescription": {"message": "현재 이 거래 경로를 사용할 수 없습니다. 금액, 네트워크, 토큰을 변경하면 최적의 옵션을 찾아드립니다."}, "swapQuotesNotAvailableErrorDescription": {"message": "금액 또는 슬리패지 설정을 조정한 후 다시 시도해 보세요."}, "swapQuotesNotAvailableErrorTitle": {"message": "사용 가능한 견적 없음"}, "swapRate": {"message": "요율"}, "swapReceiving": {"message": "수신 중"}, "swapReceivingInfoTooltip": {"message": "이것은 예상치입니다. 정확한 금액은 슬리패지에 따라 달라집니다."}, "swapRequestForQuotation": {"message": "견적 요청"}, "swapSelect": {"message": "선택"}, "swapSelectAQuote": {"message": "견적 선택"}, "swapSelectAToken": {"message": "토큰 선택"}, "swapSelectQuotePopoverDescription": {"message": "다음은 여러 유동성 소스에서 수집한 전체 견적입니다."}, "swapSelectToken": {"message": "토큰 선택"}, "swapShowLatestQuotes": {"message": "최신 견적 표시"}, "swapSlippageHighDescription": {"message": "입력한 슬리피지($1%)가 매우 높은 것으로 간주되어 불량률이 발생할 수 있습니다.", "description": "$1 is the amount of % for slippage"}, "swapSlippageHighTitle": {"message": "높은 슬리피지"}, "swapSlippageLowDescription": {"message": "이렇게 낮은 값은($1%) 스왑 실패로 이어질 수 있습니다", "description": "$1 is the amount of % for slippage"}, "swapSlippageLowTitle": {"message": "낮은 슬리피지"}, "swapSlippageNegativeDescription": {"message": "슬리피지는 0보다 크거나 같아야 합니다."}, "swapSlippageNegativeTitle": {"message": "계속하려면 슬리피지를 높이세요"}, "swapSlippageOverLimitDescription": {"message": "슬리피지 허용치는 반드시 15% 이하여야 합니다. 이 보다 높으면 비율이 나빠집니다."}, "swapSlippageOverLimitTitle": {"message": "매우 높은 슬리피지"}, "swapSlippagePercent": {"message": "$1%", "description": "$1 is the amount of % for slippage"}, "swapSlippageTooltip": {"message": "주문 시점과 확인 시점 사이에 가격이 변동되는 현상을 \"슬리피지\"라고 합니다. 슬리피지가 \"최대 슬리피지\" 설정을 초과하면 스왑이 자동으로 취소됩니다."}, "swapSlippageZeroDescription": {"message": "제로 슬리피지 견적 제공자가 거의 없어 견적의 경쟁력이 약화될 수 있습니다"}, "swapSlippageZeroTitle": {"message": "제로 슬리피지 견적 제공자 소싱"}, "swapSource": {"message": "유동성 소스"}, "swapSuggested": {"message": "제안 스왑"}, "swapSuggestedGasSettingToolTipMessage": {"message": "스왑은 복잡하고 시간에 민감한 트랜잭션입니다. 성공적인 스왑의 비용과 확신 사이의 적절한 균형을 위해 이 가스비를 권장합니다."}, "swapSwapFrom": {"message": "다음에서 스왑"}, "swapSwapSwitch": {"message": "토큰 주문 변환"}, "swapSwapTo": {"message": "다음으로 스왑"}, "swapToConfirmWithHwWallet": {"message": "하드웨어 지갑으로 컨펌하기 위해"}, "swapTokenAddedManuallyDescription": {"message": "$1에서 이 토큰이 트랜잭션할 토큰이 맞는지 확인하세요.", "description": "$1 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenAddedManuallyTitle": {"message": "토큰 직접 추가됨"}, "swapTokenAvailable": {"message": "$1이(가) 계정에 추가되었습니다.", "description": "This message is shown after a swap is successful and communicates the exact amount of tokens the user has received for a swap. The $1 is a decimal number of tokens followed by the token symbol."}, "swapTokenBalanceUnavailable": {"message": "$1 잔액을 불러오지 못했습니다.", "description": "This message communicates to the user that their balance of a given token is currently unavailable. $1 will be replaced by a token symbol"}, "swapTokenNotAvailable": {"message": "이 지역에서는 토큰을 스왑할 수 없습니다"}, "swapTokenToToken": {"message": "$1에서 $2(으)로 스왑", "description": "Used in the transaction display list to describe a swap. $1 and $2 are the symbols of tokens in involved in a swap."}, "swapTokenVerifiedOn1SourceDescription": {"message": "$1 토큰은 1 소스에서만 확인됩니다. 계속 진행하기 전에 $2에서도 확인하세요.", "description": "$1 is a token name, $2 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenVerifiedOn1SourceTitle": {"message": "잠재적 모조 토큰"}, "swapTokenVerifiedSources": {"message": "$1 소스에서 컨펌함. $2에서 확인하세요.", "description": "$1 the number of sources that have verified the token, $2 points the user to a block explorer as a place they can verify information about the token."}, "swapTooManyDecimalsError": {"message": "$1은(는) 소수점 이하 $2까지 허용됩니다.", "description": "$1 is a token symbol and $2 is the max. number of decimals allowed for the token"}, "swapTransactionComplete": {"message": "트랜잭션 완료"}, "swapTwoTransactions": {"message": "트랜잭션 2건"}, "swapUnknown": {"message": "알 수 없음"}, "swapZeroSlippage": {"message": "0% 슬리패지"}, "swapsMaxSlippage": {"message": "슬리피지 허용치"}, "swapsNotEnoughToken": {"message": "$1 부족", "description": "Tells the user that they don't have enough of a token for a proposed swap. $1 is a token symbol"}, "swapsViewInActivity": {"message": "활동에서 보기"}, "switch": {"message": "전환"}, "switchEthereumChainConfirmationDescription": {"message": "이는 NeoNix에서 선택한 네트워크를 이전에 추가한 다음 네트워크로 전환하게 됩니다:"}, "switchEthereumChainConfirmationTitle": {"message": "이 사이트가 네트워크를 전환하도록 허용하시겠습니까?"}, "switchInputCurrency": {"message": "입력 통화 전환"}, "switchNetwork": {"message": "네트워크 전환"}, "switchNetworks": {"message": "네트워크 전환"}, "switchToNetwork": {"message": "$1 네트워크로 전환", "description": "$1 represents the custom network that has previously been added"}, "switchToThisAccount": {"message": "이 계정으로 전환"}, "switchedNetworkToastDecline": {"message": "다시 표시 안 함"}, "switchedNetworkToastMessage": {"message": "$1 계정이 현재 $2 네트워크에서 활성화되어 있습니다", "description": "$1 represents the account name, $2 represents the network name"}, "switchedNetworkToastMessageNoOrigin": {"message": "이제 $1을(를) 사용 중입니다", "description": "$1 represents the network name"}, "switchingNetworksCancelsPendingConfirmations": {"message": "네트워크를 전환하면 대기 중인 모든 컨펌 작업이 취소됩니다."}, "symbol": {"message": "기호"}, "symbolBetweenZeroTwelve": {"message": "기호는 11자 이하여야 합니다."}, "tenPercentIncreased": {"message": "10% 인상"}, "terms": {"message": "이용약관"}, "termsOfService": {"message": "서비스 약관"}, "termsOfUseAgreeText": {"message": " 본인은 NeoNix의 사용 및 관련 모든 기능에 적용되는 이용약관에 동의합니다"}, "termsOfUseFooterText": {"message": "스크롤하여 모든 섹션의 내용을 확인하세요"}, "termsOfUseTitle": {"message": "이용약관이 개정되었습니다"}, "testNetworks": {"message": "테스트 네트워크"}, "testnets": {"message": "테스트넷"}, "theme": {"message": "테마"}, "themeDescription": {"message": "원하는 NeoNix 테마를 선택하세요."}, "thirdPartySoftware": {"message": "타사 소프트웨어 알림", "description": "Title of a popup modal displayed when installing a snap for the first time."}, "time": {"message": "시간"}, "tipsForUsingAWallet": {"message": "지갑 사용 팁"}, "tipsForUsingAWalletDescription": {"message": "토큰을 추가하면 웹3를 더 다양한 방법으로 사용할 수 있습니다."}, "to": {"message": "수신"}, "toAddress": {"message": "수신: $1", "description": "$1 is the address to include in the To label. It is typically shortened first using shortenAddress"}, "toggleDecodeDescription": {"message": "트랜잭션 데이터는 4byte.directory와 Sourcify 서비스를 사용하여 디코딩하고 읽기 쉬운 형식으로 표시합니다. 이는 대기 중이거나 과거 트랜잭션의 결과를 이해하는 데 도움이 되지만, IP 주소를 공유하는 결과가 초래될 수 있습니다."}, "token": {"message": "토큰"}, "tokenAddress": {"message": "토큰 주소"}, "tokenAlreadyAdded": {"message": "토큰이 이미 추가되었습니다."}, "tokenAutoDetection": {"message": "토큰 자동 감지"}, "tokenContractAddress": {"message": "토큰 계약 주소"}, "tokenDecimal": {"message": "토큰 십진수"}, "tokenDecimalFetchFailed": {"message": "토큰 십진수가 필요합니다. $1에서 찾아보세요"}, "tokenDetails": {"message": "토큰 상세 정보"}, "tokenFoundTitle": {"message": "$1개의 새 토큰을 찾았습니다"}, "tokenId": {"message": "토큰 ID"}, "tokenList": {"message": "토큰 목록:"}, "tokenMarketplace": {"message": "토큰 마켓플레이스"}, "tokenScamSecurityRisk": {"message": "토큰 사기 및 보안 위험"}, "tokenStandard": {"message": "토큰 표준"}, "tokenSymbol": {"message": "토큰 기호"}, "tokens": {"message": "토큰"}, "tokensFoundTitle": {"message": "$1개의 새 토큰을 찾았습니다", "description": "$1 is the number of new tokens detected"}, "tokensInCollection": {"message": "컬렉션 내 토큰"}, "tooltipApproveButton": {"message": "이해했습니다"}, "tooltipSatusConnected": {"message": "연결됨"}, "tooltipSatusConnectedUpperCase": {"message": "연결됨"}, "tooltipSatusNotConnected": {"message": "연결되지 않음"}, "total": {"message": "합계"}, "totalVolume": {"message": "총 거래량"}, "transaction": {"message": "트랜잭션"}, "transactionCancelAttempted": {"message": "$2에서 가스비가 $1인 트랜잭션의 취소가 시도되었습니다"}, "transactionCancelSuccess": {"message": "$2에서 성공적으로 트랜잭션을 취소했습니다"}, "transactionConfirmed": {"message": "$2에서 트랜잭션이 컨펌되었습니다."}, "transactionCreated": {"message": "$2에서 $1 값으로 생성된 트랜잭션"}, "transactionDataFunction": {"message": "기능"}, "transactionDetailGasHeading": {"message": "예상 가스비"}, "transactionDetailMultiLayerTotalSubtitle": {"message": "금액 + 요금"}, "transactionDropped": {"message": "$2에서의 트랜잭션이 중단되었습니다."}, "transactionError": {"message": "트랜잭션 오류입니다. 계약 코드에 예외가 발생했습니다."}, "transactionErrorNoContract": {"message": "비계약 주소에서 함수를 호출하고 있습니다."}, "transactionErrored": {"message": "트랜잭션에 오류가 발생했습니다."}, "transactionFlowNetwork": {"message": "네트워크"}, "transactionHistoryBaseFee": {"message": "기본 수수료(GWEI)"}, "transactionHistoryL1GasLabel": {"message": "총 L1 가스비"}, "transactionHistoryL2GasLimitLabel": {"message": "L2 가스 한도"}, "transactionHistoryL2GasPriceLabel": {"message": "L2 가스 가격"}, "transactionHistoryMaxFeePerGas": {"message": "가스당 최대 수수료"}, "transactionHistoryPriorityFee": {"message": "우선 수수료(GWEI)"}, "transactionHistoryTotalGasFee": {"message": "총 가스비"}, "transactionIdLabel": {"message": "트랜잭션 ID", "description": "Label for the source transaction ID field."}, "transactionIncludesTypes": {"message": "이 트랜잭션에 포함된 사항: $1."}, "transactionResubmitted": {"message": "$2에서 가스비를 $1(으)로 올린 트랜잭션이 다시 제출되었습니다."}, "transactionSettings": {"message": "트랜잭션 설정"}, "transactionSubmitted": {"message": "$2에서 가스비가 $1인 트랜잭션이 제출되었습니다."}, "transactionTotalGasFee": {"message": "가스비 합계", "description": "Label for the total gas fee incurred in the transaction."}, "transactionUpdated": {"message": "$2에서 트랜잭션이 업데이트되었습니다."}, "transactions": {"message": "트랜잭션"}, "transfer": {"message": "전송"}, "transferCrypto": {"message": "암호화폐 전송"}, "transferFrom": {"message": "전송 위치"}, "transferRequest": {"message": "전송 요청"}, "trillionAbbreviation": {"message": "T", "description": "Shortened form of 'trillion'"}, "troubleConnectingToLedgerU2FOnFirefox": {"message": "Ledger 연결에 오류가 발생했습니다. $1", "description": "$1 is a link to the wallet connection guide;"}, "troubleConnectingToLedgerU2FOnFirefox2": {"message": "하드웨어 지갑 연결 가이드를 확인하고 다시 시도하세요.", "description": "$1 of the ledger wallet connection guide"}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution": {"message": "Firefox 최신 버전을 사용하신다면 Firefox에서 U2F 지원 중단과 관련된 문제가 발생할 수 있습니다. $1 문제 해결 방법을 알아보세요.", "description": "It is a link to the ledger website for the workaround."}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution2": {"message": "여기에서", "description": "Second part of the error message; It is a link to the ledger website for the workaround."}, "troubleConnectingToWallet": {"message": "$1 연결 도중 문제가 발생했습니다. $2을(를) 검토하고 다시 시도해 보세요.", "description": "$1 is the wallet device name; $2 is a link to wallet connection guide"}, "troubleStarting": {"message": "NeoNix 실행 중 오류가 발생했습니다. 일시적인 오류일 수 있으니 확장 프로그램을 재시작해 보세요."}, "tryAgain": {"message": "다시 시도"}, "turnOff": {"message": "끄기"}, "turnOffNeoNixNotificationsError": {"message": "알림을 비활성화하는 동안 오류가 발생했습니다. 나중에 다시 시도해 주세요."}, "turnOn": {"message": "켜기"}, "turnOnNeoNixNotifications": {"message": "알림 켜기"}, "turnOnNeoNixNotificationsButton": {"message": "켜기"}, "turnOnNeoNixNotificationsError": {"message": "알림을 생성하는 동안 오류가 발생했습니다. 나중에 다시 시도해 주세요."}, "turnOnNeoNixNotificationsMessageFirst": {"message": "알림을 통해 지갑에서 무슨 일이 일어나고 있는지 계속 확인하세요."}, "turnOnNeoNixNotificationsMessagePrivacyBold": {"message": "알림 설정."}, "turnOnNeoNixNotificationsMessagePrivacyLink": {"message": "이 기능을 사용하는 동안 개인정보가 어떻게 보호되는지 알아보세요."}, "turnOnNeoNixNotificationsMessageSecond": {"message": "지갑 알림 기능을 사용하려면 프로필을 사용해 기기 간에 일부 설정을 동기화해야 합니다. $1"}, "turnOnNeoNixNotificationsMessageThird": {"message": "$1에서 언제든지 알림을 끌 수 있습니다"}, "turnOnTokenDetection": {"message": "향상된 토큰 감지 켜기"}, "tutorial": {"message": "튜토리얼"}, "twelveHrTitle": {"message": "12시간:"}, "u2f": {"message": "U2F", "description": "A name on an API for the browser to interact with devices that support the U2F protocol. On some browsers we use it to connect NeoNix to Ledger devices."}, "unapproved": {"message": "승인되지 않음"}, "unexpectedBehavior": {"message": "이 동작은 예상치 못한 것으로, 계정이 정상적으로 복구되었더라도 버그로 신고해야 합니다. 아래 링크를 통해 NeoNix에 버그를 신고해 주세요."}, "units": {"message": "단위"}, "unknown": {"message": "알 수 없음"}, "unknownCollection": {"message": "제목 미지정 컬렉션"}, "unknownNetworkForKeyEntropy": {"message": "알 수 없는 네트워크", "description": "Displayed on places like Snap install warning when regular name is not available."}, "unknownQrCode": {"message": "오류: QR 코드를 식별할 수 없습니다."}, "unlimited": {"message": "무제한"}, "unlock": {"message": "잠금 해제"}, "unpin": {"message": "고정 해제"}, "unrecognizedChain": {"message": "이 맞춤 네트워크는 인식되지 않았습니다.", "description": "$1 is a clickable link with text defined by the 'unrecognizedChanLinkText' key. The link will open to instructions for users to validate custom network details."}, "unsendableAsset": {"message": "NFT(ERC-721) 토큰 전송은 현재 지원되지 않습니다.", "description": "This is an error message we show the user if they attempt to send an NFT asset type, for which currently don't support sending"}, "unstableTokenPriceDescription": {"message": "이 토큰의 USD 가격은 변동성이 매우 커서 상호작용 시 큰 손실 위험이 있습니다."}, "unstableTokenPriceTitle": {"message": "불안정한 토큰 가격"}, "upArrow": {"message": "상승 화살표"}, "update": {"message": "업데이트"}, "updateEthereumChainConfirmationDescription": {"message": "이 사이트에서 기본 네트워크 URL 업데이트를 요청하고 있습니다. 기본값 및 네트워크 정보는 언제든지 수정할 수 있습니다."}, "updateNetworkConfirmationTitle": {"message": "$1 업데이트", "description": "$1 represents network name"}, "updateOrEditNetworkInformations": {"message": "정보를 업데이트하거나"}, "updateRequest": {"message": "업데이트 요청"}, "updatedRpcForNetworks": {"message": "네트워크 RPC 업데이트 완료"}, "uploadDropFile": {"message": "여기에 파일을 드롭"}, "uploadFile": {"message": "파일 업로드"}, "urlErrorMsg": {"message": "URI에는 적절한 HTTP/HTTPS 접두사가 필요합니다."}, "use4ByteResolution": {"message": "스마트 계약 디코딩"}, "useMultiAccountBalanceChecker": {"message": "일괄 계정 잔액 요청"}, "useMultiAccountBalanceCheckerSettingDescription": {"message": "계좌 잔액 요청을 일괄 처리하여 잔액을 더 빠르게 업데이트하세요. 이렇게 하면 계좌 잔액을 일괄적으로 가져올 수 있으므로 업데이트가 빨라져 더 나은 경험을 할 수 있습니다. 이 기능을 비활성화하면 제삼자가 회원님의 계정을 서로 연결할 가능성이 낮아질 수 있습니다."}, "useNftDetection": {"message": "NFT 자동 감지"}, "useNftDetectionDescriptionText": {"message": "NeoNix가 제삼자 서비스를 이용하여 회원님의 NFT를 추가할 수 있도록 합니다. NFT를 자동 감지하면 이러한 서비스에 IP와 계정 주소가 노출됩니다. 이 기능을 켜면 IP 주소가 이더리움 주소와 연결되고 사기꾼이 에어드랍한 가짜 NFT가 표시될 수 있습니다. 이러한 위험을 피하기 위해 수동으로 토큰을 추가할 수 있습니다."}, "usePhishingDetection": {"message": "피싱 감지 사용"}, "usePhishingDetectionDescription": {"message": "이더리움 사용자를 노리는 피싱 도메인에 대한 경고를 표시합니다"}, "useSafeChainsListValidation": {"message": "네트워크 세부 정보 확인"}, "useSafeChainsListValidationDescription": {"message": "NeoNix는 $1(이)라는 제삼자 서비스를 사용하여 정확하고 표준화된 네트워크 세부 정보를 표시합니다. 이를 통해 악성 또는 잘못된 네트워크에 연결할 가능성이 줄어듭니다. 이 기능을 사용하면 사용자의 IP 주소가 chainid.network에 노출됩니다."}, "useSafeChainsListValidationWebsite": {"message": "chainid.network", "description": "useSafeChainsListValidationWebsite is separated from the rest of the text so that we can bold the third party service name in the middle of them"}, "useTokenDetectionPrivacyDesc": {"message": "계정으로 전송된 토큰이 자동으로 표시되도록 하려면 타사 서버와의 통신을 통해 토큰 이미지를 불러와야 합니다. 이를 위해 타사 서버는 사용자의 IP 주소에 액세스하게 됩니다."}, "usedByClients": {"message": "다양한 클라이언트에서 사용합니다."}, "userName": {"message": "사용자 이름"}, "userOpContractDeployError": {"message": "스마트 계약 계정에서 계약 배포는 지원되지 않습니다."}, "version": {"message": "버전"}, "view": {"message": "보기"}, "viewActivity": {"message": "활동 보기"}, "viewAllQuotes": {"message": "모든 견적 보기"}, "viewContact": {"message": "연락처 보기"}, "viewDetails": {"message": "세부 정보 보기"}, "viewMore": {"message": "더 보기"}, "viewOnBlockExplorer": {"message": "블록 탐색기에서 보기"}, "viewOnCustomBlockExplorer": {"message": "$2에서 $1 보기", "description": "$1 is the action type. e.g (Account, Transaction, Swap) and $2 is the Custom Block Explorer URL"}, "viewOnEtherscan": {"message": "Etherscan에서 $1 보기", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "viewOnExplorer": {"message": "Explorer에서 보기"}, "viewOnOpensea": {"message": "Opensea에서 보기"}, "viewSolanaAccount": {"message": "솔라나 계정 보기"}, "viewTransaction": {"message": "트랜잭션 보기"}, "viewinExplorer": {"message": "Explorer에서 $1 보기", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "visitSite": {"message": "사이트 방문"}, "visitSupportDataConsentModalAccept": {"message": "컨펌"}, "visitSupportDataConsentModalDescription": {"message": "NeoNix 식별자와 앱 버전을 지원 센터에 공유하시겠습니까? 문제 해결에 도움이 될 수 있으며, 선택 사항입니다."}, "visitSupportDataConsentModalReject": {"message": "공유하지 않기"}, "visitSupportDataConsentModalTitle": {"message": "지원팀과 장치 세부정보 공유"}, "visitWebSite": {"message": "웹사이트를 방문하세요"}, "wallet": {"message": "지갑"}, "walletConnectionGuide": {"message": "당사의 하드웨어 지갑 연결 가이드"}, "wantToAddThisNetwork": {"message": "이 네트워크를 추가하시겠습니까?"}, "wantsToAddThisAsset": {"message": "이렇게 하면 다음 자산이 지갑에 추가됩니다"}, "warning": {"message": "경고"}, "warningFromSnap": {"message": "$1의 경고", "description": "$1 represents the name of the snap"}, "watchEthereumAccountsDescription": {"message": "이 옵션을 켜면 공개 주소 또는 ENS 이름을 통해 이더리움 계정을 볼 수 있게 됩니다. 이 베타 기능에 대한 피드백을 보내주시려면 이 $1을(를) 작성해 주세요.", "description": "$1 is the link to a product feedback form"}, "watchEthereumAccountsToggle": {"message": "이더리움 계정 모니터(베타)"}, "watchOutMessage": {"message": "$1에 주의하세요.", "description": "$1 is a link with text that is provided by the 'securityMessageLinkForNetworks' key"}, "weak": {"message": "약함"}, "web3": {"message": "웹3"}, "web3ShimUsageNotification": {"message": "현재의 웹사이트가 제거된 window.web3 API를 이용하려고 합니다. 이 사이트가 제대로 작동하지 않는 경우, $1을(를) 클릭해 자세히 알아보세요.", "description": "$1 is a clickable link."}, "webhid": {"message": "WebHID", "description": "Refers to a interface for connecting external devices to the browser. Used for connecting ledger to the browser. Read more here https://developer.mozilla.org/en-US/docs/Web/API/WebHID_API"}, "websites": {"message": "웹사이트", "description": "Used in the 'permission_rpc' message."}, "welcomeBack": {"message": "재방문을 환영합니다!"}, "welcomeToNeoNix": {"message": "시작하기"}, "whatsThis": {"message": "이것은 무엇인가요?"}, "willApproveAmountForBridging": {"message": "브릿지를 위해 $1을(를) 승인합니다."}, "willApproveAmountForBridgingHardware": {"message": "하드웨어 지갑에서 두 개의 트랜잭션을 확인해야 합니다."}, "withdrawing": {"message": "인출"}, "wrongNetworkName": {"message": "기록에 따르면 네트워크 이름이 이 체인 ID와 일치하지 않습니다."}, "yes": {"message": "예"}, "you": {"message": "회원님"}, "youDeclinedTheTransaction": {"message": "트랜잭션을 거부했습니다."}, "youNeedToAllowCameraAccess": {"message": "이 기능을 사용하려면 카메라 액세스를 허용해야 합니다."}, "youReceived": {"message": "받음:", "description": "Label indicating the amount and asset the user received."}, "youSent": {"message": "보냄:", "description": "Label indicating the amount and asset the user sent."}, "yourAccounts": {"message": "계정"}, "yourActivity": {"message": "내 활동"}, "yourBalance": {"message": "내 잔액"}, "yourNFTmayBeAtRisk": {"message": "NFT가 위험할 수 있습니다"}, "yourNetworks": {"message": "내 네트워크"}, "yourPrivateSeedPhrase": {"message": "비밀복구구문"}, "yourTransactionConfirmed": {"message": "트랜잭션이 이미 확인되었습니다"}, "yourTransactionJustConfirmed": {"message": "블록체인에서 거래가 확인되기 전에는 거래를 취소할 수 없습니다."}, "yourWalletIsReady": {"message": "지갑이 준비되었습니다"}}