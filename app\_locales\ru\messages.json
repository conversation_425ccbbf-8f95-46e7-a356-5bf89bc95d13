{"QRHardwareInvalidTransactionTitle": {"message": "Ошибка"}, "QRHardwareMismatchedSignId": {"message": "Несоответствующие данные транзакции. Проверьте ее реквизиты."}, "QRHardwarePubkeyAccountOutOfRange": {"message": "Больше нет счетов. Если вы хотите получить доступ к другому счету, не указанному ниже, повторно подключите аппаратный кошелек и выберите его."}, "QRHardwareScanInstructions": {"message": "Поместите QR-код перед камерой. Экран размытый, но это не повлияет на считывание."}, "QRHardwareSignRequestCancel": {"message": "Отклонить"}, "QRHardwareSignRequestDescription": {"message": "После подписания с помощью кошелька нажмите «Получить подпись»."}, "QRHardwareSignRequestGetSignature": {"message": "Получить подпись"}, "QRHardwareSignRequestSubtitle": {"message": "Отсканируйте QR-код с помощью кошелька"}, "QRHardwareSignRequestTitle": {"message": "Запросить подпись"}, "QRHardwareUnknownQRCodeTitle": {"message": "Ошибка"}, "QRHardwareUnknownWalletQRCode": {"message": "Недействительный QR-код. Отсканируйте QR-код синхронизации аппаратного кошелька."}, "QRHardwareWalletImporterTitle": {"message": "Сканировать QR-код"}, "QRHardwareWalletSteps1Description": {"message": "Вы можете выбрать из списка официальных партнеров, поддерживающих QR-код ниже."}, "QRHardwareWalletSteps1Title": {"message": "Подключите свой аппаратный кошелек на основе QR-кодов"}, "QRHardwareWalletSteps2Description": {"message": "<PERSON><PERSON>"}, "SrpListHideAccounts": {"message": "Скрыть $1 счета(-ов)", "description": "$1 is the number of accounts"}, "SrpListHideSingleAccount": {"message": "Скрыть 1 счет"}, "SrpListShowAccounts": {"message": "Показать $1 счета(-ов)", "description": "$1 is the number of accounts"}, "SrpListShowSingleAccount": {"message": "Показать 1 счет"}, "about": {"message": "О NeoNix"}, "accept": {"message": "Согласиться"}, "acceptTermsOfUse": {"message": "Я прочитал(-а) $1 и согласен(-на) с ними", "description": "$1 is the `terms` message"}, "accessingYourCamera": {"message": "Доступ к камере..."}, "account": {"message": "Счет"}, "accountActivity": {"message": "Активность счета"}, "accountActivityText": {"message": "Выберите счета, о которых хотите получать уведомления:"}, "accountDetails": {"message": "Реквизиты счета"}, "accountIdenticon": {"message": "Идентикон счета"}, "accountIsntConnectedToastText": {"message": "$1 не подключен к $2"}, "accountName": {"message": "Имя счета"}, "accountNameDuplicate": {"message": "Такое имя счета уже существует", "description": "This is an error message shown when the user enters a new account name that matches an existing account name"}, "accountNameReserved": {"message": "Это название счета зарезервировано", "description": "This is an error message shown when the user enters a new account name that is reserved for future use"}, "accountOptions": {"message": "Параметры счета"}, "accountPermissionToast": {"message": "Разрешения счета обновлены"}, "accountSelectionRequired": {"message": "Вам необходимо выбрать счет!"}, "accountTypeNotSupported": {"message": "Тип счета не поддерживается"}, "accounts": {"message": "Счета"}, "accountsConnected": {"message": "Счета подключены"}, "accountsPermissionsTitle": {"message": "Просматривать ваши счета и предлагать транзакции"}, "accountsSmallCase": {"message": "счета"}, "active": {"message": "Активный"}, "activity": {"message": "Активность"}, "activityLog": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> активности"}, "add": {"message": "Добавить"}, "addACustomNetwork": {"message": "Добавить пользовательскую сеть"}, "addANetwork": {"message": "Добавить сеть"}, "addANickname": {"message": "Добавить ник"}, "addAUrl": {"message": "Добавить URL"}, "addAccount": {"message": "Добавить счет"}, "addAccountFromNetwork": {"message": "Добавить счет $1", "description": "$1 is the network name, e.g. Bitcoin or Solana"}, "addAccountToNeoNix": {"message": "Добавить счет в NeoNix"}, "addAcquiredTokens": {"message": "Добавьте токены, которые вы приобрели с помощью NeoNix"}, "addAlias": {"message": "Добавить псевдоним"}, "addBitcoinAccountLabel": {"message": "Счет Биткойн (бета-версия)"}, "addBlockExplorer": {"message": "Добавить обозреватель блоков"}, "addBlockExplorerUrl": {"message": "Добавить URL обозревателя блоков"}, "addContact": {"message": "Добавить контакт"}, "addCustomNetwork": {"message": "Добавить пользовательскую сеть"}, "addEthereumChainWarningModalHeader": {"message": "Добавляйте этого поставщика RPC только в том случае, если уверены, что ему можно доверять. $1", "description": "$1 is addEthereumChainWarningModalHeaderPartTwo passed separately so that it can be bolded"}, "addEthereumChainWarningModalHeaderPartTwo": {"message": "Вредоносные провайдеры могут лгать о стейте блокчейна и записывать вашу сетевую активность."}, "addEthereumChainWarningModalListHeader": {"message": "Важно, чтобы ваш поставщик был надежным, поскольку он способен:"}, "addEthereumChainWarningModalListPointOne": {"message": "Просматривать ваши счета и IP-адрес и связывать их друг с другом"}, "addEthereumChainWarningModalListPointThree": {"message": "Показывать балансы счетов и другие ончейн-стейты"}, "addEthereumChainWarningModalListPointTwo": {"message": "Распространять ваши транзакции"}, "addEthereumChainWarningModalTitle": {"message": "Вы добавляете нового поставщика RPC для Мейн-нета Ethereum"}, "addEthereumWatchOnlyAccount": {"message": "Следить за счетом Ethereum (бета)"}, "addFriendsAndAddresses": {"message": "Добавьте друзей и адреса, которым доверяете"}, "addHardwareWalletLabel": {"message": "Аппаратный кошелек"}, "addIPFSGateway": {"message": "Добавьте предпочтительный шлюз IPFS"}, "addImportAccount": {"message": "Добавить счет или аппаратный кошелек"}, "addMemo": {"message": "Добавить примечание"}, "addNetwork": {"message": "Добавить сеть"}, "addNetworkConfirmationTitle": {"message": "Добавить $1", "description": "$1 represents network name"}, "addNewAccount": {"message": "Добавить новый счет Ethereum"}, "addNewEthereumAccountLabel": {"message": "Счет Ethereum"}, "addNewSolanaAccountLabel": {"message": "Счет Solana"}, "addNft": {"message": "Добавить NFT"}, "addNfts": {"message": "Добавить NFT"}, "addNonEvmAccount": {"message": "Добавить $1 счет", "description": "$1 is the non EVM network where the account is going to be created, e.g. Bitcoin or Solana"}, "addNonEvmAccountFromNetworkPicker": {"message": "Чтобы активировать сеть $1, нужно создать счет $2.", "description": "$1 is the non EVM network where the account is going to be created, e.g. Solana Mainnet or Solana Devnet. $2 is the account type, e.g. Bitcoin or Solana"}, "addRpcUrl": {"message": "Добавить URL-адрес RPC"}, "addSnapAccountToggle": {"message": "Включите «Добавить Snap счета (бета)»"}, "addSnapAccountsDescription": {"message": "Включение этой функции даст вам возможность добавлять новые бета-версии Snaps счетов прямо из списка счетов. Когда вы устанавливаете Snap счета, помните, что это сторонний сервис."}, "addSuggestedNFTs": {"message": "Добавить рекомендованные NFT"}, "addSuggestedTokens": {"message": "Добавить рекомендованные токены"}, "addToken": {"message": "Добавить токен"}, "addTokenByContractAddress": {"message": "Не можете найти токен? Можно вручную добавить любой токен, вставив его адрес. Адреса контракта токена можно найти на $1", "description": "$1 is a blockchain explorer for a specific network, e.g. Etherscan for Ethereum"}, "addUrl": {"message": "Добавить URL-адрес"}, "addingAccount": {"message": "Добавление счет..."}, "addingCustomNetwork": {"message": "Добавление сети"}, "additionalNetworks": {"message": "Дополнительные сети"}, "address": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "addressCopied": {"message": "Адрес скопирован!"}, "addressMismatch": {"message": "Несоответствие адреса сайта"}, "addressMismatchOriginal": {"message": "Текущий URL-адрес: $1", "description": "$1 replaced by origin URL in confirmation request"}, "addressMismatchPunycode": {"message": "Версия пьюникода: $1", "description": "$1 replaced by punycode version of the URL in confirmation request"}, "advanced": {"message": "Дополнительно"}, "advancedBaseGasFeeToolTip": {"message": "После включения вашей транзакции в блок возмещается любая разница между вашей максимальной базовой комиссией и фактической базовой комиссией. Общая сумма рассчитывается следующим образом: максимальная базовая комиссия (в Гвей) x лимит газа."}, "advancedDetailsDataDesc": {"message": "Данные"}, "advancedDetailsHexDesc": {"message": "Шестнадцатиричные"}, "advancedDetailsNonceDesc": {"message": "Одноразовый код"}, "advancedDetailsNonceTooltip": {"message": "Это номер транзакции счета. Одноразовый код для первой транзакции равен 0 и увеличивается в последовательном порядке."}, "advancedGasFeeDefaultOptIn": {"message": "Сохраните эти значения как значения по умолчанию для сети $1.", "description": "$1 is the current network name."}, "advancedGasFeeModalTitle": {"message": "Дополнительная плата за газ"}, "advancedGasPriceTitle": {"message": "Цена газа"}, "advancedPriorityFeeToolTip": {"message": "Плата за приоритет (также известная как «чаевые» майнеру) направляется непосредственно майнерам, чтобы они уделили приоритетное внимание вашей транзакции."}, "airDropPatternDescription": {"message": "В истории токена в блокчейне уже были случаи подозрительных аирдропов."}, "airDropPatternTitle": {"message": "Схема действий при аирдропе"}, "airgapVault": {"message": "AirGap Vault"}, "alert": {"message": "Оповещение"}, "alertAccountTypeUpgradeMessage": {"message": "Вы обновляете свой счет до смарт-счета. Вы сохраните тот же адрес счета, одновременно получив доступ к более быстрым транзакциям и более низким комиссиям сети. $1."}, "alertAccountTypeUpgradeTitle": {"message": "Тип счета"}, "alertActionBuyWithNativeCurrency": {"message": "Купить $1"}, "alertActionUpdateGas": {"message": "Обновить лимит газа"}, "alertActionUpdateGasFee": {"message": "Изменить комиссию"}, "alertActionUpdateGasFeeLevel": {"message": "Обновить параметры газа"}, "alertDisableTooltip": {"message": "Это можно изменить в разделе «Настройки» > «Оповещения»"}, "alertMessageAddressMismatchWarning": {"message": "Злоумышленники иногда имитируют сайты, внося небольшие изменения в адрес сайта. Прежде чем продолжить, убедитесь, что вы взаимодействуете с нужным сайтом."}, "alertMessageChangeInSimulationResults": {"message": "Предполагаемые изменения для этой транзакции обновлены. Прежде чем продолжить, внимательно просмотрите их."}, "alertMessageFirstTimeInteraction": {"message": "Вы впервые взаимодействуете с этим адресом. Прежде чем продолжить, убедитесь, что он верный."}, "alertMessageGasEstimateFailed": {"message": "Мы не можем указать точную сумму комиссии, и эта оценка может быть высокой. Мы предлагаем вам ввести индивидуальный лимит газа, но существует риск, что транзакция все равно не удастся."}, "alertMessageGasFeeLow": {"message": "При выборе низкой комиссии транзакций будут медленнее, а время ожидания — дольше. Для более быстрых транзакций выберите вариант комиссии «Рыночная» или «Агрессивная»."}, "alertMessageGasTooLow": {"message": "Чтобы продолжить эту транзакцию, вам необходимо увеличить лимит газа до 21 000 или выше."}, "alertMessageInsufficientBalanceWithNativeCurrency": {"message": "У вас на счету недостаточно $1 для оплаты комиссий сети."}, "alertMessageNetworkBusy": {"message": "Цены газа высоки, а оценки менее точны."}, "alertMessageNoGasPrice": {"message": "Мы не сможем продолжить эту транзакцию, пока вы не обновите комиссию вручную."}, "alertMessageSignInDomainMismatch": {"message": "Сайт, отправляющий запрос, не является сайтом, на который вы входите. Это может быть попыткой украсть ваши учетные данные."}, "alertMessageSignInWrongAccount": {"message": "Этот сайт просит вас войти в систему, используя неправильный счет."}, "alertModalAcknowledge": {"message": "Я осознал(-а) риск и все еще хочу продолжить"}, "alertModalDetails": {"message": "Сведения об оповещении"}, "alertModalReviewAllAlerts": {"message": "Просмотреть все оповещения"}, "alertReasonChangeInSimulationResults": {"message": "Результаты изменились"}, "alertReasonFirstTimeInteraction": {"message": "1-е взаимодействие"}, "alertReasonGasEstimateFailed": {"message": "Неточная комиссия"}, "alertReasonGasFeeLow": {"message": "Низкая скорость"}, "alertReasonGasTooLow": {"message": "Низкий лимит газа"}, "alertReasonInsufficientBalance": {"message": "Недостаточно средств"}, "alertReasonNetworkBusy": {"message": "Сеть занята"}, "alertReasonNoGasPrice": {"message": "Оценка комиссии недоступна"}, "alertReasonPendingTransactions": {"message": "Ожидается транзакция"}, "alertReasonSignIn": {"message": "Подозрительный запрос на вход"}, "alertReasonWrongAccount": {"message": "Неверный счет"}, "alertSelectedAccountWarning": {"message": "Этот запрос предназначен для другого счета, отличного от того, который выбран в Вашем кошельке. Чтобы использовать другой счет, подключите его к сайту."}, "alerts": {"message": "Оповещения"}, "all": {"message": "Все"}, "allNetworks": {"message": "Все сети"}, "allPermissions": {"message": "Все разрешения"}, "allTimeHigh": {"message": "Максимум за все время"}, "allTimeLow": {"message": "Минимум за все время"}, "allowNotifications": {"message": "Разрешить уведомления"}, "allowWithdrawAndSpend": {"message": "Разрешить $1 снять и потратить до следующей суммы:", "description": "The url of the site that requested permission to 'withdraw and spend'"}, "amount": {"message": "Сумма"}, "amountReceived": {"message": "Полученная сумма"}, "amountSent": {"message": "Отправленная сумма"}, "andForListItems": {"message": "$1, и $2", "description": "$1 is the first item, $2 is the last item in a list of items. Used in Snap Install Warning modal."}, "andForTwoItems": {"message": "$1 и $2", "description": "$1 is the first item, $2 is the second item. Used in Snap Install Warning modal."}, "appDescription": {"message": "Самый надежный криптокошелек в мире", "description": "The description of the application"}, "appName": {"message": "NeoNix", "description": "The name of the application"}, "appNameBeta": {"message": "Бета-версия NeoNix", "description": "The name of the application (Beta)"}, "appNameFlask": {"message": "NeoNix Flask", "description": "The name of the application (Flask)"}, "apply": {"message": "Применить"}, "approve": {"message": "Одобрить лимит расходов"}, "approveButtonText": {"message": "Одобрить"}, "approveIncreaseAllowance": {"message": "Увеличить лимит расходов $1", "description": "The token symbol that is being approved"}, "approveSpendingCap": {"message": "Одобрить лимит расходов в размере $1", "description": "The token symbol that is being approved"}, "approved": {"message": "Одобрен"}, "approvedOn": {"message": "Одобрено $1", "description": "$1 is the approval date for a permission"}, "approvedOnForAccounts": {"message": "Одобрено $1 для $2", "description": "$1 is the approval date for a permission. $2 is the AvatarGroup component displaying account images."}, "areYouSure": {"message": "Вы уверены?"}, "asset": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "assetChartNoHistoricalPrices": {"message": "Мы не смогли получить никаких исторических данных"}, "assetMultipleNFTsBalance": {"message": "$1 NFT"}, "assetOptions": {"message": "Параметры актива"}, "assetSingleNFTBalance": {"message": "$1 NFT"}, "assets": {"message": "Активы"}, "assetsDescription": {"message": "Автоматически обнаруживайте токены в своем кошельке, отображайте NFT и получайте пакетные обновления баланса счета"}, "attemptToCancelSwapForFree": {"message": "Бесплатная попытка отменить своп"}, "attributes": {"message": "Атрибуты"}, "attributions": {"message": "Атрибуции"}, "auroraRpcDeprecationMessage": {"message": "URL-адрес Infura RPC больше не поддерживает Aurora."}, "authorizedPermissions": {"message": "Вы предоставили следующие разрешения"}, "autoDetectTokens": {"message": "Автоопределение токенов"}, "autoDetectTokensDescription": {"message": "Мы используем сторонние API для определения и отображения новых токенов, отправленных в ваш кошелек. Отключите, если не хотите, чтобы приложение извлекало данные из этих служб. $1", "description": "$1 is a link to a support article"}, "autoLockTimeLimit": {"message": "Таймер автоблокировки (минуты)"}, "autoLockTimeLimitDescription": {"message": "Установите время бездействия в минутах, прежде чем NeoNix будет заблокирован."}, "average": {"message": "Средний"}, "back": {"message": "Назад"}, "backupAndSync": {"message": "Резервное копирование и синхронизация"}, "backupAndSyncBasicFunctionalityNameMention": {"message": "базовый функционал"}, "backupAndSyncEnable": {"message": "Включить резервное копирование и синхронизацию"}, "backupAndSyncEnableConfirmation": {"message": "Когда вы включаете резервное копирование и синхронизацию, вы также включаете $1. Хотите продолжить?", "description": "$1 is backupAndSyncBasicFunctionalityNameMention in bold."}, "backupAndSyncEnableDescription": {"message": "Резервное копирование и синхронизация позволяют нам хранить зашифрованные данные для ваших пользовательских настроек и функций. Это позволяет вам сохранять один и тот же опыт испоользования NeoNix на всех устройствах и восстанавливать настройки и функции, если вам когда-либо понадобится переустановить NeoNix. Эта функция не выполняет резервное копирование вашей секретной фразы для восстановления. $1.", "description": "$1 is link to the backup and sync privacy policy."}, "backupAndSyncEnableDescriptionUpdatePreferences": {"message": "Вы можете обновить свои параметры в любое время в разделе $1", "description": "$1 is a bolded text that highlights the path to the settings page."}, "backupAndSyncEnableDescriptionUpdatePreferencesPath": {"message": "Настройки > Резервное копирование и синхронизация."}, "backupAndSyncFeatureAccounts": {"message": "Счета"}, "backupAndSyncManageWhatYouSync": {"message": "Управляйте тем, что вы синхронизируете"}, "backupAndSyncManageWhatYouSyncDescription": {"message": "Включите то, что синхронизируетмя между вашими устройствами."}, "backupAndSyncPrivacyLink": {"message": "Узнайте, как мы защищаем вашу конфиденциальность"}, "backupAndSyncSlideDescription": {"message": "Создавайте резервные копии своих счетов и синхронизируйте настройки."}, "backupAndSyncSlideTitle": {"message": "Представляем резервное копирование и синхронизацию"}, "backupApprovalInfo": {"message": "Этот секретный код необходим для восстановления вашего кошелька в случае, если вы потеряете свое устройство, забудете пароль, вам придется переустановить NeoNix или захотите получить доступ к своему кошельку на другом устройстве."}, "backupApprovalNotice": {"message": "Создайте резервную копию своей секретной фразы для восстановления, чтобы обезопасить свой кошелек и средства."}, "backupKeyringSnapReminder": {"message": "Прежде чем удалять его, убедитесь, что у вас есть доступ к счетам, созданным с помощью этого Snap"}, "backupNow": {"message": "Создать резервную копию сейчас"}, "balance": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "balanceOutdated": {"message": "Баланс мог устареть"}, "baseFee": {"message": "Базовая комиссия"}, "basic": {"message": "Базовый"}, "basicConfigurationBannerTitle": {"message": "Базовый функционал отключен"}, "basicConfigurationDescription": {"message": "NeoNix предлагает базовые функции, такие как сведения о токенах и настройки газа, через интернет-службы. Когда вы пользуетесь интернет-службами, ваш IP-адрес передается, в данном случае NeoNix. Это похоже на посещенеи вами любого веб-сайта. NeoNix временно использует ваши данные и никогда не продает их. Вы можете использовать VPN или отключить эти службы, но это может негативно повлиять на вашу работу с NeoNix. Чтобы узнать больше, прочитайте нашу $1.", "description": "$1 is to be replaced by the message for privacyMsg, and will link to https://nnxscan.io"}, "basicConfigurationLabel": {"message": "Базовый функционал"}, "basicConfigurationModalCheckbox": {"message": "Я понимаю и хочу продолжить"}, "basicConfigurationModalDisclaimerOff": {"message": "Это означает, что вы не сможете полностью оптимизировать свое время в NeoNix. Вам будут недоступны базовые функции (например, сведения о токенах, оптимальные настройки газа и т. д.)."}, "basicConfigurationModalDisclaimerOffAdditionalText": {"message": "Отключение этого параметра также отключает все функции в разделах $1 и $2.", "description": "$1 and $2 are bold text for basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst and basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast respectively"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst": {"message": "безопасность и конфиденциальность, резервное копирование и синхронизация"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast": {"message": "уведомления"}, "basicConfigurationModalDisclaimerOn": {"message": "Чтобы оптимизировать свое время в NeoNix, вам необходимо включить эту функцию. Базовые функции (например, сведения о токенах, оптимальные настройки газа и другие) важны для работы с web3."}, "basicConfigurationModalHeadingOff": {"message": "Выключить базовый функционал"}, "basicConfigurationModalHeadingOn": {"message": "Включить базовый функционал"}, "bestPrice": {"message": "Лучшая цена"}, "beta": {"message": "Бета-версия"}, "betaHeaderText": {"message": "Это бета-версия. Пожалуйста, сообщайте об ошибках $1"}, "betaNeoNixVersion": {"message": "Бета-версия NeoNix"}, "betaTerms": {"message": "Условия использования бета-версии"}, "billionAbbreviation": {"message": "Б", "description": "Shortened form of 'billion'"}, "blockExplorerAccountAction": {"message": "Счет", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Account in Explorer"}, "blockExplorerAssetAction": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Asset in Explorer"}, "blockExplorerSwapAction": {"message": "Своп", "description": "This is used with viewOnEtherscan e.g View Swap on Etherscan"}, "blockExplorerUrl": {"message": "URL обозревателя блоков"}, "blockExplorerUrlDefinition": {"message": "URL, используемый как обозреватель блоков для этой сети."}, "blockExplorerView": {"message": "Посмотреть счет в $1", "description": "$1 replaced by URL for custom block explorer"}, "blockaid": {"message": "Blockaid"}, "blockaidAlertDescriptionBlur": {"message": "Если вы продолжите, все активы, которые вы разместили на Blur, могут оказаться под угрозой."}, "blockaidAlertDescriptionMalicious": {"message": "Вы взаимодействуете с вредоносным сайтом. Если вы продолжите, вы потеряете свои активы."}, "blockaidAlertDescriptionOpenSea": {"message": "Если вы продолжите, все активы, которые вы разместили на OpenSea, могут оказаться под угрозой."}, "blockaidAlertDescriptionOthers": {"message": "Если вы подтвердите этот запрос, вы можете потерять свои активы. Мы рекомендуем вам отменить запрос."}, "blockaidAlertDescriptionTokenTransfer": {"message": "Вы отправляете свои активы мошеннику. Если вы продолжите, вы потеряете эти активы."}, "blockaidAlertDescriptionWithdraw": {"message": "Если вы подтвердите этот запрос, вы позволите мошеннику снять и потратить ваши активы. Вы не получите их обратно."}, "blockaidDescriptionApproveFarming": {"message": "Если вы одобрите этот запрос, третья сторона, известная мошенничеством, может похитить все ваши активы."}, "blockaidDescriptionBlurFarming": {"message": "Если вы одобрите этот запрос, кто-то может украсть ваши активы, указанные в Blur."}, "blockaidDescriptionErrored": {"message": "Из-за ошибки этот запрос не был подтвержден поставщиком услуг безопасности. Действуйте осторожно."}, "blockaidDescriptionMaliciousDomain": {"message": "Вы взаимодействуете с вредоносным доменом. Если вы одобрите этот запрос, вы можете потерять свои активы."}, "blockaidDescriptionMightLoseAssets": {"message": "Если вы одобрите этот запрос, вы можете потерять свои активы."}, "blockaidDescriptionSeaportFarming": {"message": "Если вы одобрите этот запрос, кто-то может украсть ваши активы, указанные в OpenSea."}, "blockaidDescriptionTransferFarming": {"message": "Если вы одобрите этот запрос, третья сторона, известная мошенничеством, похитит все ваши активы."}, "blockaidMessage": {"message": "Сохранение конфиденциальности – никакие данные не передаются третьим сторонам. Доступно в Arbitrum, Avalanche, BNB Chain, Мейн-нете Ethereum, Linea, Optimism, Polygon, Base и Sepolia."}, "blockaidTitleDeceptive": {"message": "Это запрос с целью обмана"}, "blockaidTitleMayNotBeSafe": {"message": "Будьте осторожны"}, "blockaidTitleSuspicious": {"message": "Это подозрительный запрос"}, "blockies": {"message": "Blockies"}, "borrowed": {"message": "Заимствовано"}, "boughtFor": {"message": "Куплено за"}, "bridge": {"message": "Мост"}, "bridgeAllowSwappingOf": {"message": "Разрешить точный доступ к $1, $2 и $3 для создания моста", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "bridgeApproval": {"message": "Одобрить использование $1 для моста", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be bridged. $1 is the symbol of a token that has been approved."}, "bridgeApprovalWarning": {"message": "Вы разрешаете доступ к указанной сумме: $1 $2. Контракт не предусматривает доступа к каким-либо дополнительным средствам."}, "bridgeApprovalWarningForHardware": {"message": "Вам нужно будет разрешить доступ к $1 $2 для создания моста, а затем утвердить создание моста к $2. Для этого потребуется два отдельных подтверждения."}, "bridgeBlockExplorerLinkCopied": {"message": "Ссылка обозревателя блоков скопирована!"}, "bridgeCalculatingAmount": {"message": "Расчет..."}, "bridgeConfirmTwoTransactions": {"message": "Вам нужно будет подтвердить 2 транзакции в Вашем аппаратном кошельке:"}, "bridgeCreateSolanaAccount": {"message": "Создать счет Solana"}, "bridgeCreateSolanaAccountDescription": {"message": "Для выполнения свопа в сеть Solana понадобится счет и принимающий адрес."}, "bridgeCreateSolanaAccountTitle": {"message": "Сначала вам понадобится счет Solana."}, "bridgeDetailsTitle": {"message": "Сведения о мосте", "description": "Title for the modal showing details about a bridge transaction."}, "bridgeEnterAmount": {"message": "Выберите сумму"}, "bridgeEnterAmountAndSelectAccount": {"message": "Введите сумму и выберите целевой счет"}, "bridgeExplorerLinkViewOn": {"message": "Смотреть на $1"}, "bridgeFetchNewQuotes": {"message": "Получить новый?"}, "bridgeFrom": {"message": "Мост из"}, "bridgeFromTo": {"message": "Создать мост для $1 из $2 к $3", "description": "Tells a user that they need to confirm on their hardware wallet a bridge. $1 is amount of source token, $2 is the source network, and $3 is the destination network"}, "bridgeGasFeesSplit": {"message": "Любая комиссия сети, указанная на предыдущем экране, включает обе транзакции и будет разделена."}, "bridgeNetCost": {"message": "Чистая стоимость"}, "bridgeQuoteExpired": {"message": "Срок действия Вашей котировки истек."}, "bridgeSelectDestinationAccount": {"message": "Выберите целевой счет"}, "bridgeSelectNetwork": {"message": "Выбор сети"}, "bridgeSelectTokenAmountAndAccount": {"message": "Выберите токен, сумму и целевой счет"}, "bridgeSelectTokenAndAmount": {"message": "Выберите токен и сумму"}, "bridgeSolanaAccountCreated": {"message": "Создан счет Solana"}, "bridgeStatusComplete": {"message": "Завершено", "description": "Status text indicating a bridge transaction has successfully completed."}, "bridgeStatusFailed": {"message": "Не удалось", "description": "Status text indicating a bridge transaction has failed."}, "bridgeStatusInProgress": {"message": "В процессе", "description": "Status text indicating a bridge transaction is currently processing."}, "bridgeStepActionBridgeComplete": {"message": "$1 получено в $2", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionBridgePending": {"message": "Получение $1 в $2", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionSwapComplete": {"message": "Обменяно $1 на $2", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeStepActionSwapPending": {"message": "Обменивается $1 на $2", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeTerms": {"message": "Условия"}, "bridgeTimingMinutes": {"message": "$1 мин.", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "bridgeTo": {"message": "Мост в"}, "bridgeToChain": {"message": "Мост к $1"}, "bridgeTokenCannotVerifyDescription": {"message": "Если вы добавили этот токен вручную. Убедитесь, что вы осознаете риски для своих средств, прежде чем использовать мост."}, "bridgeTokenCannotVerifyTitle": {"message": "Мы не можем проверить этот токен."}, "bridgeTransactionProgress": {"message": "Транзакция $1 из 2"}, "bridgeTxDetailsBridging": {"message": "Создание моста"}, "bridgeTxDetailsDelayedDescription": {"message": "Обратитесь в"}, "bridgeTxDetailsDelayedDescriptionSupport": {"message": "Поддержка NeoNix"}, "bridgeTxDetailsDelayedTitle": {"message": "Прошло больше 3 часов?"}, "bridgeTxDetailsNonce": {"message": "Одноразовый код"}, "bridgeTxDetailsStatus": {"message": "Статус"}, "bridgeTxDetailsTimestamp": {"message": "Отметка времени"}, "bridgeTxDetailsTimestampValue": {"message": "$1 в $2", "description": "$1 is the date, $2 is the time"}, "bridgeTxDetailsTokenAmountOnChain": {"message": "$1 $2 на", "description": "$1 is the amount of the token, $2 is the ticker symbol of the token"}, "bridgeTxDetailsTotalGasFee": {"message": "Итого платы за газ"}, "bridgeTxDetailsYouReceived": {"message": "Вы получили"}, "bridgeTxDetailsYouSent": {"message": "Вы отправили"}, "bridgeValidationInsufficientGasMessage": {"message": "У Вас недостаточно $1 для покрытия платы за газ для этого моста. Введите меньшую сумму или купите больше $1."}, "bridgeValidationInsufficientGasTitle": {"message": "Для оплаты газа нужно еще $1"}, "bridging": {"message": "Создание моста"}, "browserNotSupported": {"message": "Ваш браузер не поддерживается..."}, "buildContactList": {"message": "Создайте список контактов"}, "builtAroundTheWorld": {"message": "NeoNix разработан и создан с учетом потребностей мира."}, "bulletpoint": {"message": "·"}, "busy": {"message": "Занят"}, "buyAndSell": {"message": "Купить/продать"}, "buyMoreAsset": {"message": "Купить еще $1", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "buyNow": {"message": "Купить сейчас"}, "bytes": {"message": "<PERSON>а<PERSON><PERSON>ы"}, "canToggleInSettings": {"message": "Вы можете повторно включить это уведомление в разделе «Настройки» -> «Оповещения»."}, "cancel": {"message": "Отмена"}, "cancelPopoverTitle": {"message": "Отменить транзакцию"}, "cancelSpeedUpLabel": {"message": "Эта плата за газ составит $1 от первоначальной.", "description": "$1 is text 'replace' in bold"}, "cancelSpeedUpTransactionTooltip": {"message": "Чтобы $1 транзакции плата за газ должна быть увеличена как минимум на 10%. Это позволит обеспечить прием транзакции сетью.", "description": "$1 is string 'cancel' or 'speed up'"}, "cancelled": {"message": "Отменено"}, "chainId": {"message": "ID блокчейна"}, "chainIdDefinition": {"message": "ID блокчейна, используемый для подписания транзакций для этой сети."}, "chainIdExistsErrorMsg": {"message": "Этот ID блокчейна в настоящее время используется сетью $1."}, "chainListReturnedDifferentTickerSymbol": {"message": "Этот символ токена не соответствует введенному имени сети или идентификатору блокчейна. Многие популярные токены обозначаются похожими символами, которые мошенники могут использовать, чтобы обманом заставить вас отправить им взамен более ценный токен. Проверьте все, прежде чем продолжить."}, "chooseYourNetwork": {"message": "Выберите свою сеть"}, "chooseYourNetworkDescription": {"message": "Когда вы используете наши настройки и конфигурации по умолчанию, мы используем Infura в качестве нашего поставщика удаленного вызова процедур (RPC) по умолчанию, чтобы предложить наиболее надежный и конфиденциальный доступ к данным Ethereum, который мы можем предложить. В ограниченных случаях мы можем использовать других поставщиков RPC, чтобы обеспечить наилучший опыт для наших пользователей. Вы можете выбрать свой собственный RPC, но помните, что любой RPC получит ваш IP-адрес и кошелек Ethereum для совершения транзакций. Чтобы узнать больше о том, как Infura обрабатывает данные для учетных записей EVM, прочтите нашу статью $1, а для счетов Solana — статью $2.", "description": "$1 is a link to the privacy policy, $2 is a link to Solana accounts support"}, "chooseYourNetworkDescriptionCallToAction": {"message": "нажмите здесь"}, "chromeRequiredForHardwareWallets": {"message": "Вам необходимо использовать NeoNix в Google Chrome, чтобы подключиться к аппаратному кошельку."}, "circulatingSupply": {"message": "Циркулирующее предложение"}, "clear": {"message": "Очистить"}, "clearActivity": {"message": "Очистить данные о действиях и одноразовых номерах"}, "clearActivityButton": {"message": "Очистить данные вкладки активности"}, "clearActivityDescription": {"message": "Это приведет к сбросу одноразового номера для счета и удалению данных со вкладки \"Активность\" в вашем кошельке. Это затронет только текущий счет и сеть. Ваши балансы и входящие транзакции не изменятся."}, "click": {"message": "Нажмите"}, "clickToConnectLedgerViaWebHID": {"message": "Нажмите здесь, чтобы подключить свой Ledger через WebHID", "description": "Text that can be clicked to open a browser popup for connecting the ledger device via webhid"}, "close": {"message": "Закрыть"}, "closeExtension": {"message": "Закрыть расширение"}, "closeWindowAnytime": {"message": "Вы можете закрыть это окно в любое время."}, "coingecko": {"message": "CoinGecko"}, "collectionName": {"message": "Имя коллекции"}, "comboNoOptions": {"message": "Вариантов не найдено", "description": "Default text shown in the combo field dropdown if no options."}, "concentratedSupplyDistributionDescription": {"message": "Большая часть запаса токенов принадлежит их крупнейшим держателям, что создает риск централизованного манипулирования ценой"}, "concentratedSupplyDistributionTitle": {"message": "Концентрированное распределение запаса"}, "configureSnapPopupDescription": {"message": "Теперь вы покидаете NeoNix, чтобы настроить этот snap."}, "configureSnapPopupInstallDescription": {"message": "Теперь вы покидаете NeoNix, чтобы установить этот snap."}, "configureSnapPopupInstallTitle": {"message": "Установить snap"}, "configureSnapPopupLink": {"message": "Нажмите на эту ссылку, чтобы продолжить:"}, "configureSnapPopupTitle": {"message": "Настроить snap"}, "confirm": {"message": "Подтвердить"}, "confirmAccountTypeSmartContract": {"message": "Смарт-счет"}, "confirmAccountTypeStandard": {"message": "Стандартный счет"}, "confirmAlertModalAcknowledgeMultiple": {"message": "Я подтвердил(-а) получение оповещений и все еще хочу продолжить"}, "confirmAlertModalAcknowledgeSingle": {"message": "Я подтвердил(-а) получение предупреждения и все еще хочу продолжить"}, "confirmFieldPaymaster": {"message": "Комиссия оплачена"}, "confirmFieldTooltipPaymaster": {"message": "Комиссия за эту транзакцию будет оплачена смарт-контрактом paymaster."}, "confirmGasFeeTokenBalance": {"message": "Баланс:"}, "confirmGasFeeTokenInsufficientBalance": {"message": "Недостаточно средств"}, "confirmGasFeeTokenNeoNixFee": {"message": "Включает комиссию $1"}, "confirmGasFeeTokenModalNativeToggleNeoNix": {"message": "NeoNix пополняет баланс для завершения этой транзакции."}, "confirmGasFeeTokenModalNativeToggleWallet": {"message": "Оплатить комиссию сети с баланса вашего кошелька."}, "confirmGasFeeTokenModalPayETH": {"message": "Оплатить в ETH"}, "confirmGasFeeTokenModalPayToken": {"message": "Оплатить в других токенах"}, "confirmGasFeeTokenModalTitle": {"message": "Выбрать токен"}, "confirmGasFeeTokenToast": {"message": "Вы оплачиваете эту сетевую комиссию с помощью $1"}, "confirmGasFeeTokenTooltip": {"message": "Эта сумма выплачивается сети за обработку вашей транзакции. Включает комиссию NeoNix в размере $1 за токены, не относящиеся к ETH, или предоплаченные ETH."}, "confirmInfoAccountNow": {"message": "Сей<PERSON><PERSON>с"}, "confirmInfoSwitchingTo": {"message": "Переключение на"}, "confirmNestedTransactionTitle": {"message": "Транзакция $1"}, "confirmPassword": {"message": "Подтвердить пароль"}, "confirmRecoveryPhrase": {"message": "Подтвердите секретную фразу для восстановления"}, "confirmSimulationApprove": {"message": "Вы одобряете"}, "confirmTitleAccountTypeSwitch": {"message": "Обновление счета"}, "confirmTitleApproveTransactionNFT": {"message": "Запрос на вывод средств"}, "confirmTitleDeployContract": {"message": "Развернуть контракт"}, "confirmTitleDescApproveTransaction": {"message": "Этот сайт хочет получить разрешение на вывод ваших NFT"}, "confirmTitleDescDelegationRevoke": {"message": "Вы переключаетесь обратно на стандартный счет (EOA)."}, "confirmTitleDescDelegationUpgrade": {"message": "Вы переходите на смарт-счет"}, "confirmTitleDescDeployContract": {"message": "Этот сайт хочет, чтобы вы развернули контракт"}, "confirmTitleDescERC20ApproveTransaction": {"message": "Этот сайт хочет получить разрешение на вывод ваших токенов"}, "confirmTitleDescPermitSignature": {"message": "Этот сайт хочет получить разрешение на расходование ваших токенов."}, "confirmTitleDescSIWESignature": {"message": "Сайт требует, чтобы вы вошли в систему и доказали, что вы являетесь владельцем этого счета."}, "confirmTitleDescSign": {"message": "Прежде чем подтвердить запрос, проверьте его реквизиты."}, "confirmTitlePermitTokens": {"message": "Запрос лимита расходов"}, "confirmTitleRevokeApproveTransaction": {"message": "Удалить разрешение"}, "confirmTitleSIWESignature": {"message": "Запрос на вход"}, "confirmTitleSetApprovalForAllRevokeTransaction": {"message": "Удалить разрешение"}, "confirmTitleSignature": {"message": "Запрос подписи"}, "confirmTitleTransaction": {"message": "Запрос транзакции"}, "confirmationAlertDetails": {"message": "Чтобы защитить свои активы, рекомендуем отклонить запрос."}, "confirmationAlertModalTitleDescription": {"message": "Ваши активы могут быть в опасности"}, "confirmed": {"message": "Подтвержден(-а/о)"}, "confusableUnicode": {"message": "«$1» совпадает с «$2»."}, "confusableZeroWidthUnicode": {"message": "Найден символ нулевой ширины."}, "confusingEnsDomain": {"message": "В имени ENS обнаружен непонятный символ. Проверьте это имя, что исключить возможное мошенничество."}, "connect": {"message": "Подключиться"}, "connectAccount": {"message": "Подключить счет"}, "connectAccountOrCreate": {"message": "Подключите счет или создайте новый"}, "connectAccounts": {"message": "Подключить счета"}, "connectAnAccountHeader": {"message": "Подключить счет"}, "connectManually": {"message": "Подключиться к текущему сайту вручную"}, "connectMoreAccounts": {"message": "Подключить еще счета"}, "connectSnap": {"message": "Подключиться к $1", "description": "$1 is the snap for which a connection is being requested."}, "connectWithNeoNix": {"message": "Подключиться с помощью NeoNix"}, "connectedAccounts": {"message": "Подключенные счета"}, "connectedAccountsDescriptionPlural": {"message": "К этому сайту подключено $1 ваших счета(-ов).", "description": "$1 is the number of accounts"}, "connectedAccountsDescriptionSingular": {"message": "К этому сайту подключен 1 ваш счет."}, "connectedAccountsEmptyDescription": {"message": "NeoNix не подключен к этому сайту. Чтобы подключиться к сайту web3, найдите и нажмите кнопку подключения."}, "connectedAccountsListTooltip": {"message": "$1 может видеть баланс счета, ад<PERSON><PERSON><PERSON>, активность и предлагать транзакции для одобрения для подключенных счетов.", "description": "$1 is the origin name"}, "connectedAccountsToast": {"message": "Подключенные счета обновлены"}, "connectedSites": {"message": "Подключенные сайты"}, "connectedSitesAndSnaps": {"message": "Подключенные сайты и Snaps"}, "connectedSitesDescription": {"message": "$1 подключен к этим сайтам. Они могут увидеть адрес вашего счета.", "description": "$1 is the account name"}, "connectedSitesEmptyDescription": {"message": "$1 не подключен ни к каким сайтам.", "description": "$1 is the account name"}, "connectedSnapAndNoAccountDescription": {"message": "NeoNix подключен к этому сайту, но счета пока не подключены"}, "connectedSnaps": {"message": "Подключенные Snaps"}, "connectedWithAccount": {"message": "Счета ($1) не подключены", "description": "$1 represents account length"}, "connectedWithAccountName": {"message": "Подключен к $1", "description": "$1 represents account name"}, "connectedWithNetwork": {"message": "Подключены $1 сети(-ей)", "description": "$1 represents network length"}, "connectedWithNetworkName": {"message": "Подключено с помощью $1", "description": "$1 represents network name"}, "connecting": {"message": "Подключение..."}, "connectingTo": {"message": "Подключение к $1"}, "connectingToDeprecatedNetwork": {"message": "«$1» постепенно выводится из эксплуатации и может не работать. Попробуйте другую сеть."}, "connectingToGoerli": {"message": "Подключение к тестовой сети Goerli..."}, "connectingToLineaGoerli": {"message": "Подключение к тестовой сети Linea Goerli..."}, "connectingToLineaMainnet": {"message": "Подключение к Мейн-нету Linea"}, "connectingToLineaSepolia": {"message": "Подключение к тестовой сети Linea Sepolia..."}, "connectingToMainnet": {"message": "Подключение к Мейн-нету Ethereum..."}, "connectingToSepolia": {"message": "Подключение к тестовой сети Sepolia..."}, "connectionDescription": {"message": "Подключить этот сайт к NeoNix"}, "connectionFailed": {"message": "Не удалось установить подключение"}, "connectionFailedDescription": {"message": "Не удалось получить $1, проверьте свою сеть и повторите попытку.", "description": "$1 is the name of the snap being fetched."}, "connectionPopoverDescription": {"message": "Для подключения к сайту нажмите кнопку подключения. NeoNix может подключаться только к сайтам web3."}, "connectionRequest": {"message": "Запрос подключения"}, "contactUs": {"message": "Свяжитесь с нами"}, "contacts": {"message": "Контактная информация"}, "contentFromSnap": {"message": "Контент из $1", "description": "$1 represents the name of the snap"}, "continue": {"message": "Продолжить"}, "contract": {"message": "Контракт"}, "contractAddress": {"message": "Адрес контракта"}, "contractAddressError": {"message": "Вы отправляете токены на адрес контракта токена. Это может привести к потере токенов."}, "contractDeployment": {"message": "Развертывание контракта"}, "contractInteraction": {"message": "Взаимодействие по контракту"}, "convertTokenToNFTDescription": {"message": "Мы обнаружили, что этот актив является NFT. NeoNix теперь имеет полную встроенную поддержку NFT. Хотите удалить его из списка токенов и добавить в качестве NFT?"}, "convertTokenToNFTExistDescription": {"message": "Мы обнаруж<PERSON><PERSON><PERSON>, что этот актив был добавлен как NFT. Хотите удалить его из списка токенов?"}, "coolWallet": {"message": "CoolWallet"}, "copiedExclamation": {"message": "Скопировано."}, "copyAddress": {"message": "Скопировать адрес в буфер обмена"}, "copyAddressShort": {"message": "Копировать адрес"}, "copyPrivateKey": {"message": "Копировать закрытый ключ"}, "copyToClipboard": {"message": "Скопировать в буфер обмена"}, "copyTransactionId": {"message": "Скопировать ID транзакции"}, "create": {"message": "Создать"}, "createNewAccountHeader": {"message": "Создать новый счет"}, "createPassword": {"message": "Создать пароль"}, "createSnapAccountDescription": {"message": "$1 хочет добавить новый счет в NeoNix."}, "createSnapAccountTitle": {"message": "Создать счет"}, "createSolanaAccount": {"message": "Создать счет Solana"}, "creatorAddress": {"message": "Адрес автора"}, "crossChainSwapsLink": {"message": "Меняйте сети с помощью NeoNix Portfolio"}, "crossChainSwapsLinkNative": {"message": "Переключайтесь между сетями с помощью моста"}, "cryptoCompare": {"message": "CryptoCompare"}, "currencyConversion": {"message": "Обмен валюты"}, "currencyRateCheckToggle": {"message": "Показать средство проверки баланса и цены токена"}, "currencyRateCheckToggleDescription": {"message": "Мы используем API $1 и $2 для отображения вашего баланса и цены токена. $3", "description": "$1 represents Coingecko, $2 represents CryptoCompare and $3 represents Privacy Policy"}, "currencySymbol": {"message": "Символ валюты"}, "currencySymbolDefinition": {"message": "Тике<PERSON>, который отображается для валюты этой сети."}, "currentAccountNotConnected": {"message": "Ваша текущий счет не подключен"}, "currentExtension": {"message": "Страница текущего расширения"}, "currentLanguage": {"message": "Текущий язык"}, "currentNetwork": {"message": "Текущая сеть", "description": "Speicifies to token network filter to filter by current Network. Will render when network nickname is not available"}, "currentRpcUrlDeprecated": {"message": "Текущий URL rpc для этой сети устарел."}, "currentTitle": {"message": "Текущий:"}, "currentlyUnavailable": {"message": "Недоступно в этой сети"}, "curveHighGasEstimate": {"message": "График агрессивной оценки газа"}, "curveLowGasEstimate": {"message": "График низкой оценки газа"}, "curveMediumGasEstimate": {"message": "График рыночной оценки газа"}, "custom": {"message": "Дополнительно"}, "customGasSettingToolTipMessage": {"message": "Использовать $1, чтобы настроить цену на газ. Это может сбивать с толку, если вы не знакомы с этим. Взаимодействуйте на свой страх и риск.", "description": "$1 is key 'advanced' (text: 'Advanced') separated here so that it can be passed in with bold font-weight"}, "customSlippage": {"message": "Пользовательский"}, "customSpendLimit": {"message": "Пользовательский лимит расходов"}, "customToken": {"message": "Пользовательский токен"}, "customTokenWarningInNonTokenDetectionNetwork": {"message": "Определение токена пока недоступно в этой сети. Импортируйте токен вручную и убедитесь, что вы ему доверяете. Подробнее о $1"}, "customTokenWarningInTokenDetectionNetwork": {"message": "Кто угодно может создать токен, в том числе поддельные версии существующих токенов. Узнайте подробнее о $1"}, "customTokenWarningInTokenDetectionNetworkWithTDOFF": {"message": "Перед импортом убедитесь, что вы доверяете токену. Узнайте, как избежать $1. Вы также можете включить определение токена $2."}, "customerSupport": {"message": "поддержка клиентов"}, "customizeYourNotifications": {"message": "Настройте свои уведомления"}, "customizeYourNotificationsText": {"message": "Включите типы уведомлений, которые вы хотите получать:"}, "dappSuggested": {"message": "Рекомендовано сайтом"}, "dappSuggestedGasSettingToolTipMessage": {"message": "$1 рекомендовал эту цену.", "description": "$1 is url for the dapp that has suggested gas settings"}, "dappSuggestedHigh": {"message": "Рекомендовано сайтом"}, "dappSuggestedHighShortLabel": {"message": "Сайт (высокий)"}, "dappSuggestedShortLabel": {"message": "Сайт"}, "dappSuggestedTooltip": {"message": "$1 рекомендовал эту цену.", "description": "$1 represents the Dapp's origin"}, "darkTheme": {"message": "Темная"}, "data": {"message": "Данные"}, "dataCollectionForMarketing": {"message": "Сбор данных для маркетинга"}, "dataCollectionForMarketingDescription": {"message": "Мы будем использовать MetaMetrics, чтобы узнать, как вы взаимодействуете с нашими маркетинговыми сообщениями. Мы можем делиться соответствующими новостями (например, новостями о функциях продукта и другими материалами)."}, "dataCollectionWarningPopoverButton": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dataCollectionWarningPopoverDescription": {"message": "Вы отключили сбор данных для наших маркетинговых целей. Это касается только этого устройства. Если вы используете NeoNix на других устройствах, обязательно отключите его и там."}, "dataUnavailable": {"message": "данные недоступны"}, "dateCreated": {"message": "Дата создания"}, "dcent": {"message": "<PERSON><PERSON><PERSON>nt"}, "debitCreditPurchaseOptions": {"message": "Варианты покупки с помощью дебетовой или кредитной карты"}, "decimal": {"message": "Число десятичных знаков токена"}, "decimalsMustZerotoTen": {"message": "Число десятичных знаков должно быть не менее 0, но не более 36."}, "decrypt": {"message": "Расшифровать"}, "decryptCopy": {"message": "Скопировать зашифрованное сообщение"}, "decryptInlineError": {"message": "Это сообщение невозможно расшифровать из-за ошибки: $1", "description": "$1 is error message"}, "decryptMessageNotice": {"message": "$1 необходимо прочитать это сообщение, чтобы завершить свое действие", "description": "$1 is the web3 site name"}, "decryptNeoNix": {"message": "Расшифровать сообщение"}, "decryptRequest": {"message": "Расшифровать запрос"}, "defaultRpcUrl": {"message": "URL-адрес RPC по умолчанию"}, "defaultSettingsSubTitle": {"message": "NeoNix использует настройки по умолчанию, чтобы наилучшим образом сбалансировать безопасность и простоту использования. Измените эти настройки, чтобы еще больше повысить свою конфиденциальность."}, "defaultSettingsTitle": {"message": "Настройки конфиденциальности по умолчанию"}, "defi": {"message": "<PERSON><PERSON><PERSON>"}, "defiTabErrorContent": {"message": "Попробуйте зайти еще раз позже."}, "defiTabErrorTitle": {"message": "Нам не удалось загрузить эту страницу."}, "delete": {"message": "Удалить"}, "deleteContact": {"message": "Удалить контакт"}, "deleteMetaMetricsData": {"message": "Удалить данные MetaMetrics"}, "deleteMetaMetricsDataDescription": {"message": "Это приведет к удалению исторических данных MetaMetrics, связанных с использованием вами этого устройства. Ваш кошелек и счета останутся такими же, как сейчас, после удаления этих данных. Этот процесс может занять до 30 дней. Наша $1 содержит подробные сведения по этому вопросу.", "description": "$1 will have text saying Privacy Policy "}, "deleteMetaMetricsDataErrorDesc": {"message": "Этот запрос нельзя выполнить сейчас из-за проблемы с сервером аналитической системы. Повторите попытку позже"}, "deleteMetaMetricsDataErrorTitle": {"message": "Мы не можем удалить эти данные прямо сейчас"}, "deleteMetaMetricsDataModalDesc": {"message": "Мы собираемся удалить все ваши данные MetaMetrics. Вы уверены?"}, "deleteMetaMetricsDataModalTitle": {"message": "Удалить данные MetaMetrics?"}, "deleteMetaMetricsDataRequestedDescription": {"message": "Вы инициировали это действие $1. Этот процесс может занять до 30 дней. $2 содержит подробности по этому вопросу", "description": "$1 will be the date on which teh deletion is requested and $2 will have text saying Privacy Policy "}, "deleteNetworkIntro": {"message": "Если вы удалите эту сеть, вам нужно будет добавить ее снова, чтобы просмотреть свои активы в этой сети"}, "deleteNetworkTitle": {"message": "Удалить сеть $1?", "description": "$1 represents the name of the network"}, "depositCrypto": {"message": "Внесите криптовалюту с другого счета, используя адрес кошелька или QR-код."}, "deprecatedGoerliNtwrkMsg": {"message": "Из-за обновлений системы Ethereum тестовая сеть Goerli скоро будет закрыта."}, "deprecatedNetwork": {"message": "Эта сеть устарела"}, "deprecatedNetworkButtonMsg": {"message": "Понятно"}, "deprecatedNetworkDescription": {"message": "Сеть, к которой вы пытаетесь подключиться, больше не поддерживается NeoNix. $1"}, "description": {"message": "Описание"}, "descriptionFromSnap": {"message": "Описание из $1", "description": "$1 represents the name of the snap"}, "destinationAccountPickerNoEligible": {"message": "Удовлетворяющие требованиям счета не найдены"}, "destinationAccountPickerNoMatching": {"message": "Подходящие счета не найдены"}, "destinationAccountPickerReceiveAt": {"message": "Получить в"}, "destinationAccountPickerSearchPlaceholderToMainnet": {"message": "Адрес получения или ENS"}, "destinationAccountPickerSearchPlaceholderToSolana": {"message": "Адрес получения"}, "destinationTransactionIdLabel": {"message": "Направление Tx ID", "description": "Label for the destination transaction ID field."}, "details": {"message": "Подробности"}, "developerOptions": {"message": "Параметры разработчика"}, "disabledGasOptionToolTipMessage": {"message": "$1 отключена, поскольку не соответствует минимальному увеличению на 10% от первоначальной платы за газ.", "description": "$1 is gas estimate type which can be market or aggressive"}, "disconnect": {"message": "Отключить"}, "disconnectAllAccounts": {"message": "Отключить все счета"}, "disconnectAllAccountsConfirmationDescription": {"message": "Уверены, что хотите отключить? Вы можете потерять доступ к функциям сайта."}, "disconnectAllAccountsText": {"message": "счета"}, "disconnectAllDescriptionText": {"message": "Если вы отключитесь от этого сайта, вам придется повторно подключить свои счета и сети, чтобы снова использовать этот сайт."}, "disconnectAllSnapsText": {"message": "Snaps"}, "disconnectMessage": {"message": "Это отключит вас от этого сайта"}, "disconnectPrompt": {"message": "Отключить $1"}, "disconnectThisAccount": {"message": "Отключить этот счет"}, "disconnectedAllAccountsToast": {"message": "Все счета отключены от $1", "description": "$1 is name of the dapp`"}, "disconnectedSingleAccountToast": {"message": "$1 отключен от $2", "description": "$1 is name of the name and $2 represents the dapp name`"}, "discover": {"message": "Обзор"}, "discoverSnaps": {"message": "Откройте для себя Snaps", "description": "Text that links to the Snaps website. Displayed in a banner on Snaps list page in settings."}, "dismiss": {"message": "Отклонить"}, "dismissReminderDescriptionField": {"message": "Включите этот параметр, чтобы отклонить сообщение с напоминанием о создании резервной копии фразы для восстановления. Мы настоятельно рекомендуем создать резервную копию секретной фразы для восстановления, чтобы избежать потери средств."}, "dismissReminderField": {"message": "Отклонить напоминание о необходимости создать резервную копию секретной фразы для восстановления"}, "dismissSmartAccountSuggestionEnabledDescription": {"message": "Включите этот параметр, чтобы больше не видеть предложение «Перейдите на смарт-счет» ни в одном из счетов. Смарт-счета дают доступ к более быстрым транзакциям, более низким комиссиям сети и большей гибкости при их оплате."}, "dismissSmartAccountSuggestionEnabledTitle": {"message": "Отклонить предложение «Перейдите на смарт-счет»"}, "displayNftMedia": {"message": "Показать носитель NFT"}, "displayNftMediaDescription": {"message": "Отображение носителя и данных NFT раскрывает ваш IP-адрес для OpenSea или других третьих сторон. Это может позволить злоумышленникам связать ваш IP-адрес с вашим адресом Ethereum. Автоопределение NFT зависит от этого параметра и будет недоступно, если он отключен."}, "doNotShare": {"message": "Не сообщайте ее никому"}, "domain": {"message": "До<PERSON><PERSON>н"}, "done": {"message": "Выполнено"}, "dontShowThisAgain": {"message": "Не показывать снова"}, "downArrow": {"message": "стрелка «вниз»"}, "downloadGoogleChrome": {"message": "Скачать Google Chrome"}, "downloadNow": {"message": "Скачать сейчас"}, "downloadStateLogs": {"message": "Скачать журналы состояния"}, "dragAndDropBanner": {"message": "Вы можете перетаскивать сети, чтобы изменять их порядок. "}, "dropped": {"message": "Отменено"}, "duplicateContactTooltip": {"message": "Имя для контактов противоречит данным существующего счета или контакта"}, "duplicateContactWarning": {"message": "У Вас есть повторяющиеся контакты"}, "durationSuffixDay": {"message": "Д", "description": "Shortened form of 'day'"}, "durationSuffixHour": {"message": "Ч", "description": "Shortened form of 'hour'"}, "durationSuffixMillisecond": {"message": "МС", "description": "Shortened form of 'millisecond'"}, "durationSuffixMinute": {"message": "МИН", "description": "Shortened form of 'minute'"}, "durationSuffixMonth": {"message": "М<PERSON>С", "description": "Shortened form of 'month'"}, "durationSuffixSecond": {"message": "С", "description": "Shortened form of 'second'"}, "durationSuffixWeek": {"message": "Н", "description": "Shortened form of 'week'"}, "durationSuffixYear": {"message": "Г", "description": "Shortened form of 'year'"}, "earn": {"message": "Зарабатывайте"}, "edit": {"message": "Изменить"}, "editANickname": {"message": "Изменить ник"}, "editAccounts": {"message": "Изменить аккаунты"}, "editAddressNickname": {"message": "Изменить ник адреса"}, "editCancellationGasFeeModalTitle": {"message": "Изменить плату за газ за отмену"}, "editContact": {"message": "Изменить контакт"}, "editGasFeeModalTitle": {"message": "Изменить плату за газ"}, "editGasLimitOutOfBounds": {"message": "Лимит газа должен быть не менее $1"}, "editGasLimitOutOfBoundsV2": {"message": "Лимит газа должен быть больше $1 и меньше $2", "description": "$1 is the minimum limit for gas and $2 is the maximum limit"}, "editGasLimitTooltip": {"message": "Лимит газа — это максимальное количество единиц газа, которое вы готовы использовать. Единицы газа являются множителем «Максимальной платы за приоритет» и «Максимальной комиссии»."}, "editGasMaxBaseFeeGWEIImbalance": {"message": "Максимальная базовая комиссия не может быть ниже платы за приоритет."}, "editGasMaxBaseFeeHigh": {"message": "Максимальная базовая комиссия выше необходимой"}, "editGasMaxBaseFeeLow": {"message": "Максимальная базовая комиссия низкая для текущих условий сети"}, "editGasMaxFeeHigh": {"message": "Максимальная комиссия выше, чем необходимо"}, "editGasMaxFeeLow": {"message": "Максимальная комиссия слишком низкая для условий сети"}, "editGasMaxFeePriorityImbalance": {"message": "Максимальная комиссия не может быть меньше максимальной платы за приоритет"}, "editGasMaxPriorityFeeBelowMinimum": {"message": "Максимальная плата за приоритет должна быть больше 0 Гвей."}, "editGasMaxPriorityFeeBelowMinimumV2": {"message": "Плата за приоритет должна быть больше 0."}, "editGasMaxPriorityFeeHigh": {"message": "Максимальная плата за приоритет выше необходимой. Вы можете заплатить больше, чем нужно."}, "editGasMaxPriorityFeeHighV2": {"message": "Плата за приоритет выше необходимой. Вы можете заплатить больше, чем нужно"}, "editGasMaxPriorityFeeLow": {"message": "Максимальная плата за приоритет низкая для текущих условий сети"}, "editGasMaxPriorityFeeLowV2": {"message": "Плата за приоритет низкая для текущих условий сети"}, "editGasPriceTooLow": {"message": "Цена газа должна быть больше 0"}, "editGasPriceTooltip": {"message": "В этой сети необходимо заполнить поле «Цена газа» при отправке транзакции. Цена газа — это сумма, которую вы будете платить за единицу газа."}, "editGasSubTextFeeLabel": {"message": "Макс. комиссия:"}, "editGasTitle": {"message": "Изменить приоритет"}, "editGasTooLow": {"message": "Время обработки неизвестно"}, "editInPortfolio": {"message": "Изменить в Portfolio"}, "editNetworkLink": {"message": "изменить исходную сеть"}, "editNetworksTitle": {"message": "Изменить сети"}, "editNonceField": {"message": "Изменить одноразовый номер"}, "editNonceMessage": {"message": "Это продвинутая функция, используйте ее с осторожностью."}, "editPermission": {"message": "Изменить разрешение"}, "editPermissions": {"message": "Изменить разрешения"}, "editSpeedUpEditGasFeeModalTitle": {"message": "Изменить плату за газ за ускорение"}, "editSpendingCap": {"message": "Изменить лимит расходов"}, "editSpendingCapAccountBalance": {"message": "Баланс счета: $1 $2"}, "editSpendingCapDesc": {"message": "Введите сумму, которая может быть потрачена от вашего имени."}, "editSpendingCapError": {"message": "Лимит расходов не может иметь более $1 десятичных знаков. Удалите лишние десятичные знаки, чтобы продолжить."}, "editSpendingCapSpecialCharError": {"message": "Введите только цифры"}, "enableAutoDetect": {"message": " Включить автоопределение"}, "enableFromSettings": {"message": " Включите его в Настройках."}, "enableSnap": {"message": "Включить"}, "enableToken": {"message": "активирует для $1", "description": "$1 is a token symbol, e.g. ETH"}, "enabled": {"message": "Включено"}, "enabledNetworks": {"message": "Включенные сети"}, "encryptionPublicKeyNotice": {"message": "$1 запрашивает ваш открытый ключ шифрования. После получения вашего согласия на это данный сайт сможет создавать зашифрованные сообщения для отправки в ваш адрес.", "description": "$1 is the web3 site name"}, "encryptionPublicKeyRequest": {"message": "Запросить открытый ключ шифрования."}, "endpointReturnedDifferentChainId": {"message": "Введенный вами URL RPC вернул другой ID блокчейна ($1).", "description": "$1 is the return value of eth_chainId from an RPC endpoint"}, "enhancedTokenDetectionAlertMessage": {"message": "Улучшенное определение токенов в настоящее время доступно на $1. $2"}, "ensDomainsSettingDescriptionIntroduction": {"message": "NeoNix позволяет вам видеть домены ENS прямо в адресной строке вашего браузера. Вот как это работает:"}, "ensDomainsSettingDescriptionOutroduction": {"message": "Имейте в виду, что использование этой функции открывает доступ к вашему IP-адресу сторонним сервисам IPFS."}, "ensDomainsSettingDescriptionPart1": {"message": "NeoNix сверяется с контрактом ENS Ethereum, чтобы найти код, связанный с именем ENS."}, "ensDomainsSettingDescriptionPart2": {"message": "Если код ссылается на IPFS, вы можете увидеть связанный с ним контент (обычно веб-сайт)."}, "ensDomainsSettingTitle": {"message": "Показать домены ENS в адресной строке"}, "ensUnknownError": {"message": "Ошибка поиска ENS."}, "enterANameToIdentifyTheUrl": {"message": "Введите имя, чтобы идентифицировать URL"}, "enterChainId": {"message": "Введите ID блокчейна"}, "enterMaxSpendLimit": {"message": "Введите максимальный лимит расходов"}, "enterNetworkName": {"message": "Введите имя сети"}, "enterOptionalPassword": {"message": "Введите необязательный пароль"}, "enterPasswordContinue": {"message": "Введите пароль, чтобы продолжить"}, "enterRpcUrl": {"message": "Введите URL RPC"}, "enterSymbol": {"message": "Введите символ"}, "enterTokenNameOrAddress": {"message": "Введите имя токена или вставьте адрес"}, "enterYourPassword": {"message": "Введите свой пароль"}, "errorCode": {"message": "Код: $1", "description": "Displayed error code for debugging purposes. $1 is the error code"}, "errorGettingSafeChainList": {"message": "Ошибка при получении списка безопасных блокчейнов. Продолжайте с осторожностью."}, "errorMessage": {"message": "Сообщение: $1", "description": "Displayed error message for debugging purposes. $1 is the error message"}, "errorName": {"message": "Код: $1", "description": "Displayed error name for debugging purposes. $1 is the error name"}, "errorPageContactSupport": {"message": "Связаться с поддержкой", "description": "Button for contact MM support"}, "errorPageDescribeUsWhatHappened": {"message": "Опишите, что произошло", "description": "<PERSON><PERSON> for submitting report to sentry"}, "errorPageInfo": {"message": "Невозможно показать вашу информацию. Не волнуйтесь, ваш кошелек и средства в безопасности.", "description": "Information banner shown in the error page"}, "errorPageMessageTitle": {"message": "Сообщение об ошибке", "description": "Title for description, which is displayed for debugging purposes"}, "errorPageSentryFormTitle": {"message": "Опишите, что произошло", "description": "In sentry feedback form, The title at the top of the feedback form."}, "errorPageSentryMessagePlaceholder": {"message": "Предоставление подробной информации, например, о том, как мы можем воспроизвести ошибку, поможет нам решить проблему.", "description": "In sentry feedback form, The placeholder for the feedback description input field."}, "errorPageSentrySuccessMessageText": {"message": "Спасибо! Мы скоро рассмотрим это.", "description": "In sentry feedback form, The message displayed after a successful feedback submission."}, "errorPageTitle": {"message": "NeoNix обнару<PERSON><PERSON>л ошибку", "description": "Title of generic error page"}, "errorPageTryAgain": {"message": "Повторить попытку", "description": "<PERSON><PERSON> for try again"}, "errorStack": {"message": "Стек:", "description": "Title for error stack, which is displayed for debugging purposes"}, "errorWhileConnectingToRPC": {"message": "Ошибка при подключении к пользовательской сети."}, "errorWithSnap": {"message": "Ошибка с $1", "description": "$1 represents the name of the snap"}, "estimatedFee": {"message": "Примерная комиссия"}, "estimatedFeeTooltip": {"message": "Сумма, уплаченная за обработку транзакции в сети."}, "ethGasPriceFetchWarning": {"message": "Указана резервная цена газа, поскольку основной сервис определения цены газа сейчас недоступен."}, "ethereumProviderAccess": {"message": "Предоставить поставщику Ethereum доступ к $1", "description": "The parameter is the name of the requesting origin"}, "ethereumPublicAddress": {"message": "Публичный адрес Ethereum"}, "etherscan": {"message": "Etherscan"}, "etherscanView": {"message": "Посмотреть счет на Etherscan"}, "etherscanViewOn": {"message": "Посмотреть на Etherscan"}, "existingChainId": {"message": "Введенная вами информация связана с существующим ID блокчейна."}, "expandView": {"message": "Открыть в новой вкладке"}, "experimental": {"message": "Экспериментальные"}, "exploreweb3": {"message": "Исследуйте web3"}, "exportYourData": {"message": "Экспортируйте свои данные"}, "exportYourDataButton": {"message": "Скачать"}, "exportYourDataDescription": {"message": "Вы можете экспортировать такие данные, как ваши контакты и предпочтения."}, "extendWalletWithSnaps": {"message": "Изучите Snaps, созданные сообществом, чтобы персонализировать работу с web3", "description": "Banner description displayed on Snaps list page in Settings when less than 6 Snaps is installed."}, "externalAccount": {"message": "Внешний счет"}, "externalExtension": {"message": "Внешнее расширение"}, "externalNameSourcesSetting": {"message": "Предлагаемые псевдонимы"}, "externalNameSourcesSettingDescription": {"message": "Мы будем получать предлагаемые псевдонимы для адресов, с которыми вы взаимодействуете, из сторонних источников, таких как Etherscan, Infura и Lens Protocol. Эти источники могут видеть эти адреса и ваш IP-адрес. Адрес вашего счета не будет доступен третьим сторонам."}, "failed": {"message": "Не удалось"}, "failedToFetchChainId": {"message": "Не удалось получить ID блокчейна. Ваш URL RPC правильный?"}, "failover": {"message": "Отказоустойчивость"}, "failoverRpcUrl": {"message": "URL-адрес отказоустойчивого RPC"}, "failureMessage": {"message": "Что-то пошло не так, и мы не смогли завершить действие"}, "fast": {"message": "Быстрый"}, "feeDetails": {"message": "Сведения о комиссии"}, "fileImportFail": {"message": "Импорт файлов не работает? Нажмите здесь!", "description": "Helps user import their account from a JSON file"}, "flaskWelcomeUninstall": {"message": "вам нужно должны удалить это расширение", "description": "This request is shown on the Flask Welcome screen. It is intended for non-developers, and will be bolded."}, "flaskWelcomeWarning1": {"message": "Flask предназначен для разработчиков,и позволяет им экспериментировать с новыми нестабильными API. Если вы не разработчик или бета-тестер, $1.", "description": "This is a warning shown on the Flask Welcome screen, intended to encourage non-developers not to proceed any further. $1 is the bolded message 'flaskWelcomeUninstall'"}, "flaskWelcomeWarning2": {"message": "Мы не гарантируем безопасность и стабильность этого расширения. Новые API-интерфейсы, предлагаемые Flask, не защищены от фишинговых атак, а это означает, что любой сайт или snap, для которых требуется Flask, могут быть злонамеренной попыткой украсть ваши активы.", "description": "This explains the risks of using NeoNix Flask"}, "flaskWelcomeWarning3": {"message": "Все API Flask являются экспериментальными. Они могут быть изменены или удалены без предварительного уведомления или могут оставаться во Flask на неопределенный срок без переноса в стабильную версию NeoNix. Используйте их на свой страх и риск.", "description": "This message warns developers about unstable Flask APIs"}, "flaskWelcomeWarning4": {"message": "Обязательно отключите свое обычное расширение NeoNix при использовании Flask.", "description": "This message calls to pay attention about multiple versions of NeoNix running on the same site (Flask + Prod)"}, "flaskWelcomeWarningAcceptButton": {"message": "Я принимаю риски", "description": "this text is shown on a button, which the user presses to confirm they understand the risks of using Flask"}, "floatAmountToken": {"message": "Сумма токена должна быть целым числом"}, "followUsOnTwitter": {"message": "Подпишитесь на нас в Twitter"}, "forbiddenIpfsGateway": {"message": "Запрещенный шлюз IPFS. Укажите шлюз CID"}, "forgetDevice": {"message": "Забыть это устройство"}, "forgotPassword": {"message": "Забыли пароль?"}, "form": {"message": "форма"}, "from": {"message": "От"}, "fromAddress": {"message": "От: $1", "description": "$1 is the address to include in the From label. It is typically shortened first using shortenAddress"}, "fromTokenLists": {"message": "Из списков токенов: $1"}, "function": {"message": "Функция: $1"}, "fundingMethod": {"message": "Способ пополнения"}, "gas": {"message": "Газ"}, "gasDisplayAcknowledgeDappButtonText": {"message": "Изменить рекомендуемую плату за газ"}, "gasDisplayDappWarning": {"message": "Эта плата за газ была предложена $1. Ее переопредление может вызвать проблемы с вашей транзакцией. При наличии вопросов обратитесь к $1.", "description": "$1 represents the Dapp's origin"}, "gasFee": {"message": "Плата за газ"}, "gasLimit": {"message": "<PERSON>и<PERSON><PERSON><PERSON> газа"}, "gasLimitRecommended": {"message": "Рекомендуемый лимит газа — $1. Если лимит газа меньше этого, он может оказаться неэффективным."}, "gasLimitTooLow": {"message": "Лимит газа должен быть не менее 21 000"}, "gasLimitV2": {"message": "<PERSON>и<PERSON><PERSON><PERSON> газа"}, "gasOption": {"message": "Настройка газа"}, "gasPriceExcessive": {"message": "Установлена неоправданно высокая плата за газ. Рекомендуем снизить ее."}, "gasPriceFetchFailed": {"message": "Не удалось определить примерную цену газа из-за ошибки сети."}, "gasTimingHoursShort": {"message": "$1 ч", "description": "$1 represents a number of hours"}, "gasTimingLow": {"message": "Медленная"}, "gasTimingMinutesShort": {"message": "$1 мин.", "description": "$1 represents a number of minutes"}, "gasTimingSecondsShort": {"message": "$1 сек.", "description": "$1 represents a number of seconds"}, "gasUsed": {"message": "Использовано газа"}, "general": {"message": "Общие"}, "generalCameraError": {"message": "Нам не удалось получить доступ к вашей камере. Пожалуйста, попробуйте еще раз."}, "generalCameraErrorTitle": {"message": "Что-то пошло не так...."}, "generalDescription": {"message": "Синхронизируйте настройки между устройствами, выбирайте настройки сети и отслеживайте данные токенов"}, "genericExplorerView": {"message": "Посмотреть счет на $1"}, "goToSite": {"message": "Перейти на сайт"}, "goerli": {"message": "Тестовая сеть Goerli"}, "gotIt": {"message": "Понятно!"}, "grantExactAccess": {"message": "Предоставить точный доступ"}, "gwei": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "hardware": {"message": "Аппаратный"}, "hardwareWalletConnected": {"message": "Аппаратный кошелек подключен"}, "hardwareWalletLegacyDescription": {"message": "(устаревший)", "description": "Text representing the MEW path"}, "hardwareWalletSubmissionWarningStep1": {"message": "Убедитесь, что $1 подключен, и выберите приложение Ethereum."}, "hardwareWalletSubmissionWarningStep2": {"message": "Включите «данные смарт-контракта» или «слепую подпись» в своем устройстве $1."}, "hardwareWalletSubmissionWarningTitle": {"message": "Перед нажатием на «Отправить»:"}, "hardwareWalletSupportLinkConversion": {"message": "нажмите здесь"}, "hardwareWallets": {"message": "Подключить аппаратный кошелек"}, "hardwareWalletsInfo": {"message": "Интеграция с аппаратным кошельком использует вызовы API к внешним серверам, которые могут видеть ваш IP-адрес и адреса смарт-контрактов, с которыми вы взаимодействуете"}, "hardwareWalletsMsg": {"message": "Выберите аппаратный кошелек, который хотите использовать с NeoNix."}, "here": {"message": "здесь", "description": "as in -click here- for more information (goes with troubleTokenBalances)"}, "hexData": {"message": "Шестнадцатеричные данные"}, "hiddenAccounts": {"message": "Скрыть счета"}, "hide": {"message": "Скрыть"}, "hideAccount": {"message": "Скрыть счет"}, "hideAdvancedDetails": {"message": "Скрыть дополнительные сведения"}, "hideSentitiveInfo": {"message": "Скрыть конфиденциальную информацию"}, "hideTokenPrompt": {"message": "Скрыть токен?"}, "hideTokenSymbol": {"message": "Скрыть $1", "description": "$1 is the symbol for a token (e.g. 'DAI')"}, "hideZeroBalanceTokens": {"message": "Скрыть токены без баланса"}, "high": {"message": "Агрессивный"}, "highGasSettingToolTipMessage": {"message": "Используйте $1, чтобы компенсировать скачки сетевого трафика из-за таких событий, как популярные NFT-дропы.", "description": "$1 is key 'high' (text: 'Aggressive') separated here so that it can be passed in with bold font-weight"}, "highLowercase": {"message": "высокая"}, "highestCurrentBid": {"message": "Самое высокое текущее предложение"}, "highestFloorPrice": {"message": "Самая высокая минимальная цена"}, "history": {"message": "История"}, "holdToRevealContent1": {"message": "Ваша секретная фраза для восстановления дает $1", "description": "$1 is a bolded text with the message from 'holdToRevealContent2'"}, "holdToRevealContent2": {"message": "полный доступ к вашему кошельку и средствам.", "description": "Is the bolded text in 'holdToRevealContent1'"}, "holdToRevealContent3": {"message": "Не сообщайте ее никому. $1 $2", "description": "$1 is a message from 'holdToRevealContent4' and $2 is a text link with the message from 'holdToRevealContent5'"}, "holdToRevealContent4": {"message": "Поддержка NeoNix не будет запрашивать это,", "description": "Part of 'holdToRevealContent3'"}, "holdToRevealContent5": {"message": "но злоумышленники-фишеры могут.", "description": "The text link in 'holdToRevealContent3'"}, "holdToRevealContentPrivateKey1": {"message": "Ваш закрытый ключ обеспечивает $1", "description": "$1 is a bolded text with the message from 'holdToRevealContentPrivateKey2'"}, "holdToRevealContentPrivateKey2": {"message": "полный доступ к вашему кошельку и средствам.", "description": "Is the bolded text in 'holdToRevealContentPrivateKey2'"}, "holdToRevealLockedLabel": {"message": "удерживайте, чтобы показать заблокированный круг"}, "holdToRevealPrivateKey": {"message": "Удерживайте, чтобы показать закрытый ключ"}, "holdToRevealPrivateKeyTitle": {"message": "Обеспечьте безопасность своего закрытого ключа"}, "holdToRevealSRP": {"message": "Удерживайте для отображения СВФ"}, "holdToRevealSRPTitle": {"message": "Обеспечьте безопасность своей СВФ"}, "holdToRevealUnlockedLabel": {"message": "удерживайте, чтобы показать разблокированный круг"}, "honeypotDescription": {"message": "Этот токен может быть ловушкой. Рекомендуется провести комплексную проверку перед началом взаимодействия, чтобы предотвратить возможные финансовые потери."}, "honeypotTitle": {"message": "Ловушка"}, "howNetworkFeesWorkExplanation": {"message": "Расчетная комиссия, необходимая для обработки транзакции. Максимальная комиссия составляет $1."}, "howQuotesWork": {"message": "Как работают котировки"}, "howQuotesWorkExplanation": {"message": "Эта котировка имеет лучшую доходность из всех котировок, которые мы искали. Она основана на курсе свопа, который включает комиссию за промежуточное соединение и комиссию NeoNix в размере $1% за вычетом платы за газ. Плата за газ зависит от загруженности сети и сложности транзакции."}, "id": {"message": "ID"}, "ignoreAll": {"message": "Игнорировать все"}, "ignoreTokenWarning": {"message": "Если вы скроете токены, они не будут отображаться в вашем кошельке. Однако вы все равно можете добавить их, выполнив поиск по ним."}, "imToken": {"message": "imToken"}, "import": {"message": "Импорт", "description": "Button to import an account from a selected file"}, "importAccountError": {"message": "Ошибка импорта счета."}, "importAccountErrorIsSRP": {"message": "Вы ввели секретную фразу для восстановления (или мнемоническую фразу). Чтобы импортировать счет сюда, нужно ввести закрытый ключ, который представляет собой шестнадцатеричную строку длиной 64 символа."}, "importAccountErrorNotAValidPrivateKey": {"message": "Это недействительный закрытый ключ. Вы ввели шестнадцатеричную строку, но она должна содержать 64 символа."}, "importAccountErrorNotHexadecimal": {"message": "Это недействительный закрытый ключ. Вам нужно ввести шестнадцатеричную строку длиной 64 символа."}, "importAccountJsonLoading1": {"message": "Ожи<PERSON><PERSON><PERSON>те, что этот импорт JSON займет несколько минут и заблокирует NeoNix."}, "importAccountJsonLoading2": {"message": "Мы приносим свои извинения, и мы сделаем это быстрее в будущем."}, "importAccountMsg": {"message": "Импортированные счета не будут связаны с вашей секретной фразой для восстановления NeoNix. Узнайте больше об импортированных счетах"}, "importNFT": {"message": "Импорт NFT"}, "importNFTAddressToolTip": {"message": "Например, в OpenSea на странице NFT в разделе «Подробности» есть синяя гиперссылка с надписью «Адрес контракта». Если вы нажмете на нее, вы попадете на адрес контракта на Etherscan; в левом верхнем углу этой страницы должен быть значок с надписью «Контракт», а справа — длинная строка букв и цифр. Это адрес контракта, который создал ваш NFT. Нажмите на значок «копировать» справа от адреса, и он окажется в буфере обмена."}, "importNFTPage": {"message": "страницу импорта NFT"}, "importNFTTokenIdToolTip": {"message": "ID NFT является уникальным идентификатором, поскольку нет двух одинаковых NFT. Опять же, в OpenSea этот номер находится в разделе «Подробности». Запишите его или скопируйте в буфер обмена."}, "importNWordSRP": {"message": "У меня есть фраза для восстановления из $1 слов", "description": "$1 is the number of words in the recovery phrase"}, "importPrivateKey": {"message": "Закрытый ключ"}, "importSRPDescription": {"message": "Импортируйте существующий кошелек с помощью секретной фразы для восстановления из 12 или 24 слов."}, "importSRPNumberOfWordsError": {"message": "Секретные фразы для восстановления содержат 12 слов или 24 слова"}, "importSRPWordError": {"message": "Слово $1 неверное или содержит орфографические ошибки.", "description": "$1 is the word that is incorrect or misspelled"}, "importSRPWordErrorAlternative": {"message": "Слова $1 и $2 неверные или содержат орфографические ошибки.", "description": "$1 and $2 are multiple words that are mispelled."}, "importSecretRecoveryPhrase": {"message": "Импортировать секретную фразу для восстановления"}, "importSecretRecoveryPhraseUnknownError": {"message": "Произошла неизвестная ошибка."}, "importSelectedTokens": {"message": "Импортировать выбранные токены?"}, "importSelectedTokensDescription": {"message": "В вашем кошельке появятся только те токены, которые вы выбрали. Вы всегда можете импортировать скрытые токены позже, выполнив их поиск."}, "importTokenQuestion": {"message": "Импортировать токен?"}, "importTokenWarning": {"message": "Кто угодно может создать токен с любым именем, включая поддельные версии существующих токенов. Добавляйте и торгуйте на свой страх и риск!"}, "importTokensCamelCase": {"message": "Импорт токенов"}, "importTokensError": {"message": "Нам не удалось импортировать токены. Повторите попытку позже."}, "importWallet": {"message": "Импортировать кошелек"}, "importWalletOrAccountHeader": {"message": "Импортировать кошелек или счет"}, "importWalletSuccess": {"message": "Секретная фраза для восстановления $1 импортирована", "description": "$1 is the index of the secret recovery phrase"}, "importWithCount": {"message": "Импортировать $1", "description": "$1 will the number of detected tokens that are selected for importing, if all of them are selected then $1 will be all"}, "imported": {"message": "Импортирован", "description": "status showing that an account has been fully loaded into the keyring"}, "inYourSettings": {"message": "в ваших Настройках"}, "included": {"message": "включено"}, "includesXTransactions": {"message": "Включает $1 транзакции(-й)"}, "infuraBlockedNotification": {"message": "NeoNix не удалось подключиться к хосту блокчейна. Узнать возможные причины можно $1.", "description": "$1 is a clickable link with with text defined by the 'here' key"}, "initialTransactionConfirmed": {"message": "Ваша первоначальная транзакция подтверждена сетью. Нажмите ОК, чтобы вернуться."}, "insightsFromSnap": {"message": "Аналитика от $1", "description": "$1 represents the name of the snap"}, "install": {"message": "Установите,"}, "installOrigin": {"message": "Источник установки"}, "installRequest": {"message": "Добавить в NeoNix"}, "installedOn": {"message": "Установлено на $1", "description": "$1 is the date when the snap has been installed"}, "insufficientBalance": {"message": "Недостаточный баланс."}, "insufficientFunds": {"message": "Недостаточно средств."}, "insufficientFundsForGas": {"message": "Недостаточно средств для оплаты газа"}, "insufficientLockedLiquidityDescription": {"message": "Отсутствие адекватно заблокированной или сожженной ликвидности делает токен уязвимым к внезапному изъятию ликвидности, что может привести к нестабильности рынка."}, "insufficientLockedLiquidityTitle": {"message": "Недостаточно заблокированной ликвидности"}, "insufficientTokens": {"message": "Недостаточно токенов."}, "interactWithSmartContract": {"message": "Смарт-контракт"}, "interactingWith": {"message": "Взаимодействие с"}, "interactingWithTransactionDescription": {"message": "Это контракт, с которым вы взаимодействуете. Защитите себя от мошенников, подтвердив данные"}, "interaction": {"message": "Взаимодействие"}, "invalidAddress": {"message": "Недействительный адрес"}, "invalidAddressRecipient": {"message": "Неверный адрес получателя"}, "invalidAssetType": {"message": "Этот актив является NFT, и его необходимо повторно добавить на странице «Импорт NFT», которая находится на вкладке NFT."}, "invalidChainIdTooBig": {"message": "Недействительный ID блокчейна. Он слишком длинный."}, "invalidCustomNetworkAlertContent1": {"message": "Необходимо повторно ввести ID блокчейна для пользовательской сети «$1».", "description": "$1 is the name/identifier of the network."}, "invalidCustomNetworkAlertContent2": {"message": "Для защиты вас от провайдеров-злоумышленников или провайдеров, у которых много ошибок, ID блокчейна теперь требуются для всех настраиваемых сетей."}, "invalidCustomNetworkAlertContent3": {"message": "Перейдите в «Настройки» > «Сеть» и введите ID блокчейна. ID блокчейна наиболее популярных сетей можно найти на $1.", "description": "$1 is a link to https://chainid.network"}, "invalidCustomNetworkAlertTitle": {"message": "Недействительная пользовательская сеть"}, "invalidHexData": {"message": "Неверные шестнадцатеричные данные"}, "invalidHexNumber": {"message": "Недействительное шестнадцатеричное число."}, "invalidHexNumberLeadingZeros": {"message": "Недействительное шестнадцатеричное число. Удалите все начальные нули."}, "invalidIpfsGateway": {"message": "Недействительный шлюз IPFS: значение должно быть действительным URL"}, "invalidNumber": {"message": "Недействительное число. Введите десятичное или шестнадцатеричное число с префиксом “0x”."}, "invalidNumberLeadingZeros": {"message": "Недействительное число. Удалите все начальные нули."}, "invalidRPC": {"message": "Недействительный URL RPC"}, "invalidSeedPhrase": {"message": "Недействительная секретная фраза для восстановления"}, "invalidSeedPhraseCaseSensitive": {"message": "Введены недействительные данные! Секретная фраза для восстановления чувствительна к регистру."}, "ipfsGateway": {"message": "Шлюз IPFS"}, "ipfsGatewayDescription": {"message": "NeoNix использует сторонние службы для показа изображений ваших NFT, хранящихся в IPFS, отображения информации, связанной с адресами ENS, введенными в адресную строку вашего браузера, и получения значков для различных токенов. Ваш IP-адрес может быть открыт для этих служб, когда вы их используете."}, "ipfsToggleModalDescriptionOne": {"message": "Мы задействуем сторонние службы для показа изображений ваших NFT, хранящихся в IPFS, отображения информации, связанной с адресами ENS, введенными в адресную строку вашего браузера, и получения значков для различных токенов. При использовании вами этих служб они могут узнать ваш IP-адрес."}, "ipfsToggleModalDescriptionTwo": {"message": "При нажатии на «Подтвердить» включается разрешение IPFS. Вы можете отключить его в $1 в любое время.", "description": "$1 is the method to turn off ipfs"}, "ipfsToggleModalSettings": {"message": "Настройки > Безопасность и конфиденциальность"}, "isSigningOrSubmitting": {"message": "Предыдущая транзакция все еще подписывается или отправляется"}, "jazzAndBlockies": {"message": "Jazzicons и Blockies — это два разных стиля уникальных значков, которые помогут вам с первого взгляда идентифицировать свой счет."}, "jazzicons": {"message": "Jazzicons"}, "jsonFile": {"message": "JSON-файл", "description": "format for importing an account"}, "keyringAccountName": {"message": "Название счета"}, "keyringAccountPublicAddress": {"message": "Публичный адрес"}, "keyringSnapRemovalResult1": {"message": "$1 $2 удален", "description": "Displays the result after removal of a keyring snap. $1 is the snap name, $2 is whether it is successful or not"}, "keyringSnapRemovalResultNotSuccessful": {"message": "не", "description": "Displays the `not` word in $2."}, "keyringSnapRemoveConfirmation": {"message": "Введите $1, чтобы подтвердить, что вы хотите удалить этот Snap:", "description": "Asks user to input the name nap prior to deleting the snap. $1 is the snap name"}, "keystone": {"message": "Keystone"}, "knownAddressRecipient": {"message": "Известный адрес контракта."}, "knownTokenWarning": {"message": "Это действие изменит токены, уже указанные в вашем кошельке, которые можно использовать для фишинга. Утверждайте, только если вы уверены, что хотите изменить то, что представляют эти токены. Узнайте подробнее о $1"}, "l1Fee": {"message": "Комиссия L1"}, "l1FeeTooltip": {"message": "Плата за газ L1"}, "l2Fee": {"message": "Комиссия L2"}, "l2FeeTooltip": {"message": "Плата за газ L2"}, "lastConnected": {"message": "Последнее подключение"}, "lastSold": {"message": "Последняя продажа"}, "lavaDomeCopyWarning": {"message": "В целях вашей безопасности выбор этого текста в данный момент недоступен."}, "layer1Fees": {"message": "Комиссии 1-го уровня"}, "layer2Fees": {"message": "Комиссии 2-го уровня"}, "learnHow": {"message": "Узнайте как"}, "learnMore": {"message": "подробнее"}, "learnMoreAboutGas": {"message": "Хотите $1 о газе?", "description": "$1 will be replaced by the learnMore translation key"}, "learnMoreAboutPrivacy": {"message": "Узнайте больше о лучших методах обеспечения конфиденциальности."}, "learnMoreAboutSolanaAccounts": {"message": "Узнайте больше о счетах Solana"}, "learnMoreKeystone": {"message": "Подробнее"}, "learnMoreUpperCase": {"message": "Подробнее"}, "learnMoreUpperCaseWithDot": {"message": "Узнайте подробнее."}, "learnScamRisk": {"message": "мошенничество и угрозы безопасности."}, "leaveNeoNix": {"message": "Выйти из NeoNix?"}, "leaveNeoNixDesc": {"message": "Вы собираетесь посетить сайт за пределами NeoNix. Прежде чем продолжить, перепроверьте URL-адрес."}, "ledgerAccountRestriction": {"message": "Вам необходимо использовать свой последний счет, прежде чем вы сможете добавить новый."}, "ledgerConnectionInstructionCloseOtherApps": {"message": "Закройте все остальные программы, подключенные к вашему устройству, и нажмите здесь для обновления."}, "ledgerConnectionInstructionHeader": {"message": "Перед нажатием «Подтвердить»:"}, "ledgerConnectionInstructionStepFour": {"message": "Включите «данные смарт-контракта» или «слепую подпись» в своем устройстве Ledger."}, "ledgerConnectionInstructionStepThree": {"message": "Убедитесь, что Ledger подключен, и выберите приложение Ethereum."}, "ledgerDeviceOpenFailureMessage": {"message": "Не удалось открыть Ledger. Он может быть подключен к другой программе. Закройте Ledger Live или другие приложения, подключенные к Ledger, и снова попробуйте подключиться."}, "ledgerErrorConnectionIssue": {"message": "Переподключите свой Ledger, откройте приложение ETH и повторите попытку."}, "ledgerErrorDevicedLocked": {"message": "<PERSON>аш Ledger заблокирован. Разблокируйте его и повторите попытку."}, "ledgerErrorEthAppNotOpen": {"message": "Чтобы решить проблему, откройте приложение ETH на своем устройстве и повторите попытку."}, "ledgerErrorTransactionDataNotPadded": {"message": "Входные данные транзакции Ethereum недостаточно подробно заполнены."}, "ledgerLiveApp": {"message": "приложение Ledger Live"}, "ledgerLocked": {"message": "Не удалось подключиться к Ledger. Убедитесь, что устройство разблокировано и приложение Ethereum открыто."}, "ledgerMultipleDevicesUnsupportedInfoDescription": {"message": "Чтобы подключить новое устройство, отключите предыдущее."}, "ledgerMultipleDevicesUnsupportedInfoTitle": {"message": "Одновременно можно подключить только один Ledger"}, "ledgerTimeout": {"message": "Ledger Live слишком долго не отвечает, или время ожидания подключения истекло. Убедитесь, что приложение Ledger Live открыто и устройство разблокировано."}, "ledgerWebHIDNotConnectedErrorMessage": {"message": "Ledger не подключен. Если хотите подключить Ledger, нажмите «Продолжить» еще раз и одобрите HID-подключение", "description": "An error message shown to the user during the hardware connect flow."}, "levelArrow": {"message": "стрелка «уровень»"}, "lightTheme": {"message": "Светлая"}, "likeToImportToken": {"message": "Хотите импортировать этот токен?"}, "likeToImportTokens": {"message": "Вы хотели бы импортировать эти токены?"}, "lineaGoerli": {"message": "Тестовая сеть Linea Goerli"}, "lineaMainnet": {"message": "Мейн-нет Linea"}, "lineaSepolia": {"message": "Тестовая сеть Linea Sepolia"}, "link": {"message": "Ссылка"}, "linkCentralizedExchanges": {"message": "Привяжите счета Coinbase или Binance, чтобы бесплатно переводить криптовалюту в NeoNix."}, "links": {"message": "Ссылки"}, "loadMore": {"message": "Загрузить еще"}, "loading": {"message": "Загрузка..."}, "loadingScreenSnapMessage": {"message": "Завершите транзакцию в Snap."}, "loadingTokenList": {"message": "Загружается список токенов"}, "localhost": {"message": "Локальный хост 8545"}, "lock": {"message": "Заблокировать"}, "lockNeoNix": {"message": "Заблокировать NeoNix"}, "lockTimeInvalid": {"message": "Время блокировки будет числом от 0 до 10080"}, "logo": {"message": "логотип $1", "description": "$1 is the name of the ticker"}, "low": {"message": "Низкая"}, "lowEstimatedReturnTooltipMessage": {"message": "Вы заплатите более $1% от стартовой суммы в виде комиссий. Проверьте сумму для получения и комиссии сети."}, "lowEstimatedReturnTooltipTitle": {"message": "Высокая стоимость"}, "lowGasSettingToolTipMessage": {"message": "Используйте $1, чтобы дождаться более низкой цены. Оценки времени намного менее точны, поскольку цены в некоторой степени непредсказуемы.", "description": "$1 is key 'low' separated here so that it can be passed in with bold font-weight"}, "lowLowercase": {"message": "низкая"}, "mainnet": {"message": "Мейн-нет Ethereum"}, "mainnetToken": {"message": "Этот адрес соответствует известному адресу токена Мейн-нета Ethereum. Перепроверьте адрес контракта и сеть для токена, который вы пытаетесь добавить."}, "makeAnotherSwap": {"message": "Создать новый своп"}, "makeSureNoOneWatching": {"message": "Убедитесь, что никто не смотрит", "description": "Warning to users to be care while creating and saving their new Secret Recovery Phrase"}, "manageDefaultSettings": {"message": "Управление настройками конфиденциальности по умолчанию"}, "manageInstitutionalWallets": {"message": "Управление институциональными кошельками"}, "manageInstitutionalWalletsDescription": {"message": "Включите этот параметр, чтобы активировать институциональные кошельки."}, "manageNetworksMenuHeading": {"message": "Управление сетями"}, "managePermissions": {"message": "Управление разрешениями"}, "marketCap": {"message": "Рыночная капитализация"}, "marketDetails": {"message": "Сведения о рынке"}, "max": {"message": "Макс."}, "maxBaseFee": {"message": "Максимальная базовая комиссия"}, "maxFee": {"message": "Максимальная комиссия"}, "maxFeeTooltip": {"message": "Максимальная комиссия, предусмотренная для оплаты транзакции."}, "maxPriorityFee": {"message": "Максимальная плата за приоритет"}, "medium": {"message": "Рынок"}, "mediumGasSettingToolTipMessage": {"message": "Используйте $1 для быстрой обработки по текущей рыночной цене.", "description": "$1 is key 'medium' (text: 'Market') separated here so that it can be passed in with bold font-weight"}, "memo": {"message": "заметка"}, "message": {"message": "Сообщение"}, "NeoNixConnectStatusParagraphOne": {"message": "Теперь у вас больше возможностей контроля за подключениями счетов в NeoNix."}, "NeoNixConnectStatusParagraphThree": {"message": "Нажмите на него, чтобы управлять подключенными счетами."}, "NeoNixConnectStatusParagraphTwo": {"message": "Кнопка статуса подключения показывает, подключен ли посещаемый вами веб-сайт, к выбранному вами в настоящее время счету."}, "metaMetricsIdNotAvailableError": {"message": "Поскольку вы никогда не включали MetaMetrics, здесь нет данных для удаления."}, "metadataModalSourceTooltip": {"message": "$1 размещается на npm, а $2 — это уникальный идентификатор Snap.", "description": "$1 is the snap name and $2 is the snap NPM id."}, "NeoNixNotificationsAreOff": {"message": "Уведомления кошелька в настоящее время неактивны."}, "NeoNixSwapsOfflineDescription": {"message": "Сервис свопов NeoNix на техобслуживании. Зайдите позже."}, "NeoNixVersion": {"message": "Версия NeoNix"}, "methodData": {"message": "Метод"}, "methodDataTransactionDesc": {"message": "Функция выполняется на основе декодированных входных данных."}, "methodNotSupported": {"message": "Не поддерживается с этим счётом."}, "metrics": {"message": "Показатели"}, "millionAbbreviation": {"message": "млн", "description": "Shortened form of 'million'"}, "mismatchedChainLinkText": {"message": "проверить сведения о сети", "description": "Serves as link text for the 'mismatched<PERSON><PERSON><PERSON>' key. This text will be embedded inside the translation for that key."}, "mismatchedChainRecommendation": {"message": "Мы рекомендуем вам $1, прежде чем продолжить.", "description": "$1 is a clickable link with text defined by the 'mismatchedChainLinkText' key. The link will open to instructions for users to validate custom network details."}, "mismatchedNetworkName": {"message": "Согласно нашим данным, имя сети может не соответствовать этому ID блокчейна."}, "mismatchedNetworkSymbol": {"message": "Отправленный символ валюты не соответствует тому, что мы ожидаем для этого ID блокчейна."}, "mismatchedRpcChainId": {"message": "ID блокчейна, возвращенный пользовательской сетью, не соответствует отправленному ID блокчейна."}, "mismatchedRpcUrl": {"message": "Согласно нашим записям, отправленное значение URL RPC не соответствует известному поставщику для этого ID блокчейна."}, "missingSetting": {"message": "Не удается найти настройку?"}, "missingSettingRequest": {"message": "Запросите здесь"}, "more": {"message": "больше"}, "moreAccounts": {"message": "+ еще $1 счета(-ов)", "description": "$1 is the number of accounts"}, "moreNetworks": {"message": "+ еще $1 сети(-ей)", "description": "$1 is the number of networks"}, "moreQuotes": {"message": "Больше котировок"}, "multichainAddEthereumChainConfirmationDescription": {"message": "Вы добавляете эту сеть в NeoNix и разрешаете этому сайту использовать ее."}, "multichainQuoteCardBridgingLabel": {"message": "Создание моста"}, "multichainQuoteCardQuoteLabel": {"message": "Котировка"}, "multichainQuoteCardTimeLabel": {"message": "Время"}, "multipleSnapConnectionWarning": {"message": "$1 хочет использовать $2 Snaps", "description": "$1 is the dapp and $2 is the number of snaps it wants to connect to."}, "mustSelectOne": {"message": "Необходимо выбрать хотя бы 1 токен."}, "name": {"message": "Имя"}, "nameAddressLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Label above address field in name component modal."}, "nameAlreadyInUse": {"message": "Имя уже используется"}, "nameInstructionsNew": {"message": "Если вы знаете этот адрес, присвойте ему псевдоним, чтобы узнавать его в будущем.", "description": "Instruction text in name component modal when value is not recognised."}, "nameInstructionsRecognized": {"message": "У этого адреса есть псевдоним по умолчанию, но вы можете изменить его или рассмотреть другие рекомендации.", "description": "Instruction text in name component modal when value is recognized but not saved."}, "nameInstructionsSaved": {"message": "Вы уже добавили псевдоним для этого адреса ранее. Вы можете редактировать или просматривать другие рекомендованные псевдонимы.", "description": "Instruction text in name component modal when value is saved."}, "nameLabel": {"message": "Ник", "description": "Label above name input field in name component modal."}, "nameModalMaybeProposedName": {"message": "Может быть: $1", "description": "$1 is the proposed name"}, "nameModalTitleNew": {"message": "Неизвестный адрес", "description": "Title of the modal created by the name component when value is not recognised."}, "nameModalTitleRecognized": {"message": "Распознанный адрес", "description": "Title of the modal created by the name component when value is recognized but not saved."}, "nameModalTitleSaved": {"message": "Сохраненный адрес", "description": "Title of the modal created by the name component when value is saved."}, "nameProviderProposedBy": {"message": "Предложено $1", "description": "$1 is the name of the provider"}, "nameProvider_ens": {"message": "Служба имен Ethereum (ENS)"}, "nameProvider_etherscan": {"message": "Etherscan"}, "nameProvider_lens": {"message": "Протокол Lens"}, "nameProvider_token": {"message": "NeoNix"}, "nameSetPlaceholder": {"message": "Выберите псевдоним...", "description": "Placeholder text for name input field in name component modal."}, "nativeNetworkPermissionRequestDescription": {"message": "$1 запрашивает ваше одобрение на:", "description": "$1 represents dapp name"}, "nativeTokenScamWarningConversion": {"message": "Изменить сведения о сети"}, "nativeTokenScamWarningDescription": {"message": "Символ нативного токена не соответствует ожидаемому символу нативного токена для сети со связанным ID блокчейна. Вы ввели $1, а ожидаемый символ токена — $2. Убедитесь, что вы подключены к правильному блокчейну.", "description": "$1 represents the currency name, $2 represents the expected currency symbol"}, "nativeTokenScamWarningDescriptionExpectedTokenFallback": {"message": "что-то еще", "description": "graceful fallback for when token symbol isn't found"}, "nativeTokenScamWarningTitle": {"message": "Неожиданный символ нативного токена", "description": "Title for nativeTokenScamWarningDescription"}, "needHelp": {"message": "Нужна помощь? Обратитесь в $1", "description": "$1 represents `needHelpLinkText`, the text which goes in the help link"}, "needHelpFeedback": {"message": "Оставьте отзыв"}, "needHelpLinkText": {"message": "Поддержка NeoNix"}, "needHelpSubmitTicket": {"message": "Отправить запрос о поддержке"}, "needImportFile": {"message": "Нужно выбрать файл для импорта.", "description": "User is important an account and needs to add a file to continue"}, "negativeETH": {"message": "Невозможно отправить отрицательную сумму ETH."}, "negativeOrZeroAmountToken": {"message": "Невозможно отправить отрицательную или нулевую сумму актива."}, "network": {"message": "Сеть"}, "networkChanged": {"message": "Сеть изменена"}, "networkChangedMessage": {"message": "Сейчас вы выполняете транзакции на $1.", "description": "$1 is the name of the network"}, "networkDetails": {"message": "Сведения о сети"}, "networkFee": {"message": "Комиссия сети"}, "networkIsBusy": {"message": "Сеть занята. Цены газа высоки, а оценки менее точны."}, "networkMenu": {"message": "Сетевое меню"}, "networkMenuHeading": {"message": "Выбрать сеть"}, "networkName": {"message": "Имя сети"}, "networkNameArbitrum": {"message": "Arbitrum"}, "networkNameAvalanche": {"message": "Avalanche"}, "networkNameBSC": {"message": "BSC"}, "networkNameBase": {"message": "Base"}, "networkNameBitcoin": {"message": "Биткоин"}, "networkNameDefinition": {"message": "Имя, связанное с этой сетью."}, "networkNameEthereum": {"message": "Ethereum"}, "networkNameGoerli": {"message": "<PERSON><PERSON><PERSON>"}, "networkNameLinea": {"message": "Linea"}, "networkNameOpMainnet": {"message": "Мейн-нет OP"}, "networkNamePolygon": {"message": "Polygon"}, "networkNameSolana": {"message": "Solana"}, "networkNameTestnet": {"message": "Тестнет"}, "networkNameZkSyncEra": {"message": "zkSync Era"}, "networkOptions": {"message": "Параметры сети"}, "networkPermissionToast": {"message": "Обновлены сетевые разрешения"}, "networkProvider": {"message": "Поставщик услуг сети"}, "networkStatus": {"message": "Статус сети"}, "networkStatusBaseFeeTooltip": {"message": "Базовая комиссия устанавливается сетью и меняется каждые 13–14 секунд. Наши варианты $1 и $2 учитывают внезапный рост комиссии.", "description": "$1 and $2 are bold text for Medium and Aggressive respectively."}, "networkStatusPriorityFeeTooltip": {"message": "Диапазон платы за приоритет (также известной как «чаевые» майнеру), которая направляется непосредственно майнерам, чтобы они уделили приоритетное внимание вашей транзакции."}, "networkStatusStabilityFeeTooltip": {"message": "Относительная плата за газ составляет $1 за последние 72 часа.", "description": "$1 is networks stability value - stable, low, high"}, "networkSwitchConnectionError": {"message": "Нам не удается подключиться к $1", "description": "$1 represents the network name"}, "networkURL": {"message": "URL сети"}, "networkURLDefinition": {"message": "URL, используемый для доступа к этой сети."}, "networkUrlErrorWarning": {"message": "Злоумышленники иногда имитируют сайты, внося небольшие изменения в адрес сайта. Прежде чем продолжить, убедитесь, что вы взаимодействуете с нужным сайтом. Версия пьюникода: $1", "description": "$1 replaced by RPC URL for network"}, "networks": {"message": "Сети"}, "networksSmallCase": {"message": "сети"}, "nevermind": {"message": "Неважно"}, "new": {"message": "Новинка!"}, "newAccount": {"message": "Новый счет"}, "newAccountNumberName": {"message": "Счет $1", "description": "Default name of next account to be created on create account screen"}, "newContact": {"message": "Новый контакт"}, "newContract": {"message": "Новый контракт"}, "newNFTDetectedInImportNFTsMessageStrongText": {"message": "Настройки > Безопасность и конфиденциальность"}, "newNFTDetectedInImportNFTsMsg": {"message": "Чтобы использовать OpenSea для просмотра своих NFT, включите «Показать носитель NFT» в $1.", "description": "$1 is used for newNFTDetectedInImportNFTsMessageStrongText"}, "newNFTDetectedInNFTsTabMessage": {"message": "Разрешите NeoNix автоматически определять и отображать NFT в вашем кошельке."}, "newNFTsAutodetected": {"message": "Автоопределение NFT"}, "newNetworkAdded": {"message": "«$1» успешно добавлен!"}, "newNetworkEdited": {"message": "«$1» успешно изменен!"}, "newNftAddedMessage": {"message": "NFT успешно добавлен!"}, "newPassword": {"message": "Новый пароль (мин. 8 знаков)"}, "newPrivacyPolicyActionButton": {"message": "Подробнее"}, "newPrivacyPolicyTitle": {"message": "Мы обновили нашу политику конфиденциальности"}, "newRpcUrl": {"message": "Новый URL RPC"}, "newTokensImportedMessage": {"message": "Вы успешно импортировали $1.", "description": "$1 is the string of symbols of all the tokens imported"}, "newTokensImportedTitle": {"message": "Токен импортирован"}, "next": {"message": "Далее"}, "nftAddFailedMessage": {"message": "Невозможно добавить NFT, так как сведения о владельце не совпадают. Убедитесь, что вы ввели правильную информацию."}, "nftAddressError": {"message": "Этот токен является NFT. Добавьте на $1", "description": "$1 is a clickable link with text defined by the 'importNFTPage' key"}, "nftAlreadyAdded": {"message": "NFT уже добавлен."}, "nftAutoDetectionEnabled": {"message": "Автоопределение NFT включено"}, "nftDisclaimer": {"message": "Отказ от ответственности: NeoNix извлекает медиафайл из исходного URL. Этот URL иногда изменяется торговой площадкой, на которой был выполнен минтинг NFT."}, "nftOptions": {"message": "Опционы NFT"}, "nftTokenIdPlaceholder": {"message": "Введите ID токена"}, "nftWarningContent": {"message": "Вы предоставляете доступ к $1, включая все, что может принадлежать вам в будущем. Сторона на другом конце может перевести эти NFT из вашего кошелька в любое время, не спрашивая вас, пока вы не отзовете это одобрение. $2", "description": "$1 is nftWarningContentBold bold part, $2 is Learn more link"}, "nftWarningContentBold": {"message": "все ваши NFT в $1", "description": "$1 is name of the collection"}, "nftWarningContentGrey": {"message": "Действуйте с осторожностью."}, "nfts": {"message": "NFT"}, "nftsPreviouslyOwned": {"message": "Ранее было во владении"}, "nickname": {"message": "Ник"}, "noAccountsFound": {"message": "По данному поисковому запросу счетов не найдено"}, "noActivity": {"message": "Пока нет активности"}, "noConnectedAccountTitle": {"message": "NeoNix не подключен к этому сайту"}, "noConnectionDescription": {"message": "Для подключения к сайту найдите и нажмите кнопку «подключиться». Помните, что NeoNix может подключаться только к сайтам в web3"}, "noConversionRateAvailable": {"message": "Нет доступного обменного курса"}, "noDeFiPositions": {"message": "Пока нет позиций"}, "noDomainResolution": {"message": "Разрешение для домена не предоставлено."}, "noHardwareWalletOrSnapsSupport": {"message": "Snaps и большинство аппаратных кошельков не будут работать с вашей текущей версией браузера."}, "noNFTs": {"message": "Пока нет NFT"}, "noNetworksFound": {"message": "По заданному поисковому запросу сети не найдены"}, "noOptionsAvailableMessage": {"message": "Этот торговый путь сейчас недоступен. Попробуйте изменить сумму, сеть или токен и мы подберем лучший вариант."}, "noSnaps": {"message": "Snaps не установлены."}, "noThanks": {"message": "Нет, спасибо"}, "noTransactions": {"message": "У вас нет транзакций"}, "noWebcamFound": {"message": "Веб-камера вашего компьютера не найдена. Попробуйте еще раз."}, "noWebcamFoundTitle": {"message": "Веб-камера не найдена"}, "nonContractAddressAlertDesc": {"message": "Вы отправляете данные о вызовах на адрес, который не является контрактом. Это может привести к потере средств. Убедитесь, что вы используете правильный адрес и сеть, прежде чем продолжить."}, "nonContractAddressAlertTitle": {"message": "Возможная ошибка"}, "nonce": {"message": "Одноразовый номер"}, "none": {"message": "Нет"}, "notBusy": {"message": "Не занят"}, "notCurrentAccount": {"message": "Это правильный счет? Он отличается от счета, выбранного в настоящее время в вашем кошельке."}, "notEnoughBalance": {"message": "Недостаточный баланс"}, "notEnoughGas": {"message": "Недостаточно газа"}, "notNow": {"message": "Не сейчас"}, "notificationDetail": {"message": "Подробности"}, "notificationDetailBaseFee": {"message": "Базовая комиссия (Гвей)"}, "notificationDetailGasLimit": {"message": "<PERSON>и<PERSON><PERSON><PERSON> газа (единицы)"}, "notificationDetailGasUsed": {"message": "Использовано газа (единицы)"}, "notificationDetailMaxFee": {"message": "Макс. комиссия на газ"}, "notificationDetailNetwork": {"message": "Сеть"}, "notificationDetailNetworkFee": {"message": "Комиссия сети"}, "notificationDetailPriorityFee": {"message": "Плата за приоритет (Гвей)"}, "notificationItemCheckBlockExplorer": {"message": "Проверить в BlockExplorer"}, "notificationItemCollection": {"message": "Коллекция"}, "notificationItemConfirmed": {"message": "Подтверждено"}, "notificationItemError": {"message": "Сейчас невозможно получить данные о комиссиях"}, "notificationItemFrom": {"message": "От"}, "notificationItemLidoStakeReadyToBeWithdrawn": {"message": "Вывод выполнен"}, "notificationItemLidoStakeReadyToBeWithdrawnMessage": {"message": "Теперь вы можете вывести свои $1, не используемые в стейкинге"}, "notificationItemLidoWithdrawalRequestedMessage": {"message": "Ваш запрос на отмену стейкинга $1 отправлен"}, "notificationItemNFTReceivedFrom": {"message": "Получил(-а) NFT от"}, "notificationItemNFTSentTo": {"message": "Отправил(-а) NFT в адрес"}, "notificationItemNetwork": {"message": "Сеть"}, "notificationItemRate": {"message": "Курс (включая комиссию)"}, "notificationItemReceived": {"message": "Получено"}, "notificationItemReceivedFrom": {"message": "Получено от"}, "notificationItemSent": {"message": "Отправлено"}, "notificationItemSentTo": {"message": "Отправлено в адрес"}, "notificationItemStakeCompleted": {"message": "Стейкинг завершен"}, "notificationItemStaked": {"message": "В стейкинге"}, "notificationItemStakingProvider": {"message": "Провайдер стейкинга"}, "notificationItemStatus": {"message": "Статус"}, "notificationItemSwapped": {"message": "Обменяно"}, "notificationItemSwappedFor": {"message": "за"}, "notificationItemTo": {"message": "В"}, "notificationItemTransactionId": {"message": "ID транзакции"}, "notificationItemUnStakeCompleted": {"message": "Отмена стейкинга завершена"}, "notificationItemUnStaked": {"message": "Стейкинг отменен"}, "notificationItemUnStakingRequested": {"message": "Запрошена отмена стейкинга"}, "notificationTransactionFailedMessage": {"message": "Транзакция $1 не удалась! $2", "description": "Content of the browser notification that appears when a transaction fails"}, "notificationTransactionFailedTitle": {"message": "Неудачная транзакция", "description": "Title of the browser notification that appears when a transaction fails"}, "notificationTransactionSuccessMessage": {"message": "Транзакция $1 подтверждена!", "description": "Content of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessTitle": {"message": "Подтвержденная транзакция", "description": "Title of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessView": {"message": "Смотреть на $1", "description": "Additional content in a notification that appears when a transaction is confirmed and has a block explorer URL."}, "notifications": {"message": "Уведомления"}, "notificationsFeatureToggle": {"message": "Включить уведомления кошелька", "description": "Experimental feature title"}, "notificationsFeatureToggleDescription": {"message": "Это позволяет получать уведомления кошелька, такие как уведомления об отправке/получении средств или Nft, а также объявления о функциях.", "description": "Description of the experimental notifications feature"}, "notificationsMarkAllAsRead": {"message": "Отметить все как прочитанные"}, "notificationsPageEmptyTitle": {"message": "Здесь нечего смотреть"}, "notificationsPageErrorContent": {"message": "Попробуйте еще раз посетить эту страницу."}, "notificationsPageErrorTitle": {"message": "Возникла ошибка"}, "notificationsPageNoNotificationsContent": {"message": "Вы еще не получили ни одного уведомления."}, "notificationsSettingsBoxError": {"message": "Возникла проблема, повторите попытку позже."}, "notificationsSettingsPageAllowNotifications": {"message": "Будьте в курсе того, что происходит в вашем кошельке, с помощью уведомлений. Чтобы отправлять уведомления, мы используем профиль для синхронизации некоторых настроек на ваших устройствах. $1"}, "notificationsSettingsPageAllowNotificationsLink": {"message": "Узнайте, как мы защищаем вашу конфиденциальность при использовании этой функции."}, "numberOfNewTokensDetectedPlural": {"message": "$1 новых токена(-ов) найдены в этом счете", "description": "$1 is the number of new tokens detected"}, "numberOfNewTokensDetectedSingular": {"message": "1 новый токен найден в этом счете"}, "numberOfTokens": {"message": "Количество токенов"}, "ofTextNofM": {"message": "из"}, "off": {"message": "Выкл."}, "offlineForMaintenance": {"message": "Отключено для обслуживания"}, "ok": {"message": "ОК"}, "on": {"message": "<PERSON><PERSON><PERSON>."}, "onboardedMetametricsAccept": {"message": "Я согласен(-на)"}, "onboardedMetametricsDisagree": {"message": "Нет, спасибо"}, "onboardedMetametricsKey1": {"message": "Последние разработки"}, "onboardedMetametricsKey2": {"message": "Особенности продукта"}, "onboardedMetametricsKey3": {"message": "Другие соответствующие промоматериалы"}, "onboardedMetametricsLink": {"message": "MetaMetrics,"}, "onboardedMetametricsParagraph1": {"message": "Кроме $1, мы хотели бы использовать данные, чтобы понять, как вы взаимодействуете с рекламными сообщениями.", "description": "$1 represents the 'onboardedMetametricsLink' locale string"}, "onboardedMetametricsParagraph2": {"message": "Это помогает нам персонализировать то, чем мы делимся с вами, например:"}, "onboardedMetametricsParagraph3": {"message": "Помните, что мы никогда не продаем предоставленные вами данные и вы можете отказаться от их предоставления в любое время."}, "onboardedMetametricsTitle": {"message": "Помогите нам улучшить ваш опыт"}, "onboardingAdvancedPrivacyIPFSDescription": {"message": "Шлюз IPFS позволяет получать доступ к данным, размещенным третьими сторонами, и просматривать их. Вы можете добавить пользовательский шлюз IPFS или продолжить использовать шлюз по умолчанию."}, "onboardingAdvancedPrivacyIPFSInvalid": {"message": "Введите действительный URL"}, "onboardingAdvancedPrivacyIPFSTitle": {"message": "Добавить пользовательский шлюз IPFS"}, "onboardingAdvancedPrivacyIPFSValid": {"message": "URL шлюза IPFS действителен"}, "onboardingAdvancedPrivacyNetworkDescription": {"message": "Когда вы используете наши настройки и конфигурации по умолчанию, мы используем Infura в качестве нашего поставщика удаленного вызова процедур (RPC) по умолчанию, чтобы предложить наиболее надежный и конфиденциальный доступ к данным Ethereum, который мы можем предложить. В ограниченных случаях мы можем использовать других поставщиков RPC, чтобы обеспечить наилучший опыт для наших пользователей. Вы можете выбрать свой собственный RPC, но помните, что любой RPC получит ваш IP-адрес и кошелек Ethereum для совершения транзакций. Чтобы узнать больше о том, как Infura обрабатывает данные для учетных записей EVM, прочтите нашу статью $1; для счетов Solana — $2."}, "onboardingAdvancedPrivacyNetworkDescriptionCallToAction": {"message": "нажмите здесь"}, "onboardingAdvancedPrivacyNetworkTitle": {"message": "Выберите свою сеть"}, "onboardingCreateWallet": {"message": "Создать новый кошелек"}, "onboardingImportWallet": {"message": "Импорт существующего кошелька"}, "onboardingMetametricsAgree": {"message": "Я согласен(-на)"}, "onboardingMetametricsDescription": {"message": "Мы хотели бы собрать базовые данные об использовании и диагностике для улучшения NeoNix. Помните, что мы никогда не продаем данные, которые вы здесь предоставляете."}, "onboardingMetametricsInfuraTerms": {"message": "Мы сообщим вам, если решим использовать эти данные для других целей. Вы можете ознакомиться с нашей $1 для получения дополнительной информации. Помните, что вы можете перейти в настройки и отказаться в любой момент.", "description": "$1 represents `onboardingMetametricsInfuraTermsPolicy`"}, "onboardingMetametricsInfuraTermsPolicy": {"message": "Политикой конфиденциальности"}, "onboardingMetametricsNeverCollect": {"message": "$1 клики и просмотры в приложении сохраняются, но другие данные (например, ваш публичный адрес) — нет.", "description": "$1 represents `onboardingMetametricsNeverCollectEmphasis`"}, "onboardingMetametricsNeverCollectEmphasis": {"message": "Приватными:"}, "onboardingMetametricsNeverCollectIP": {"message": "$1 мы временно используем ваш IP-адрес для определения общего местоположения (например, вашей страны или региона), но он никогда не сохраняется.", "description": "$1 represents `onboardingMetametricsNeverCollectIPEmphasis`"}, "onboardingMetametricsNeverCollectIPEmphasis": {"message": "Общедоступными:"}, "onboardingMetametricsNeverSellData": {"message": "$1 вы в любое время решаете, хотите ли вы поделиться своими данными об использовании или удалить их в настройках.", "description": "$1 represents `onboardingMetametricsNeverSellDataEmphasis`"}, "onboardingMetametricsNeverSellDataEmphasis": {"message": "Необязательными:"}, "onboardingMetametricsTitle": {"message": "Помогите нам улучшить NeoNix"}, "onboardingMetametricsUseDataCheckbox": {"message": "Мы будем использовать эти данные, чтобы узнать, как вы взаимодействуете с нашими маркетинговыми сообщениями. Мы можем отправлять соответствующие новости (например, об особенностях продукта)."}, "onboardingPinExtensionDescription": {"message": "Закрепите расширение NeoNix в браузере для его постоянной доступности и легкого просмотра подтверждений транзакций."}, "onboardingPinExtensionDescription2": {"message": "Вы можете открыть NeoNix, нажав на расширение, и получить доступ к своему кошельку одним щелчком мыши."}, "onboardingPinExtensionDescription3": {"message": "Нажмите на значок расширения браузера для мгновенного перехода к нему", "description": "$1 is the browser name"}, "onboardingPinExtensionTitle": {"message": "Установка NeoNix завершена!"}, "onekey": {"message": "OneKey"}, "only": {"message": "только"}, "onlyConnectTrust": {"message": "Подключайтесь только к сайтам, которым доверяете. $1", "description": "Text displayed above the buttons for connection confirmation. $1 is the link to the learn more web page."}, "openFullScreenForLedgerWebHid": {"message": "Перейдите в полноэкранный режим, чтобы подключить свой Ledger.", "description": "Shown to the user on the confirm screen when they are viewing NeoNix in a popup window but need to connect their ledger via webhid."}, "openInBlockExplorer": {"message": "Открыть в обозревателе блоков"}, "optional": {"message": "Необязательно"}, "options": {"message": "Опционы"}, "origin": {"message": "Источник"}, "originChanged": {"message": "Сайт изменился"}, "originChangedMessage": {"message": "Сейчас вы рассматриваете запрос от $1.", "description": "$1 is the name of the origin"}, "osTheme": {"message": "Системная"}, "other": {"message": "другой(-ая/ое)"}, "otherSnaps": {"message": "другие snaps", "description": "Used in the 'permission_rpc' message."}, "others": {"message": "другие"}, "outdatedBrowserNotification": {"message": "Ваш браузер устарел. Если вы не обновите его, то не сможете получать исправления безопасности и новые функции от NeoNix."}, "overrideContentSecurityPolicyHeader": {"message": "Переопределить заголовок Content-Security-Policy"}, "overrideContentSecurityPolicyHeaderDescription": {"message": "Этот вариант является обходным решением известной проблемы в Firefox, когда заголовок Content-Security-Policy децентрализованного приложения может препятствовать правильной загрузке расширения. Отключать эту опцию не рекомендуется, если только это не требуется для совместимости конкретной веб-страницы."}, "padlock": {"message": "Замок"}, "participateInMetaMetrics": {"message": "Участие в MetaMetrics"}, "participateInMetaMetricsDescription": {"message": "Участвуйте в MetaMetrics, чтобы помочь нам улучшить его"}, "password": {"message": "Пароль"}, "passwordNotLongEnough": {"message": "Пароль недостаточно длинный"}, "passwordStrength": {"message": "Надежность пароля: $1", "description": "Return password strength to the user when user wants to create password."}, "passwordStrengthDescription": {"message": "Надежный пароль может повысить безопасность вашего кошелька в случае кражи или взлома вашего устройства."}, "passwordTermsWarning": {"message": "Я понимаю, что NeoNix не может восстановить этот пароль для меня. $1"}, "passwordsDontMatch": {"message": "Пароли не совпадают"}, "pastePrivateKey": {"message": "Вставьте строку вашего закрытого ключа сюда:", "description": "For importing an account from a private key"}, "pending": {"message": "Ожидающий"}, "pendingConfirmationAddNetworkAlertMessage": {"message": "Обновление сети приведет к отмене $1 ожидающих транзакций, поступивших с этого сайта.", "description": "Number of transactions."}, "pendingConfirmationSwitchNetworkAlertMessage": {"message": "Смена сети приведет к отмене $1 ожидающих транзакций, поступивших с этого сайта.", "description": "Number of transactions."}, "pendingTransactionAlertMessage": {"message": "Эта транзакция не пройдет, пока не завершится предыдущая транзакция. $1", "description": "$1 represents the words 'how to cancel or speed up a transaction' in a hyperlink"}, "pendingTransactionAlertMessageHyperlink": {"message": "Узнайте, как отменить или ускорить транзакцию.", "description": "The text for the hyperlink in the pending transaction alert message"}, "permissionDetails": {"message": "Сведения о разрешении"}, "permissionFor": {"message": "Разрешение для"}, "permissionFrom": {"message": "Разрешение от"}, "permissionRequested": {"message": "Запрошено сейчас"}, "permissionRequestedForAccounts": {"message": "Запрошено сейчас для $1", "description": "Permission cell status for requested permission including accounts, rendered as AvatarGroup which is $1."}, "permissionRevoked": {"message": "Отменено в этом обновлении"}, "permissionRevokedForAccounts": {"message": "Отменено в этом обновлении для $1", "description": "Permission cell status for revoked permission including accounts, rendered as AvatarGroup which is $1."}, "permission_accessNamedSnap": {"message": "Подключиться к $1.", "description": "The description for the `wallet_snap` permission. $1 is the human-readable name of the snap."}, "permission_accessNetwork": {"message": "Доступ в Интернет.", "description": "The description of the `endowment:network-access` permission."}, "permission_accessNetworkDescription": {"message": "Предоставьте $1 доступ к Интернету. Его можно использовать как для отправки, так и для получения данных со сторонних серверов.", "description": "An extended description of the `endowment:network-access` permission. $1 is the snap name."}, "permission_accessSnap": {"message": "Подключение к спапу $1.", "description": "The description for the `wallet_snap` permission. $1 is the name of the snap."}, "permission_accessSnapDescription": {"message": "Разрешите веб-сайту и snap взаимодействовать с $1.", "description": "The description for the `wallet_snap_*` permission. $1 is the name of the Snap."}, "permission_assets": {"message": "Показать активы счета в NeoNix.", "description": "The description for the `endowment:assets` permission."}, "permission_assetsDescription": {"message": "Разрешить $1 предоставить информацию об активах клиенту NeoNix. Активы могут быть в блокчейне или вне него.", "description": "An extended description for the `endowment:assets` permission. $1 is the name of the Snap."}, "permission_cronjob": {"message": "Планируйте и выполняйте периодические действия.", "description": "The description for the `snap_cronjob` permission"}, "permission_cronjobDescription": {"message": "Разрешите $1выполнять действия, которые выполняются периодически в фиксированное время, даты или интервалы. Его можно использовать для запуска срочных взаимодействий или уведомлений.", "description": "An extended description for the `snap_cronjob` permission. $1 is the snap name."}, "permission_dialog": {"message": "Отображение диалоговых окон в NeoNix.", "description": "The description for the `snap_dialog` permission"}, "permission_dialogDescription": {"message": "Разрешите $1 отображать всплывающие окна NeoNix с настраиваемым текстом, полем ввода и кнопками для одобрения или отклонения действия.\nМожет использоваться для создания, например, оповещения, подтверждения и процедуры получения согласия для snap.", "description": "An extended description for the `snap_dialog` permission. $1 is the snap name."}, "permission_ethereumAccounts": {"message": "См. адрес, ба<PERSON><PERSON><PERSON><PERSON> счета, активность и рекомендуйте транзакции для одобрения", "description": "The description for the `eth_accounts` permission"}, "permission_ethereumProvider": {"message": "Получите доступ к поставщику Ethereum.", "description": "The description for the `endowment:ethereum-provider` permission"}, "permission_ethereumProviderDescription": {"message": "Разрешите $1 связываться с NeoNix напрямую, чтобы он мог считывать данные из блокчейна и предлагать сообщения и транзакции.", "description": "An extended description for the `endowment:ethereum-provider` permission. $1 is the snap name."}, "permission_getEntropy": {"message": "Извлекайте произвольные ключи, уникальные для этого $1.", "description": "The description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getEntropyDescription": {"message": "Разрешите $1 извлекать произвольные ключи, уникальные для этого $1, не раскрывая их. Эти ключи отделены от вашего(-их) счета(-ов) NeoNix и не связаны с вашими закрытыми ключами или секретной фразой для восстановления. Другие snaps не могут получить доступ к этой информации.", "description": "An extended description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getLocale": {"message": "Просмотрите свой предпочитаемый язык.", "description": "The description for the `snap_getLocale` permission"}, "permission_getLocaleDescription": {"message": "Разрешите $1 получить доступ к предпочитаемому вами языку в настройках NeoNix. Его можно использовать для локализации и отображения содержимого $1 на вашем языке.", "description": "An extended description for the `snap_getLocale` permission. $1 is the snap name."}, "permission_getPreferences": {"message": "Просматривайте такую ​​информацию, как ваш предпочитаемый язык и фиатная валюта.", "description": "The description for the `snap_getPreferences` permission"}, "permission_getPreferencesDescription": {"message": "Предоставьте $1 доступ к такой информации, как ваш предпочитаемый язык и фиатная валюта, в настройках NeoNix. Это помогает $1 отображать контент с учетом ваших предпочтений. ", "description": "An extended description for the `snap_getPreferences` permission. $1 is the snap name."}, "permission_homePage": {"message": "Показать пользовательский экран", "description": "The description for the `endowment:page-home` permission"}, "permission_homePageDescription": {"message": "Разрешите $1 отображает собственный главный экран в NeoNix. Эту функцию можно использовать для пользовательских интерфейсов, конфигурации и инфопанелей.", "description": "An extended description for the `endowment:page-home` permission. $1 is the snap name."}, "permission_keyring": {"message": "Разрешить запросы на добавление и управление счетами Ethereum", "description": "The description for the `endowment:keyring` permission"}, "permission_keyringDescription": {"message": "Разрешите $1 получать запросы на добавление или удаление счетов, а также подписывать и совершать транзакции от имени этих счетов.", "description": "An extended description for the `endowment:keyring` permission. $1 is the snap name."}, "permission_lifecycleHooks": {"message": "Используйте хуки жизненного цикла.", "description": "The description for the `endowment:lifecycle-hooks` permission"}, "permission_lifecycleHooksDescription": {"message": "Разрешите $1 использовать обработчики жизненного цикла для запуска кода в определенное время в течение его жизненного цикла.", "description": "An extended description for the `endowment:lifecycle-hooks` permission. $1 is the snap name."}, "permission_manageAccounts": {"message": "Добавляйте счета Ethereum и управляйте ими", "description": "The description for `snap_manageAccounts` permission"}, "permission_manageAccountsDescription": {"message": "Разрешите $1 добавлять или удалять счета Ethereum, а затем совершать транзакции и подписывать с использованием этих счетов.", "description": "An extended description for the `snap_manageAccounts` permission. $1 is the snap name."}, "permission_manageBip32Keys": {"message": "Управлять счетами $1.", "description": "The description for the `snap_getBip32Entropy` permission. $1 is a derivation path, e.g. 'm/44'/0'/0' (secp256k1)'."}, "permission_manageBip44AndBip32KeysDescription": {"message": "Разрешите $1 управлять счетами и активами в запрошенной сети. Создание и резервное копирование этих счетов выполняется с использованием вашей секретной фразы для восстановления (без ее раскрытия). Благодаря возможности получения ключей $1 может поддерживать различные протоколы блокчейна, помимо Ethereum (EVM).", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions. $1 is the snap name."}, "permission_manageBip44Keys": {"message": "Управлять счетами $1.", "description": "The description for the `snap_getBip44Entropy` permission. $1 is the name of a protocol, e.g. 'Filecoin'."}, "permission_manageState": {"message": "Храните и управляйте его данными на вашем устройстве.", "description": "The description for the `snap_manageState` permission"}, "permission_manageStateDescription": {"message": "Разрешите $1 безопасно хранить, обновлять и извлекать данные с помощью шифрования. Другие snaps не могут получить доступ к этой информации.", "description": "An extended description for the `snap_manageState` permission. $1 is the snap name."}, "permission_nameLookup": {"message": "Выдаёт результаты поиска домена и адреса.", "description": "The description for the `endowment:name-lookup` permission."}, "permission_nameLookupDescription": {"message": "Разрешите snap получать и отображать результаты поиска адресов и доменов в разных частях пользовательского интерфейса NeoNix.", "description": "An extended description for the `endowment:name-lookup` permission."}, "permission_notifications": {"message": "Показать уведомления.", "description": "The description for the `snap_notify` permission"}, "permission_notificationsDescription": {"message": "Разрешите $1 отображать уведомления в NeoNix. Текст короткого уведомления может отображаться snap для получения информации, требующей действий, или срочной информации.", "description": "An extended description for the `snap_notify` permission. $1 is the snap name."}, "permission_protocol": {"message": "Предоставьте данные протокола для одного или нескольких блокчейнов.", "description": "The description for the `endowment:protocol` permission."}, "permission_protocolDescription": {"message": "Разрешить $1 предоставлять NeoNix данные протокола, такие как оценки суммы газа или информацию о токенах.", "description": "An extended description for the `endowment:protocol` permission. $1 is the name of the Snap."}, "permission_rpc": {"message": "Разрешите $1 напрямую взаимодействовать с $2.", "description": "The description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescription": {"message": "Разрешите $1 отправлять сообщения в адрес $2 и получать ответ от $2.", "description": "An extended description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescriptionOriginList": {"message": "$1 и $2", "description": "A list of allowed origins where $2 is the last origin of the list and $1 is the rest of the list separated by ','."}, "permission_signatureInsight": {"message": "Показывать модальное окно с информацией о подписях.", "description": "The description for the `endowment:signature-insight` permission"}, "permission_signatureInsightDescription": {"message": "Разрешить $1 показывать модальное окно с информацией по любому запросу на подпись перед утверждением. Это можно использовать в решениях для защиты от фишинга и обеспечения безопасности.", "description": "An extended description for the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_signatureInsightOrigin": {"message": "Просматривайте источник веб-сайтов, которые инициируют запрос на подпись", "description": "The description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission"}, "permission_signatureInsightOriginDescription": {"message": "Разрешите $1 просматривать источник (URI) веб-сайтов, которые инициируют запросы на подпись. Это можно использовать в решениях для защиты от фишинга и обеспечения безопасности.", "description": "An extended description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_transactionInsight": {"message": "Получайте и отображайте подробную информацию о транзакциях.", "description": "The description for the `endowment:transaction-insight` permission"}, "permission_transactionInsightDescription": {"message": "Разрешите $1 декодировать транзакции и показывать информацию в пользовательском интерфейсе NeoNix. Это можно использовать для защиты от фишинга и обеспечения безопасности.", "description": "An extended description for the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_transactionInsightOrigin": {"message": "Смотрите происхождение веб-сайтов, которые предлагают транзакции", "description": "The description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission"}, "permission_transactionInsightOriginDescription": {"message": "Разрешить $1 видеть происхождение (URI) веб-сайтов, предлагающих транзакции. Это можно использовать для защиты от фишинга и обеспечения безопасности.", "description": "An extended description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_unknown": {"message": "Неизвестное разрешение: $1", "description": "$1 is the name of a requested permission that is not recognized."}, "permission_viewBip32PublicKeys": {"message": "Просмотрите свой открытый ключ для $1 ($2).", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a derivation path, e.g. 'm/44'/0'/0''. $2 is the elliptic curve name, e.g. 'secp256k1'."}, "permission_viewBip32PublicKeysDescription": {"message": "Разрешите $2 просматривать ваши открытые ключи (и адреса) для $1. Это не дает никакого контроля над счетами или активами.", "description": "An extended description for the `snap_getBip32PublicKey` permission. $1 is a derivation path (name). $2 is the snap name."}, "permission_viewNamedBip32PublicKeys": {"message": "Просмотрите свой открытый ключ для $1.", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a name for the derivation path, e.g., 'Ethereum accounts'."}, "permission_walletSwitchEthereumChain": {"message": "Используйте ваши активные сети", "description": "The label for the `wallet_switchEthereumChain` permission"}, "permission_webAssembly": {"message": "Поддержка WebAssembly.", "description": "The description of the `endowment:webassembly` permission."}, "permission_webAssemblyDescription": {"message": "Разрешите $1 доступ к низкоуровневым средам исполнения через WebAssembly.", "description": "An extended description of the `endowment:webassembly` permission. $1 is the snap name."}, "permissions": {"message": "Разрешения"}, "permissionsPageEmptyContent": {"message": "Здесь не на что смотреть"}, "permissionsPageEmptySubContent": {"message": "Здесь вы можете увидеть разрешения, которые вы предоставили установленным Snaps или подключенным сайтам."}, "permitSimulationChange_approve": {"message": "<PERSON>и<PERSON><PERSON><PERSON> расходов"}, "permitSimulationChange_bidding": {"message": "Ваша ставка"}, "permitSimulationChange_listing": {"message": "Ваш список"}, "permitSimulationChange_nft_listing": {"message": "Цена листинга"}, "permitSimulationChange_receive": {"message": "Вы получаете"}, "permitSimulationChange_revoke2": {"message": "Отозвать"}, "permitSimulationChange_transfer": {"message": "Вы отправляете"}, "permitSimulationDetailInfo": {"message": "Вы даёте расходующему лицу разрешение потратить именно столько токенов из вашего аккаунта"}, "permittedChainToastUpdate": {"message": "У $1 есть доступ к $2."}, "personalAddressDetected": {"message": "Обнаружен личный адрес. Введите адрес контракта токена."}, "pinToTop": {"message": "Закрепить вверху"}, "pleaseConfirm": {"message": "Пожалуйста, подтвердите"}, "plusMore": {"message": "+ еще $1", "description": "$1 is the number of additional items"}, "plusXMore": {"message": "+ еще $1", "description": "$1 is a number of additional but unshown items in a list- this message will be shown in place of those items"}, "popularNetworkAddToolTip": {"message": "Некоторые из этих сетей являются зависимыми от третьих сторон. Соединения могут быть менее надежными или позволять третьим сторонам отслеживать активность.", "description": "Learn more link"}, "popularNetworks": {"message": "Популярные сети"}, "portfolio": {"message": "Portfolio"}, "preparingSwap": {"message": "Подготовка свопа..."}, "prev": {"message": "Пред."}, "price": {"message": "Цена"}, "priceUnavailable": {"message": "цена недоступна"}, "primaryType": {"message": "Основной тип"}, "priorityFee": {"message": "Плата за приоритет"}, "priorityFeeProperCase": {"message": "Плата за приоритет"}, "privacy": {"message": "Конфиденциальность"}, "privacyMsg": {"message": "Политика конфиденциальности"}, "privateKey": {"message": "Закрытый ключ", "description": "select this type of file to use to import an account"}, "privateKeyCopyWarning": {"message": "Закрытый ключ за $1", "description": "$1 represents the account name"}, "privateKeyHidden": {"message": "Закрытый ключ скрыт", "description": "Explains that the private key input is hidden"}, "privateKeyShow": {"message": "Показать/скрыть закрытый ключ при вводе", "description": "Describes a toggle that is used to show or hide the private key input"}, "privateKeyShown": {"message": "Этот закрытый ключ отображается", "description": "Explains that the private key input is being shown"}, "privateKeyWarning": {"message": "Предупреждение: никогда не раскрывайте этот ключ. Любой, у кого есть ваши закрытые ключи, может украсть любые активы, хранящиеся на вашем счете."}, "privateNetwork": {"message": "Частная сеть"}, "proceedWithTransaction": {"message": "Я все равно хочу продолжить"}, "productAnnouncements": {"message": "Объявления о продуктах"}, "proposedApprovalLimit": {"message": "Предлагаемый лимит одобрения"}, "provide": {"message": "Предоставить"}, "publicAddress": {"message": "Публичный адрес"}, "pushPlatformNotificationsFundsReceivedDescription": {"message": "Вы получили $1 $2"}, "pushPlatformNotificationsFundsReceivedDescriptionDefault": {"message": "Вы получили несколько токенов"}, "pushPlatformNotificationsFundsReceivedTitle": {"message": "Средства получены"}, "pushPlatformNotificationsFundsSentDescription": {"message": "Вы успешно отправили $1 $2"}, "pushPlatformNotificationsFundsSentDescriptionDefault": {"message": "Вы успешно отправили несколько токенов"}, "pushPlatformNotificationsFundsSentTitle": {"message": "Средства отправлены"}, "pushPlatformNotificationsNftReceivedDescription": {"message": "Вы получили новые NFT"}, "pushPlatformNotificationsNftReceivedTitle": {"message": "NFT получен"}, "pushPlatformNotificationsNftSentDescription": {"message": "Вы успешно отправили NFT"}, "pushPlatformNotificationsNftSentTitle": {"message": "NFT отправлен"}, "pushPlatformNotificationsStakingLidoStakeCompletedDescription": {"message": "Ваш стейкинг Lido выполнен"}, "pushPlatformNotificationsStakingLidoStakeCompletedTitle": {"message": "Стейкинг выполнен"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnDescription": {"message": "Ваша доля Lido готова к выводу"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnTitle": {"message": "Доля готова к выводу"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedDescription": {"message": "Ваш вывод средств из Lido выполнен"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedTitle": {"message": "Вывод средств завершен"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedDescription": {"message": "Ваш запрос на вывод средств из Lido отправлен"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedTitle": {"message": "Запрошен вывод средств"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedDescription": {"message": "Ваш стейкинг RocketPool выполнен"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedTitle": {"message": "Стейкинг выполнен"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedDescription": {"message": "Выполнена отмена стейкинга RocketPool"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedTitle": {"message": "Отмена стейкинга завершена"}, "pushPlatformNotificationsSwapCompletedDescription": {"message": "Ваш обмен NeoNix выполнен"}, "pushPlatformNotificationsSwapCompletedTitle": {"message": "Обмен выполнен"}, "queued": {"message": "В очереди"}, "quoteRate": {"message": "Ку<PERSON><PERSON> котировки"}, "quotedReceiveAmount": {"message": "Сумма к получению $1"}, "quotedTotalCost": {"message": "Общая стоимость: $1"}, "rank": {"message": "<PERSON>ей<PERSON>инг"}, "rateIncludesMMFee": {"message": "В курс включена комиссия в размере $1%"}, "reAddAccounts": {"message": "повторно добавить любые другие счета"}, "reAdded": {"message": "добавлены повторно"}, "readdToken": {"message": "Вы можете снова добавить этот токен в будущем, выбрав «Импорт токена» в меню параметров вашего счета."}, "receive": {"message": "Получить"}, "receiveCrypto": {"message": "Получить криптовалюту"}, "recipientAddressPlaceholderNew": {"message": "Введите публичный адрес (0x) или имя домена"}, "recommendedGasLabel": {"message": "Рекомендовано"}, "recoveryPhraseReminderBackupStart": {"message": "Начните здесь"}, "recoveryPhraseReminderConfirm": {"message": "Понятно"}, "recoveryPhraseReminderHasBackedUp": {"message": "Всегда храните свою секретную фразу для восстановления в надежном и потайном месте."}, "recoveryPhraseReminderHasNotBackedUp": {"message": "Хотите снова сделать резервную копию секретной фразы для восстановления?"}, "recoveryPhraseReminderItemOne": {"message": "Никогда никому не сообщайте никому свою секретную фразу для восстановления."}, "recoveryPhraseReminderItemTwo": {"message": "Команда NeoNix никогда неожиданно не запросит вашу секретную фразу для восстановления"}, "recoveryPhraseReminderSubText": {"message": "Ваша секретная фраза для восстановления контролирует все ваши счета."}, "recoveryPhraseReminderTitle": {"message": "Защитите свои средства"}, "redeposit": {"message": "Повторить депозит"}, "refreshList": {"message": "Обновить список"}, "reject": {"message": "Отклонить"}, "rejectAll": {"message": "Отклонить все"}, "rejectRequestsDescription": {"message": "Вы собираетесь отклонить сразу $1 запроса(-ов)."}, "rejectRequestsN": {"message": "Отклонить $1 запроса(-ов)"}, "rejectTxsDescription": {"message": "Вы собираетесь отклонить сразу $1 транзакции(-ий)."}, "rejectTxsN": {"message": "Отклонить $1 транзакции(-ий)"}, "rejected": {"message": "Отклонено"}, "remove": {"message": "Удалить"}, "removeAccount": {"message": "Удалить счет"}, "removeAccountDescription": {"message": "Этот счет будет удален из вашего кошелька. Перед продолжением убедитесь, что у вас есть секретная фраза для восстановления или закрытый ключ для этого импортированного счета. Вы можете импортировать или снова создать счета из раскрывающегося списка счетов. "}, "removeKeyringSnap": {"message": "Удаление этого Snap приведет к удалению этих счетов из NeoNix:"}, "removeKeyringSnapToolTip": {"message": "Snap контролирует счета, и при его удалении счета будут удалены и из NeoNix, но останутся в блокчейне."}, "removeNFT": {"message": "Удалить NFT"}, "removeNftErrorMessage": {"message": "Нам не удалось удалить этот NFT."}, "removeNftMessage": {"message": "NFT успешно удален!"}, "removeSnap": {"message": "Удали<PERSON>ь Snap"}, "removeSnapAccountDescription": {"message": "Если вы продолжите, этот счет больше не будет доступен в NeoNix."}, "removeSnapAccountTitle": {"message": "Удалить счет"}, "removeSnapConfirmation": {"message": "Уверены, что хотите удалить $1?", "description": "$1 represents the name of the snap"}, "removeSnapDescription": {"message": "Это действие удалит snap, его данные и аннулирует предоставленные вам разрешения."}, "replace": {"message": "заменить"}, "reportIssue": {"message": "Сообщить о проблеме"}, "requestFrom": {"message": "Запрос от"}, "requestFromInfo": {"message": "Это сайт, который запрашивает вашу подпись."}, "requestFromInfoSnap": {"message": "Это <PERSON>nap, который запрашивает вашу подпись."}, "requestFromTransactionDescription": {"message": "Это сайт, запрашивающий ваше подтверждение."}, "requestingFor": {"message": "Запрашивается для"}, "requestingForAccount": {"message": "Запрашивается для $1", "description": "Name of Account"}, "requestingForNetwork": {"message": "Запрашивается для $1", "description": "Name of Network"}, "required": {"message": "Требуется"}, "reset": {"message": "Сбросить"}, "resetWallet": {"message": "Сбросить кошелек"}, "resetWalletSubHeader": {"message": "NeoNix не хранит копию вашего пароля. Если у вас возникли проблемы с разблокировкой счета, вам необходимо сбросить настройки кошелька. Вы можете сделать это, введя секретную фразу для восстановления, которую вы использовали при настройке кошелька."}, "resetWalletUsingSRP": {"message": "Это действие удалит ваш текущий кошелек и секретную фразу для восстановления с этого устройства, а также список счетов, которые вы курировали. После сброса с помощью секретной фразы для восстановления вы увидите список счетов, основанный на секретной фразе для восстановления, которую вы использовали для сброса. Этот новый список будет автоматически включать счета с балансом средств. Вы также сможете получить $1, созданный ранее. Пользовательские счета, которые вы импортировали, должны будут $2, и любые пользовательские токены, которые вы добавили на счет, также должны будут $3."}, "resetWalletWarning": {"message": "Прежде чем продолжить, убедитесь, что вы используете правильную секретную фразу для восстановления. Вы не сможете отменить это."}, "restartNeoNix": {"message": "Перезапустить NeoNix"}, "restore": {"message": "Восстановить"}, "restoreUserData": {"message": "Восстановить пользовательские данные"}, "resultPageError": {"message": "Ошибка"}, "resultPageErrorDefaultMessage": {"message": "Ошибка операции."}, "resultPageSuccess": {"message": "Успех"}, "resultPageSuccessDefaultMessage": {"message": "Операция выполнена."}, "retryTransaction": {"message": "Повторить транзакцию"}, "reusedTokenNameWarning": {"message": "В токене здесь используется символ другого токена, который вы отслеживаете. Это может запутать или ввести в заблуждение."}, "revealSecretRecoveryPhrase": {"message": "Показать секретную фразу для восстановления"}, "revealSeedWords": {"message": "Показать секретную фразу для восстановления"}, "revealSeedWordsDescription1": {"message": "$1 дает $2", "description": "This is a sentence consisting of link using 'revealSeedWordsSRPName' as $1 and bolded text using 'revealSeedWordsDescription3' as $2."}, "revealSeedWordsDescription2": {"message": "NeoNix — $1. Это означает, что вы являетесь владельцем своей СВФ.", "description": "$1 is text link with the message from 'revealSeedWordsNonCustodialWallet'"}, "revealSeedWordsDescription3": {"message": "полный доступ к вашему кошельку и средствам.\n"}, "revealSeedWordsNonCustodialWallet": {"message": "некастодиальный кошелек"}, "revealSeedWordsQR": {"message": "QR"}, "revealSeedWordsSRPName": {"message": "Секретная фраза для восстановления (СВФ)"}, "revealSeedWordsText": {"message": "Текст"}, "revealSeedWordsWarning": {"message": "Убедитесь, что никто не смотрит на ваш экран. $1", "description": "$1 is bolded text using the message from 'revealSeedWordsWarning2'"}, "revealSeedWordsWarning2": {"message": "Поддержка NeoNix никогда не будет запрашивать этот ключ.", "description": "The bolded texted in the second part of 'revealSeedWordsWarning'"}, "revealSensitiveContent": {"message": "Показать конфиденциальный контент"}, "review": {"message": "Проверить"}, "reviewAlert": {"message": "Оповещение о проверке"}, "reviewAlerts": {"message": "Просмотреть оповещения"}, "reviewPendingTransactions": {"message": "Проверить ожидающие транзакции"}, "reviewPermissions": {"message": "Проверить разрешения"}, "revokePermission": {"message": "Отозвать разрешение"}, "revokePermissionTitle": {"message": "Удалить разрешение для $1", "description": "The token symbol that is being revoked"}, "revokeSimulationDetailsDesc": {"message": "Вы удаляете чье-либо разрешение на трату токенов с вашего счета."}, "reward": {"message": "Вознаграждение"}, "rpcNameOptional": {"message": "Имя RPC (необязательно)"}, "rpcUrl": {"message": "URL RPC"}, "safeTransferFrom": {"message": "Безопасный перевод из"}, "save": {"message": "Сохранить"}, "scanInstructions": {"message": "Поместите QR-код перед камерой"}, "scanQrCode": {"message": "Сканировать QR-код"}, "scrollDown": {"message": "Прокрутить вниз"}, "search": {"message": "Поиск"}, "searchAccounts": {"message": "Поиск счетов"}, "searchNfts": {"message": "Поиск NFT"}, "searchTokens": {"message": "Поиск токенов"}, "searchTokensByNameOrAddress": {"message": "Поиск токенов по имени или адресу"}, "secretRecoveryPhrase": {"message": "Секретная фраза для восстановления"}, "secretRecoveryPhrasePlusNumber": {"message": "Секретная фраза для восстановления $1", "description": "The $1 is the order of the Secret Recovery Phrase"}, "secureWallet": {"message": "Безопасный кошелек"}, "security": {"message": "Безопасность"}, "securityAlert": {"message": "Оповещение о безопасности от $1 и $2"}, "securityAlerts": {"message": "Оповещения безопасности"}, "securityAlertsDescription": {"message": "Эта функция предупреждает вас о вредоносной активности, активно проверяя транзакции и запросы на подпись. $1", "description": "Link to learn more about security alerts"}, "securityAndPrivacy": {"message": "Безопасность и конфиденциальность"}, "securityDescription": {"message": "Уменьшите свои шансы присоединиться к небезопасным сетям и защитите свои счета"}, "securityMessageLinkForNetworks": {"message": "мошенничества с сетью и угроз безопасности"}, "securityProviderPoweredBy": {"message": "На основе $1", "description": "The security provider that is providing data"}, "seeAllPermissions": {"message": "Смотреть все разрешения", "description": "Used for revealing more content (e.g. permission list, etc.)"}, "seeDetails": {"message": "См. подробности"}, "seedPhraseIntroTitle": {"message": "Защитите свой кошелек"}, "seedPhraseReq": {"message": "Секретные фразы для восстановления содержат 12, 15, 18, 21 или 24 слова"}, "select": {"message": "Выбрать"}, "selectAccountToConnect": {"message": "Выберите счет для подключения"}, "selectAccounts": {"message": "Выберите счета(-а) для использования на этом сайте"}, "selectAccountsForSnap": {"message": "Выберите счет(-а) для использования с этим snap"}, "selectAll": {"message": "Выбрать все"}, "selectAnAccount": {"message": "Выберите счет"}, "selectAnAccountAlreadyConnected": {"message": "Этот счет уже подключен к NeoNix"}, "selectEnableDisplayMediaPrivacyPreference": {"message": "Включить отображение носителя NFT"}, "selectHdPath": {"message": "Выберите путь HD"}, "selectNFTPrivacyPreference": {"message": "Включите автоопределение NFT"}, "selectPathHelp": {"message": "Если вы не видите ожидаемые счета, попробуйте переключиться на путь HD или текущую выбранную сеть."}, "selectRpcUrl": {"message": "Выберите URL RPC"}, "selectSecretRecoveryPhrase": {"message": "Выберите секретную фразу для восстановления"}, "selectType": {"message": "Выбрать тип"}, "selectedAccountMismatch": {"message": "Выбран другой счет"}, "selectingAllWillAllow": {"message": "Выбор всех позволит этому сайту просматривать все ваши текущие счета. Убедитесь, что вы доверяете этому сайту."}, "send": {"message": "Отправить"}, "sendBugReport": {"message": "Отправьте нам отчет об ошибке."}, "sendNoContactsConversionText": {"message": "нажмите здесь"}, "sendNoContactsDescription": {"message": "Контакты позволяют безопасно отправлять транзакции в другой счет несколько раз. Чтобы создать контакт, $1", "description": "$1 represents the action text 'click here'"}, "sendNoContactsTitle": {"message": "У вас пока нет контактов"}, "sendSelectReceiveAsset": {"message": "Выберите актив для получения"}, "sendSelectSendAsset": {"message": "Выберите актив для отправки"}, "sendSpecifiedTokens": {"message": "Отправить $1", "description": "Symbol of the specified token"}, "sendSwapSubmissionWarning": {"message": "Нажатие этой кнопки немедленно инициирует транзакцию обмена. Прежде чем продолжить, проверьте детали транзакции."}, "sendTokenAsToken": {"message": "Отправить $1 как $2", "description": "Used in the transaction display list to describe a swap and send. $1 and $2 are the symbols of tokens in involved in the swap."}, "sendingAsset": {"message": "Идет отправка $1"}, "sendingDisabled": {"message": "Отправка NFT-активов ERC-1155 пока не поддерживается."}, "sendingNativeAsset": {"message": "Отправка $1...", "description": "$1 represents the native currency symbol for the current network (e.g. ETH or BNB)"}, "sendingToTokenContractWarning": {"message": "Предупреждение: вы собираетесь отправить контракт на токены, что может привести к потере средств. $1", "description": "$1 is a clickable link with text defined by the 'learnMoreUpperCase' key. The link will open to a support article regarding the known contract address warning"}, "sepolia": {"message": "Тестовая сеть Sepolia"}, "setApprovalForAll": {"message": "Установить одобрение для всех"}, "setApprovalForAllRedesignedTitle": {"message": "Запрос вывода средств"}, "setApprovalForAllTitle": {"message": "Одобрить $1 без ограничений по расходам", "description": "The token symbol that is being approved"}, "settingAddSnapAccount": {"message": "Доба<PERSON><PERSON><PERSON>ь Snap счета"}, "settings": {"message": "Настройки"}, "settingsSearchMatchingNotFound": {"message": "Совпадений не найдено."}, "settingsSubHeadingSignaturesAndTransactions": {"message": "Запросы на подпись или транзакции"}, "show": {"message": "Показать"}, "showAccount": {"message": "Показать счет"}, "showAdvancedDetails": {"message": "Показать дополнительные сведения"}, "showExtensionInFullSizeView": {"message": "Пказать расширение в полноразмерном виде"}, "showExtensionInFullSizeViewDescription": {"message": "Включите этот параметр, чтобы полноразмерный просмотр отображался по умолчанию при щелчке по значку расширения."}, "showFiatConversionInTestnets": {"message": "Показывать обмен в тестовых сетях"}, "showFiatConversionInTestnetsDescription": {"message": "Выберите это, чтобы показывать обмен на фиатную валюту в тестовых сетях"}, "showHexData": {"message": "Показать шестнадцатеричные данные"}, "showHexDataDescription": {"message": "Выберите эту опцию, чтобы отобразить поле шестнадцатеричных данных на экране отправки"}, "showLess": {"message": "Показать меньше"}, "showMore": {"message": "Показать больше"}, "showNativeTokenAsMainBalance": {"message": "Показывать нативный токен в качестве основного баланса"}, "showNft": {"message": "Показать NFT"}, "showPermissions": {"message": "Показать разрешения"}, "showPrivateKey": {"message": "Показать закрытый ключ"}, "showSRP": {"message": "Показать секретную фразу для восстановления"}, "showTestnetNetworks": {"message": "Показать тестовые сети"}, "showTestnetNetworksDescription": {"message": "Выберите эту опцию, чтобы показать тестовые сети в списке сетей"}, "sign": {"message": "Подписать"}, "signatureRequest": {"message": "Запрос подписи"}, "signature_decoding_bid_nft_tooltip": {"message": "NFT отобразится в Вашем кошельке, когда ставка будет принята."}, "signature_decoding_list_nft_tooltip": {"message": "Ожидайте изменений, только если кто-то купит Ваши NFT."}, "signed": {"message": "Подписано"}, "signing": {"message": "Подписание"}, "signingInWith": {"message": "Вход с помощью"}, "signingWith": {"message": "Подписание с помощью"}, "simulationApproveHeading": {"message": "Отозвать"}, "simulationDetailsApproveDesc": {"message": "Вы даете кому-то другому разрешение на вывод NFT с вашего счета."}, "simulationDetailsERC20ApproveDesc": {"message": "Вы даете кому-то разрешение на трату этой суммы с вашего счета."}, "simulationDetailsFiatNotAvailable": {"message": "Недоступно"}, "simulationDetailsIncomingHeading": {"message": "Вы получаете"}, "simulationDetailsNoChanges": {"message": "Изменений нет"}, "simulationDetailsOutgoingHeading": {"message": "Вы отправляете"}, "simulationDetailsRevokeSetApprovalForAllDesc": {"message": "Вы отзываете чье-то разрешение на вывод NFT с вашего счета."}, "simulationDetailsSetApprovalForAllDesc": {"message": "Вы даете кому-то разрешение на вывод NFT с вашего счета."}, "simulationDetailsTitle": {"message": "Прогнозируемые изменения"}, "simulationDetailsTitleTooltip": {"message": "Прогнозируемые изменения — это то, что может произойти, если вы завершите эту транзакцию. Это всего лишь прогноз, а не гарантия."}, "simulationDetailsTotalFiat": {"message": "Итого = $1", "description": "$1 is the total amount in fiat currency on one side of the transaction"}, "simulationDetailsTransactionReverted": {"message": "Эта транзакция, скорее всего, не удастся"}, "simulationDetailsUnavailable": {"message": "Недоступно"}, "simulationErrorMessageV2": {"message": "Мы не смогли оценить размер платы за газ. В контракте может быть ошибка, и эта транзакция может завершиться неудачно."}, "simulationsSettingDescription": {"message": "Включите эту опцию, чтобы оценить изменения баланса транзакций и подписей перед их подтверждением. Это не гарантирует их окончательный результат. $1"}, "simulationsSettingSubHeader": {"message": "Спрогнозировать изменения баланса"}, "singleNetwork": {"message": "1 сеть"}, "siweIssued": {"message": "Выдано"}, "siweNetwork": {"message": "Сеть"}, "siweRequestId": {"message": "ID запроса"}, "siweResources": {"message": "Ресурсы"}, "siweURI": {"message": "URL-адрес"}, "skipAccountSecurity": {"message": "Пропустить безопасность счета?"}, "skipAccountSecurityDetails": {"message": "Я понимаю, что, если я не создам резервную копию своей секретной фразы для восстановления, я могу потерять доступ ко всем своим счетам и всем средствам на них."}, "slideBridgeDescription": {"message": "Перемещайтесь по 9 блокчейнам, все из которых находятся в Вашем кошельке"}, "slideBridgeTitle": {"message": "Готовы создать мост?"}, "slideCashOutDescription": {"message": "Продайте свою криптовалюту за наличные"}, "slideCashOutTitle": {"message": "Вывести деньги с помощью NeoNix"}, "slideDebitCardDescription": {"message": "Доступна в некоторых регионах"}, "slideDebitCardTitle": {"message": "Дебетовая карта NeoNix"}, "slideFundWalletDescription": {"message": "Добавьте или переведите токены, чтобы начать"}, "slideFundWalletTitle": {"message": "Пополните свой кошелек"}, "slideMultiSrpDescription": {"message": "Импорт и использование нескольких кошельков в NeoNix"}, "slideMultiSrpTitle": {"message": "Добавьте несколько секретных фраз для восстановления"}, "slideRemoteModeDescription": {"message": "Используйте свой холодный кошелек по беспроводной сети"}, "slideRemoteModeTitle": {"message": "Холодное хранилище, быстрый доступ"}, "slideSmartAccountUpgradeDescription": {"message": "Один и тот же адрес, более умные функции"}, "slideSmartAccountUpgradeTitle": {"message": "Начните использовать смарт-счета"}, "slideSolanaDescription": {"message": "Создайте счет Solana, чтобы начать"}, "slideSolanaTitle": {"message": "Теперь поддерживается Solana"}, "slideSweepStakeDescription": {"message": "Выполните минтинг NFT и получите шанс выиграть"}, "slideSweepStakeTitle": {"message": "Примите участие в розыгрыше USDC на $5000!"}, "smartAccountAccept": {"message": "Использовать смарт-счет"}, "smartAccountBetterTransaction": {"message": "Более быстрые транзакции, более низкие комиссии"}, "smartAccountBetterTransactionDescription": {"message": "Экономьте время и деньги, обрабатывая транзакции вместе."}, "smartAccountFeaturesDescription": {"message": "Сохраните тот же адрес счета, и вы сможете вернуться к нему в любое время."}, "smartAccountLabel": {"message": "Смарт-счет"}, "smartAccountPayToken": {"message": "Платите любым токеном в любое время"}, "smartAccountPayTokenDescription": {"message": "Используйте имеющиеся у вас токены для оплаты комиссий сети."}, "smartAccountReject": {"message": "Не использовать смарт-счет"}, "smartAccountRequestFor": {"message": "Запрос на"}, "smartAccountSameAccount": {"message": "Один и тот же счет, более умные функции."}, "smartAccountSplashTitle": {"message": "Использовать смарт-счет?"}, "smartAccountUpgradeBannerDescription": {"message": "Один и тот же адрес. Более умные функции."}, "smartAccountUpgradeBannerTitle": {"message": "Перейдите на смарт-счет"}, "smartContracts": {"message": "Смарт-контракты"}, "smartSwapsErrorNotEnoughFunds": {"message": "Недостаточно средств для смарт-свопа."}, "smartSwapsErrorUnavailable": {"message": "Смарт-свопы временно недоступны."}, "smartTransactionCancelled": {"message": "Ваша транзакция отменена"}, "smartTransactionCancelledDescription": {"message": "Не удалось завершить вашу транзакцию, поэтому она была отменена, чтобы избавить вас от ненужной платы за газ."}, "smartTransactionError": {"message": "Ваша транзакция не удалась"}, "smartTransactionErrorDescription": {"message": "Внезапные изменения на рынке могут привести к неудачам. Если проблема не исчезнет, ​​обратитесь в поддержку NeoNix."}, "smartTransactionPending": {"message": "Ваша транзакция отправляется"}, "smartTransactionSuccess": {"message": "Ваша транзакция завершена"}, "smartTransactions": {"message": "Умные транзакции"}, "smartTransactionsEnabledDescription": {"message": " и защита MEV. Теперь включено по умолчанию."}, "smartTransactionsEnabledLink": {"message": "Более высокие показатели успеха"}, "smartTransactionsEnabledTitle": {"message": "Транзакции только что стали умнее"}, "snapAccountCreated": {"message": "Счет создан"}, "snapAccountCreatedDescription": {"message": "Ваш новый счет готов к использованию!"}, "snapAccountCreationFailed": {"message": "Не удалось создать счет"}, "snapAccountCreationFailedDescription": {"message": "$1 не удалось создать для вас счет.", "description": "$1 is the snap name"}, "snapAccountRedirectFinishSigningTitle": {"message": "Завершить подписание"}, "snapAccountRedirectSiteDescription": {"message": "Следуйте инструкциям от $1"}, "snapAccountRemovalFailed": {"message": "Не удалось удалить счет"}, "snapAccountRemovalFailedDescription": {"message": "$1 не удалось удалить для вас этот счет.", "description": "$1 is the snap name"}, "snapAccountRemoved": {"message": "Счет удален"}, "snapAccountRemovedDescription": {"message": "Этот счет больше не будет доступен для использования в NeoNix."}, "snapAccounts": {"message": "Snaps счета"}, "snapAccountsDescription": {"message": "Счета, контролируемые сторонними Snap."}, "snapConnectTo": {"message": "Подключиться к $1", "description": "$1 is the website URL or a Snap name. Used for Snaps pre-approved connections."}, "snapConnectionPermissionDescription": {"message": "Позвольте $1 автоматически подключаться к $2 без вашего одобрения.", "description": "Used for Snap pre-approved connections. $1 is the Snap name, $2 is a website URL."}, "snapConnectionWarning": {"message": "$1 хочет использовать $2", "description": "$2 is the snap and $1 is the dapp requesting connection to the snap."}, "snapDetailWebsite": {"message": "Веб-сайт"}, "snapHomeMenu": {"message": "Главное меню Snap"}, "snapInstallRequest": {"message": "Установка $1 дает ему следующие разрешения.", "description": "$1 is the snap name."}, "snapInstallSuccess": {"message": "Установка завершена"}, "snapInstallWarningCheck": {"message": "$1 требуется разрешение, чтобы сделать следующее:", "description": "Warning message used in popup displayed on snap install. $1 is the snap name."}, "snapInstallWarningHeading": {"message": "Действуйте с осторожностью"}, "snapInstallWarningPermissionDescriptionForBip32View": {"message": "Разрешите $1 просматривать ваши открытые ключи (и адреса). Это не дает никакого контроля над счетами или активами.", "description": "An extended description for the `snap_getBip32PublicKey` permission used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionDescriptionForEntropy": {"message": "Разрешите $1 управлять счетами и активами в запрошенной(-ых) сети(-ях). Создание и резервное копирование этих счетов выполняется с использованием вашей секретной фразы для восстановления (без ее раскрытия). Благодаря возможности получения ключей $1 может поддерживать различные протоколы блокчейна, помимо Ethereum (EVM).", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionNameForEntropy": {"message": "Управлять счетами $1", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallWarningPermissionNameForViewPublicKey": {"message": "Посмотрите свой открытый ключ для $1", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallationErrorDescription": {"message": "Не удалось установить $1.", "description": "Error description used when snap installation fails. $1 is the snap name."}, "snapInstallationErrorTitle": {"message": "Ошибка установки", "description": "Error title used when snap installation fails."}, "snapResultError": {"message": "Ошибка"}, "snapResultSuccess": {"message": "Успех"}, "snapResultSuccessDescription": {"message": "$1 готово к использованию"}, "snapUIAssetSelectorTitle": {"message": "Выбра<PERSON>ь актив"}, "snapUpdateAlertDescription": {"message": "Получите последнюю версию $1", "description": "Description used in Snap update alert banner when snap update is available. $1 is the Snap name."}, "snapUpdateAvailable": {"message": "Доступно обновление"}, "snapUpdateErrorDescription": {"message": "Не удалось обновить $1.", "description": "Error description used when snap update fails. $1 is the snap name."}, "snapUpdateErrorTitle": {"message": "Ошибка обновления", "description": "Error title used when snap update fails."}, "snapUpdateRequest": {"message": "Обновление $1 дает ему следующие разрешения.", "description": "$1 is the Snap name."}, "snapUpdateSuccess": {"message": "Обновление завершено"}, "snapUrlIsBlocked": {"message": "Этот Snap хочет перенаправить вас на заблокированный сайт. $1."}, "snaps": {"message": "Snaps"}, "snapsConnected": {"message": "Snaps подключены"}, "snapsNoInsight": {"message": "Нет аналитики для отображения"}, "snapsPrivacyWarningFirstMessage": {"message": "Вы признаете, что Snap, который вы устанавливаете, является Сторонней службой, как она определена в $1 NeoNix, кроме случаев, когда он определяется иначе. Использование вами Сторонних служб регулируется отдельными положениями и условиями, установленными сторонним поставщиком услуг. NeoNix не рекомендует использовать Snap каком-либо конкретному лицу по какой-либо конкретной причине. Вы получаете доступ к Сторонней службе, полагаетесь на нее или используете его на свой страх и риск. NeoNix отказывается от любой ответственности и ответственности за любые убытки, связанные с использованием вами сторонних услуг.", "description": "First part of a message in popup modal displayed when installing a snap for the first time. $1 is terms of use link."}, "snapsPrivacyWarningSecondMessage": {"message": "Любая информация, котор<PERSON>ю вы передаете Сторонним службам, будет собираться непосредственно этими Сторонними службами в соответствии с их политикой конфиденциальности. Пожалуйста, обратитесь к их политике конфиденциальности для получения дополнительной информации.", "description": "Second part of a message in popup modal displayed when installing a snap for the first time."}, "snapsPrivacyWarningThirdMessage": {"message": "Consensys не имеет доступа к информации, которую вы передаете Сторонним службам.", "description": "Third part of a message in popup modal displayed when installing a snap for the first time."}, "snapsSettings": {"message": "Настройки Snap"}, "snapsTermsOfUse": {"message": "Условия использования"}, "snapsToggle": {"message": "Snap будет работать только в том случае, если он включен"}, "snapsUIError": {"message": "Свяжитесь с авторами $1 для получения дополнительной поддержки.", "description": "This is shown when the insight snap throws an error. $1 is the snap name"}, "solanaAccountRequested": {"message": "Этот сайт запрашивает счет Solana."}, "solanaAccountRequired": {"message": "Для подключения к этому сайту необходим счет Solana."}, "solanaImportAccounts": {"message": "Импортировать счета Solana"}, "solanaImportAccountsDescription": {"message": "Импортируйте секретную фразу для восстановления, чтобы перенести свой счет Solana из другого кошелька."}, "solanaMoreFeaturesComingSoon": {"message": "Скоро появятся другие функции"}, "solanaMoreFeaturesComingSoonDescription": {"message": "Скоро появятся dapps Solana, NFT, поддержка аппаратного кошелька и многое другое."}, "solanaOnNeoNix": {"message": "Solana в NeoNix"}, "solanaSendReceiveSwapTokens": {"message": "Отправляйте, получайте и обменивайте токены"}, "solanaSendReceiveSwapTokensDescription": {"message": "Выполняйте пеерводы и совершайте транзакции с помощью токенов, таких как SOL, USDC и другие."}, "someNetworks": {"message": "Сети $1"}, "somethingDoesntLookRight": {"message": "Что-то не так? $1", "description": "A false positive message for users to contact support. $1 is a link to the support page."}, "somethingIsWrong": {"message": "Что-то пошло не так. Попробуйте перезагрузить страницу."}, "somethingWentWrong": {"message": "Нам не удалось загрузить эту страницу."}, "sortBy": {"message": "Сортировать по"}, "sortByAlphabetically": {"message": "По алфавиту (A-Z)"}, "sortByDecliningBalance": {"message": "Уменьшающийся баланс (от высокого к низкому в $1)", "description": "Indicates a descending order based on token fiat balance. $1 is the preferred currency symbol"}, "source": {"message": "Источник"}, "spamModalBlockedDescription": {"message": "Этот сайт будет заблокирован на 1 минуту."}, "spamModalBlockedTitle": {"message": "Вы временно заблокировали этот сайт"}, "spamModalDescription": {"message": "Если вы получаете спам в виде многочисленных запросов, вы можете временно заблокировать сайт."}, "spamModalTemporaryBlockButton": {"message": "Временно заблокировать этот сайт"}, "spamModalTitle": {"message": "Мы заметили многочисленные запросы"}, "speed": {"message": "Скорость"}, "speedUp": {"message": "Ускорить"}, "speedUpCancellation": {"message": "Ускорить эту отмену"}, "speedUpExplanation": {"message": "Мы обновили плату за газ с учетом текущих условий сети и увеличили ее как минимум на 10% (это требование сети)."}, "speedUpPopoverTitle": {"message": "Ускорить транзакцию"}, "speedUpTooltipText": {"message": "Новая плата за газ"}, "speedUpTransaction": {"message": "Ускорить эту транзакцию"}, "spendLimitInsufficient": {"message": "Недостаточный лимит расходов"}, "spendLimitInvalid": {"message": "Неверный лимит расходов. Должен быть положительным числом"}, "spendLimitPermission": {"message": "Разрешение на лимит расходов"}, "spendLimitRequestedBy": {"message": "Лимит расходов затребован $1", "description": "Origin of the site requesting the spend limit"}, "spendLimitTooLarge": {"message": "Лимит расходов слишком велик"}, "spender": {"message": "Расходующее лицо"}, "spenderTooltipDesc": {"message": "Это адрес, по которому можно будет вывести ваши NFT."}, "spenderTooltipERC20ApproveDesc": {"message": "Это адрес, который сможет потратить ваши токены от вашего имени."}, "spendingCap": {"message": "<PERSON>и<PERSON><PERSON><PERSON> расходов"}, "spendingCaps": {"message": "Лимиты расходов"}, "srpInputNumberOfWords": {"message": "У меня есть фраза из $1 слов(-а)", "description": "This is the text for each option in the dropdown where a user selects how many words their secret recovery phrase has during import. The $1 is the number of words (either 12, 15, 18, 21, or 24)."}, "srpListName": {"message": "Секретная фраза для восстановления $1", "description": "$1 is the order of the Secret Recovery Phrase"}, "srpListNumberOfAccounts": {"message": "$1 счета(-ов)", "description": "$1 is the number of accounts in the list"}, "srpListSelectionDescription": {"message": "Секретная фраза для восстановления, на основе которой будет создан ваш новый счет"}, "srpListSingleOrZero": {"message": "$1 счет(-ов)", "description": "$1 is the number of accounts in the list, it is either 1 or 0"}, "srpPasteFailedTooManyWords": {"message": "Не удалось вставить, так как он содержит более 24 слов. Секретная фраза для восстановления может содержать не более 24 слов.", "description": "Description of SRP paste error when the pasted content has too many words"}, "srpPasteTip": {"message": "Вы можете вставить всю свою секретную фразу для восстановления в любое поле", "description": "Our secret recovery phrase input is split into one field per word. This message explains to users that they can paste their entire secrete recovery phrase into any field, and we will handle it correctly."}, "srpSecurityQuizGetStarted": {"message": "Начало работы"}, "srpSecurityQuizImgAlt": {"message": "Глаз с замочной скважиной в центре и три плавающих поля пароля"}, "srpSecurityQuizIntroduction": {"message": "Чтобы увидеть свою секретную фразу для восстановления, вам нужно правильно ответить на два вопроса"}, "srpSecurityQuizQuestionOneQuestion": {"message": "Если вы потеряете свою секретную фразу для восстановления, NeoNix..."}, "srpSecurityQuizQuestionOneRightAnswer": {"message": "Не сможет вам помочь"}, "srpSecurityQuizQuestionOneRightAnswerDescription": {"message": "Запишите ее, выгравируйте ее на металле или храните в нескольких потайных местах, чтобы никогда не потерять. Если вы потеряете ее, она пропадет навсегда."}, "srpSecurityQuizQuestionOneRightAnswerTitle": {"message": "Правильно! Никто не может помочь вернуть вашу секретную фразу для восстановления"}, "srpSecurityQuizQuestionOneWrongAnswer": {"message": "Не сможет вернуть ее вам"}, "srpSecurityQuizQuestionOneWrongAnswerDescription": {"message": "Если вы потеряете свою секретную фразу для восстановления, она пропадет навсегда. Никто не может помочь вам вернуть ее, что бы кто ни говорил."}, "srpSecurityQuizQuestionOneWrongAnswerTitle": {"message": "Неправильно! Никто не может помочь вернуть вашу секретную фразу для восстановления"}, "srpSecurityQuizQuestionTwoQuestion": {"message": "Если кто-нибудь, даже представитель поддержки, попросит вашу секретную фразу для восстановления..."}, "srpSecurityQuizQuestionTwoRightAnswer": {"message": "Вас обманывают"}, "srpSecurityQuizQuestionTwoRightAnswerDescription": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, кто утверждает, что ему нужна ваша секретная фраза для восстановления, лжет вам. Если вы сообщите эту фразу ему (ей), он(-а) украдет ваши активы."}, "srpSecurityQuizQuestionTwoRightAnswerTitle": {"message": "Правильно! Сообщать кому-либо своей секретную фразу для восстановления — это всегда плохая идея"}, "srpSecurityQuizQuestionTwoWrongAnswer": {"message": "Вы должны сообщите фразу ему (ей)"}, "srpSecurityQuizQuestionTwoWrongAnswerDescription": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, кто утверждает, что ему нужна ваша секретная фраза для восстановления, лжет вам. Если вы сообщите эту фразу ему (ей), он(-а) украдет ваши активы."}, "srpSecurityQuizQuestionTwoWrongAnswerTitle": {"message": "Нет! Никогда никому не сообщайте никому свою секретную фразу для восстановления"}, "srpSecurityQuizTitle": {"message": "Тест по безопасности"}, "srpToggleShow": {"message": "Показать/скрыть это слово секретной фразы для восстановления", "description": "Describes a toggle that is used to show or hide a single word of the secret recovery phrase"}, "srpWordHidden": {"message": "Этот слово скрыто", "description": "Explains that a word in the secret recovery phrase is hidden"}, "srpWordShown": {"message": "Это слово отображается", "description": "Explains that a word in the secret recovery phrase is being shown"}, "stable": {"message": "Стабильная"}, "stableLowercase": {"message": "стабильная"}, "stake": {"message": "Выполнить стейкинг"}, "staked": {"message": "В стейкинге"}, "standardAccountLabel": {"message": "Стандартный счет"}, "startEarning": {"message": "Начните зарабатывать"}, "stateLogError": {"message": "Ошибка при получении журналов состояния."}, "stateLogFileName": {"message": "Журналы состояния NeoNix"}, "stateLogs": {"message": "Журналы состояний"}, "stateLogsDescription": {"message": "Журналы состояния содержат ваши адреса публичных счетов и отправленные транзакции."}, "status": {"message": "Статус"}, "statusNotConnected": {"message": "Не подключено"}, "statusNotConnectedAccount": {"message": "Счета не подключены"}, "step1LatticeWallet": {"message": "Подключите Lattice1"}, "step1LatticeWalletMsg": {"message": "Вы можете подключить NeoNix к своему устройству Lattice1, как только оно будет настроено и подключено к сети. Разблокируйте устройство и подготовьте свой ID устройства.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step1LedgerWallet": {"message": "Скачать приложение Ledger"}, "step1LedgerWalletMsg": {"message": "Скачайте $1, настройте его и введите пароль для его разблокировки.", "description": "$1 represents the `ledgerLiveApp` localization value"}, "step1TrezorWallet": {"message": "Подключить кошелек Trezor"}, "step1TrezorWalletMsg": {"message": "Подключите кошелек Trezor напрямую к компьютеру и разблокируйте его. Убедитесь, что вы используете правильную парольную фразу.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step2LedgerWallet": {"message": "Подключите свой Ledger"}, "step2LedgerWalletMsg": {"message": "Подключите Ledger напрямую к компьютеру, разблокируйте его и откройте приложение Ethereum.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "stillGettingMessage": {"message": "Все еще получаете это сообщение?"}, "strong": {"message": "Сильный"}, "stxCancelled": {"message": "Своп бы не удался"}, "stxCancelledDescription": {"message": "Ваша транзакция завершилась неудачно и была бы отменена, чтобы избежать ненужной платы за газ."}, "stxCancelledSubDescription": {"message": "Попробуйте выполнить своп еще раз. Мы готовы защитить вас от подобных рисков в следующий раз."}, "stxFailure": {"message": "Своп не удался"}, "stxFailureDescription": {"message": "Внезапные изменения на рынке могут привести к отказам. Если проблема не устранена, обратитесь по адресу $1.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.NeoNix.io"}, "stxOptInSupportedNetworksDescription": {"message": "Включите функцию «Смарт-транзакции» для более надежных и безопасных транзакций в поддерживаемых сетях. $1"}, "stxPendingPrivatelySubmittingSwap": {"message": "Приватная отправка вашего свопа..."}, "stxPendingPubliclySubmittingSwap": {"message": "Публичная отправка вашего свопа..."}, "stxSuccess": {"message": "Своп завершен!"}, "stxSuccessDescription": {"message": "Ваши $1 уже доступны.", "description": "$1 is a token symbol, e.g. ETH"}, "stxSwapCompleteIn": {"message": "Своп завершится через <", "description": "'<' means 'less than', e.g. <PERSON><PERSON><PERSON> will complete in < 2:59"}, "stxTryingToCancel": {"message": "Попытка отменить транзакцию..."}, "stxUnknown": {"message": "Статус неизвестен"}, "stxUnknownDescription": {"message": "Транзакция прошла успешно, но мы не уверены, что это такое. Это может быть связано с отправкой другой транзакции во время обработки этого свопа."}, "stxUserCancelled": {"message": "Своп отменен"}, "stxUserCancelledDescription": {"message": "Ваша транзакция была отменена, и вы не вносили никакой ненужной платы за газ."}, "submit": {"message": "Отправить"}, "submitted": {"message": "Отправлено"}, "suggestedBySnap": {"message": "Рекомендовано $1", "description": "$1 is the snap name"}, "suggestedCurrencySymbol": {"message": "Рекомендуемый символ валюты:"}, "suggestedTokenName": {"message": "Предлагаемое имя:"}, "supplied": {"message": "Поставлено"}, "support": {"message": "Поддержка"}, "supportCenter": {"message": "Посетите наш центр поддержки"}, "supportMultiRpcInformation": {"message": "Теперь мы поддерживаем несколько RPC для одной сети. Ваш последний RPC был выбран в качестве RPC по умолчанию для разрешения проблемы с противоречивой информацией."}, "surveyConversion": {"message": "Пройдите наш опрос"}, "surveyTitle": {"message": "Сформируйте будущее NeoNix"}, "swap": {"message": "Своп"}, "swapAdjustSlippage": {"message": "Откорректировать проскальзывание"}, "swapAggregator": {"message": "Агрегатор"}, "swapAllowSwappingOf": {"message": "Разрешите своп $1", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "swapAmountReceived": {"message": "Гара<PERSON><PERSON>ированная сумма"}, "swapAmountReceivedInfo": {"message": "Это минимальная сумма, которую вы получите. Вы можете получить больше в зависимости от проскальзывания."}, "swapAndSend": {"message": "Обменять и отправить"}, "swapAnyway": {"message": "Все равно выполнить своп"}, "swapApproval": {"message": "Одобрить использование $1 для свопа", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be swapped.. $1 is the symbol of a token that has been approved."}, "swapApproveNeedMoreTokens": {"message": "Вам нужно еще $1 $2 для завершения этого свопа", "description": "Tells the user how many more of a given token they need for a specific swap. $1 is an amount of tokens and $2 is the token symbol."}, "swapAreYouStillThere": {"message": "Вы все еще там?"}, "swapAreYouStillThereDescription": {"message": "Мы готовы показать вам последние котировки, когда вы захотите продолжить"}, "swapConfirmWithHwWallet": {"message": "Подтвердите с помощью аппаратного кошелька"}, "swapContinueSwapping": {"message": "Продолжить своп"}, "swapContractDataDisabledErrorDescription": {"message": "В приложении Ethereum на Ledger перейдите в раздел «Настройки» и разрешите использование данных о контракте. Затем попробуйте повторить своп."}, "swapContractDataDisabledErrorTitle": {"message": "На Ledger не включены данные о контракте"}, "swapCustom": {"message": "пользовательский"}, "swapDecentralizedExchange": {"message": "Децентрализованная биржа"}, "swapDirectContract": {"message": "Прямой контракт"}, "swapEditLimit": {"message": "Изменить лимит"}, "swapEnableDescription": {"message": "Это необходимо и дает NeoNix разрешение на своп вашего $1.", "description": "Gives the user info about the required approval transaction for swaps. $1 will be the symbol of a token being approved for swaps."}, "swapEnableTokenForSwapping": {"message": "Это $1 возможность свопа", "description": "$1 is for the 'enableToken' key, e.g. 'enable ETH'"}, "swapEnterAmount": {"message": "Введите сумму"}, "swapEstimatedNetworkFees": {"message": "Примерные комиссии сети"}, "swapEstimatedNetworkFeesInfo": {"message": "Это оценочная сетевая комиссия, которая будет использована для завершения вашего свопа. Фактическая сумма может меняться в зависимости от условий сети."}, "swapFailedErrorDescriptionWithSupportLink": {"message": "Иногда транзакции не удается выполнить. Мы рады помочь вам в таких случаях. Если проблема не исчезнет, обратитесь в нашу поддержку клентов на сайте $1.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.NeoNix.io"}, "swapFailedErrorTitle": {"message": "Своп не удался"}, "swapFetchingQuote": {"message": "Получение котировки..."}, "swapFetchingQuoteNofN": {"message": "Получение котировки $1 из $2", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapFetchingQuotes": {"message": "Получение котировок..."}, "swapFetchingQuotesErrorDescription": {"message": "Хмм... Что-то пошло не так. Повторите попытку или, если ошибка не исчезнет, обратитесь в поддержку."}, "swapFetchingQuotesErrorTitle": {"message": "Ошибка при получении котировок"}, "swapFromTo": {"message": "Своп $1 на $2", "description": "Tells a user that they need to confirm on their hardware wallet a swap of 2 tokens. $1 is a source token and $2 is a destination token"}, "swapGasFeesDetails": {"message": "Плата за газ является примерной и будет колебаться в зависимости от сетевого трафика и сложности транзакции."}, "swapGasFeesExplanation": {"message": "NeoNix не получает плату за газ. Сумма этой платы являются приблизительной и может меняться в зависимости от загруженности сети и сложности транзакции. Узнайте подробнее $1.", "description": "$1 is a link (text in link can be found at 'swapGasFeesSummaryLinkText')"}, "swapGasFeesExplanationLinkText": {"message": "здесь", "description": "Text for link in swapGasFeesExplanation"}, "swapGasFeesLearnMore": {"message": "Узнать больше о плате за газ"}, "swapGasFeesSplit": {"message": "Плата за газ, указанная на предыдущем экране, распределяется между этими двумя транзакциями."}, "swapGasFeesSummary": {"message": "Плата за газ переводится криптомайнерам, которые обрабатывают транзакции в сети $1. NeoNix не получает прибыли от платы за газ.", "description": "$1 is the selected network, e.g. Ethereum or BSC"}, "swapGasIncludedTooltipExplanation": {"message": "Плата за газ включена в эту котировку путем корректировки суммы отправленных или полученных токенов. Вы можете получить ETH отдельной транзакцией в своем списке действий."}, "swapGasIncludedTooltipExplanationLinkText": {"message": "Подробнее о плате за газ"}, "swapHighSlippage": {"message": "Высокое проскальзывание"}, "swapIncludesGasAndNeoNixFee": {"message": "Включает плату за газ и комиссию NeoNix в размере $1%", "description": "Provides information about the fee that NeoNix takes for swaps. $1 is a decimal number."}, "swapIncludesMMFee": {"message": "Включает комиссию NeoNix в размере $1%.", "description": "Provides information about the fee that NeoNix takes for swaps. $1 is a decimal number."}, "swapIncludesMMFeeAlt": {"message": "Котировка с учетом комиссии NeoNix в размере $1 %", "description": "Provides information about the fee that NeoNix takes for swaps using the latest copy. $1 is a decimal number."}, "swapIncludesNeoNixFeeViewAllQuotes": {"message": "Включает комиссию NeoNix в размере $1% — $2.", "description": "Provides information about the fee that NeoNix takes for swaps. $1 is a decimal number and $2 is a link to view all quotes."}, "swapLearnMore": {"message": "Узнайте больше о свопах"}, "swapLiquiditySourceInfo": {"message": "Мы ищем по разным источникам ликвидности (биржи, агрегаторы и профессиональные маркет-мейкеры), чтобы сравнивать обменные курсы и комиссии сети."}, "swapLowSlippage": {"message": "Низкое проскальзывание"}, "swapMaxSlippage": {"message": "Максимальное проскальзывание"}, "swapNeoNixFee": {"message": "Комиссия NeoNix"}, "swapNeoNixFeeDescription": {"message": "В этой котировке автоматически учитывается комиссия в размере $1%. Вы платите ее в обмен на лицензию на использование программного обеспечения NeoNix для сбора информации о поставщиках ликвидности.", "description": "Provides information about the fee that NeoNix takes for swaps. $1 is a decimal number."}, "swapNQuotesWithDot": {"message": "$1 котировки(-ок).", "description": "$1 is the number of quotes that the user can select from when opening the list of quotes on the 'view quote' screen"}, "swapNewQuoteIn": {"message": "Новые котировки через $1", "description": "Tells the user the amount of time until the currently displayed quotes are update. $1 is a time that is counting down from 1:00 to 0:00"}, "swapNoTokensAvailable": {"message": "Нет доступных подходящих токенов $1", "description": "Tells the user that a given search string does not match any tokens in our token lists. $1 can be any string of text"}, "swapOnceTransactionHasProcess": {"message": "Ваш $1 будет зачислен на ваш счет после обработки этой транзакции.", "description": "This message communicates the token that is being transferred. It is shown on the awaiting swap screen. The $1 will be a token symbol."}, "swapPriceDifference": {"message": "Вы собираетесь выполнить своп $1 $2 (~$3) на $4 $5 (~$6).", "description": "This message represents the price slippage for the swap.  $1 and $4 are a number (ex: 2.89), $2 and $5 are symbols (ex: ETH), and $3 and $6 are fiat currency amounts."}, "swapPriceDifferenceTitle": {"message": "Разница в цене составляет ~$1%", "description": "$1 is a number (ex: 1.23) that represents the price difference."}, "swapPriceUnavailableDescription": {"message": "Не удалось определить колебание цены из-за отсутствия данных о рыночных ценах. Перед свопом убедитесь, что вас устраивает количество токенов, которое вы получите."}, "swapPriceUnavailableTitle": {"message": "Прежде чем продолжить, проверьте курс"}, "swapProcessing": {"message": "Обработка..."}, "swapQuoteDetails": {"message": "Свдения о котировке"}, "swapQuoteNofM": {"message": "$1 из $2", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapQuoteSource": {"message": "Источник котировки"}, "swapQuotesExpiredErrorDescription": {"message": "Запрашивайте новые котировки, чтобы узнать последние курсы."}, "swapQuotesExpiredErrorTitle": {"message": "Таймаут котировок"}, "swapQuotesNotAvailableDescription": {"message": "Этот торговый путь сейчас недоступен. Попробуйте изменить сумму, сеть или токен и мы подберем лучший вариант."}, "swapQuotesNotAvailableErrorDescription": {"message": "Попробуйте изменить настройки суммы или проскальзывания и повторить попытку."}, "swapQuotesNotAvailableErrorTitle": {"message": "Нет доступных котировок"}, "swapRate": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "swapReceiving": {"message": "Получение"}, "swapReceivingInfoTooltip": {"message": "Это примерное значение. Точная сумма зависит от проскальзывания."}, "swapRequestForQuotation": {"message": "Запрос котировки"}, "swapSelect": {"message": "Выбрать"}, "swapSelectAQuote": {"message": "Выбрать котировку"}, "swapSelectAToken": {"message": "Выбрать токен"}, "swapSelectQuotePopoverDescription": {"message": "Ниже приведены все котировки, собранные из нескольких источников ликвидности."}, "swapSelectToken": {"message": "Выберите токен"}, "swapShowLatestQuotes": {"message": "Показать последние котировки"}, "swapSlippageHighDescription": {"message": "Введенное проскальзывание ($1%) считается очень высоким и может привести к использованию плохого курса", "description": "$1 is the amount of % for slippage"}, "swapSlippageHighTitle": {"message": "Высокое проскальзывание"}, "swapSlippageLowDescription": {"message": "Такое низкое значение ($1%) может привести к неудачному свопу", "description": "$1 is the amount of % for slippage"}, "swapSlippageLowTitle": {"message": "Низкое проскальзывание"}, "swapSlippageNegativeDescription": {"message": "Проскальзывание должно быть больше или равно нулю"}, "swapSlippageNegativeTitle": {"message": "Увеличьте проскальзывание, чтобы продолжить"}, "swapSlippageOverLimitDescription": {"message": "Допуск на проскальзывание должен составлять 15% или менее. Все, что выше, приведет к неудачной ставке."}, "swapSlippageOverLimitTitle": {"message": "Очень высокое проскальзывание"}, "swapSlippagePercent": {"message": "$1%", "description": "$1 is the amount of % for slippage"}, "swapSlippageTooltip": {"message": "Изменение цены в период между размещением заказа и подтверждением называется «проскальзыванием». Своп будет автоматически отменен, если фактическое проскальзывание превысит установленный «допуск проскальзывания»."}, "swapSlippageZeroDescription": {"message": "Существует меньше поставщиков котировок с нулевым проскальзыванием, что приводит к менее конкурентоспособным котировкам."}, "swapSlippageZeroTitle": {"message": "Поиск поставщиков с нулевым проскальзыванием"}, "swapSource": {"message": "Источник ликвидности"}, "swapSuggested": {"message": "Рекомендуется своп"}, "swapSuggestedGasSettingToolTipMessage": {"message": "Своп — это сложная транзакция, выполнение которой зависят от имеющегося времени. Мы рекомендуем эту плату за газ для хорошего баланса между стоимостью и уверенностью в успехе свопа."}, "swapSwapFrom": {"message": "Выполнить своп из"}, "swapSwapSwitch": {"message": "Сменить порядок токенов"}, "swapSwapTo": {"message": "Выполнить своп на"}, "swapToConfirmWithHwWallet": {"message": "чтобы подтвердить с помощью аппаратного кошелька"}, "swapTokenAddedManuallyDescription": {"message": "Проверьте этот токен на $1 и убедитесь, что это именно тот токен, которым вы хотите торговать.", "description": "$1 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenAddedManuallyTitle": {"message": "Токен добавлен вручную"}, "swapTokenAvailable": {"message": "Ваши $1 зачислены на ваш счет.", "description": "This message is shown after a swap is successful and communicates the exact amount of tokens the user has received for a swap. The $1 is a decimal number of tokens followed by the token symbol."}, "swapTokenBalanceUnavailable": {"message": "Не удалось получить баланс $1", "description": "This message communicates to the user that their balance of a given token is currently unavailable. $1 will be replaced by a token symbol"}, "swapTokenNotAvailable": {"message": "Своп токена в этом регионе недоступен"}, "swapTokenToToken": {"message": "Выполнить своп $1 на $2", "description": "Used in the transaction display list to describe a swap. $1 and $2 are the symbols of tokens in involved in a swap."}, "swapTokenVerifiedOn1SourceDescription": {"message": "$1 проверяется только на 1 источнике. Попробуйте проверить его на $2, прежде чем продолжить.", "description": "$1 is a token name, $2 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenVerifiedOn1SourceTitle": {"message": "Потенциально неаутентичный токен"}, "swapTokenVerifiedSources": {"message": "Подтверждено $1 источниками. Подтвердить на $2.", "description": "$1 the number of sources that have verified the token, $2 points the user to a block explorer as a place they can verify information about the token."}, "swapTooManyDecimalsError": {"message": "$1 позволяет использовать до $2 десятичных знаков", "description": "$1 is a token symbol and $2 is the max. number of decimals allowed for the token"}, "swapTransactionComplete": {"message": "Транзакция завершена"}, "swapTwoTransactions": {"message": "2 транзакции"}, "swapUnknown": {"message": "Неизвестно"}, "swapZeroSlippage": {"message": "0% проскальзывания"}, "swapsMaxSlippage": {"message": "Допуск проскальзывания"}, "swapsNotEnoughToken": {"message": "Недостаточно $1", "description": "Tells the user that they don't have enough of a token for a proposed swap. $1 is a token symbol"}, "swapsViewInActivity": {"message": "Посмотреть в журнале активности"}, "switch": {"message": "Сменить"}, "switchEthereumChainConfirmationDescription": {"message": "В результате этого сеть, выбранная в NeoNix, будет изменена на ранее добавленную:"}, "switchEthereumChainConfirmationTitle": {"message": "Разрешить этому сайту сменить сеть?"}, "switchInputCurrency": {"message": "Изменить входную валюту"}, "switchNetwork": {"message": "Сменить сеть"}, "switchNetworks": {"message": "Сменить сети"}, "switchToNetwork": {"message": "Сменить на $1", "description": "$1 represents the custom network that has previously been added"}, "switchToThisAccount": {"message": "Переключиться на этот счет"}, "switchedNetworkToastDecline": {"message": "Не показывать снова"}, "switchedNetworkToastMessage": {"message": "$1 теперь активен в $2", "description": "$1 represents the account name, $2 represents the network name"}, "switchedNetworkToastMessageNoOrigin": {"message": "Сейчас Вы используете $1", "description": "$1 represents the network name"}, "switchingNetworksCancelsPendingConfirmations": {"message": "В случае смены сетей все ожидающие подтверждения будут отменены"}, "symbol": {"message": "Символ"}, "symbolBetweenZeroTwelve": {"message": "Символ должен состоять из 11 или менее знаков."}, "tenPercentIncreased": {"message": "Увеличение на 10%"}, "terms": {"message": "Условия использования"}, "termsOfService": {"message": "Условия обслуживания"}, "termsOfUseAgreeText": {"message": " Я согласен(-на) с Условиями использования, которые применяются к использованию мною NeoNix и всех его функций"}, "termsOfUseFooterText": {"message": "Прокрутите, чтобы прочитать все разделы"}, "termsOfUseTitle": {"message": "Наши Условия использования обновлены"}, "testNetworks": {"message": "Протестировать сети"}, "testnets": {"message": "Тестнеты"}, "theme": {"message": "Тема"}, "themeDescription": {"message": "Выберите предпочитаемую тему NeoNix."}, "thirdPartySoftware": {"message": "Уведомление о стороннем ПО", "description": "Title of a popup modal displayed when installing a snap for the first time."}, "time": {"message": "Время"}, "tipsForUsingAWallet": {"message": "Советы по использованию кошелька"}, "tipsForUsingAWalletDescription": {"message": "Добавление токенов открывает больше способов использования web3."}, "to": {"message": "Адре<PERSON><PERSON>т"}, "toAddress": {"message": "Адресат: $1", "description": "$1 is the address to include in the To label. It is typically shortened first using shortenAddress"}, "toggleDecodeDescription": {"message": "Мы используем сервисы 4byte.directory и Sourcify для декодирования и отображения более читаемых данных транзакций. Это поможет Вам понять результат ожидающих и прошлых транзакций, но может привести к раскрытию вашего IP-адреса."}, "token": {"message": "Токен"}, "tokenAddress": {"message": "Адрес токена"}, "tokenAlreadyAdded": {"message": "Токен уже добавлен."}, "tokenAutoDetection": {"message": "Автоопределение токена"}, "tokenContractAddress": {"message": "Адрес контракта токена"}, "tokenDecimal": {"message": "Число десятичных знаков токена"}, "tokenDecimalFetchFailed": {"message": "Требуется десятичный токен. Найдите его здесь: $1"}, "tokenDetails": {"message": "Сведения о токене"}, "tokenFoundTitle": {"message": "Найден 1 новый токен"}, "tokenId": {"message": "ID токена"}, "tokenList": {"message": "Списки токенов:"}, "tokenMarketplace": {"message": "Торговая площадка для токенов"}, "tokenScamSecurityRisk": {"message": "мошенничество с токенами и угрозы безопасности"}, "tokenStandard": {"message": "Стандарт токена"}, "tokenSymbol": {"message": "Символ токена"}, "tokens": {"message": "Токены"}, "tokensFoundTitle": {"message": "Найдены $1 новых токена(-ов)", "description": "$1 is the number of new tokens detected"}, "tokensInCollection": {"message": "Токены в коллекции"}, "tooltipApproveButton": {"message": "Я понимаю"}, "tooltipSatusConnected": {"message": "подключено"}, "tooltipSatusConnectedUpperCase": {"message": "Подключен"}, "tooltipSatusNotConnected": {"message": "не подключено"}, "total": {"message": "Итого"}, "totalVolume": {"message": "Об<PERSON>ий объем"}, "transaction": {"message": "транзакция"}, "transactionCancelAttempted": {"message": "Предпринята попытка отменить транзакцию с примерной платой за газ в размере $1 в $2"}, "transactionCancelSuccess": {"message": "Транзакция успешно отменена в $2"}, "transactionConfirmed": {"message": "Транзакция подтверждена в $2."}, "transactionCreated": {"message": "В $2 создана транзакция на сумму $1."}, "transactionDataFunction": {"message": "Функция"}, "transactionDetailGasHeading": {"message": "Примерная плата за газ"}, "transactionDetailMultiLayerTotalSubtitle": {"message": "Сумма + комиссии"}, "transactionDropped": {"message": "Транзакция отменена в $2."}, "transactionError": {"message": "Ошибка транзакции. Исключение в коде контракта."}, "transactionErrorNoContract": {"message": "Попытка вызвать функцию на неконтрактном адресе."}, "transactionErrored": {"message": "Транзакция обнаружила ошибку."}, "transactionFlowNetwork": {"message": "Сеть"}, "transactionHistoryBaseFee": {"message": "Базовая комиссия (Гвей)"}, "transactionHistoryL1GasLabel": {"message": "Итого платы за газ L1"}, "transactionHistoryL2GasLimitLabel": {"message": "Лимит газа L2"}, "transactionHistoryL2GasPriceLabel": {"message": "Цена газа L2"}, "transactionHistoryMaxFeePerGas": {"message": "Макс. плата за газ"}, "transactionHistoryPriorityFee": {"message": "Плата за приоритет (Гвей)"}, "transactionHistoryTotalGasFee": {"message": "Итого платы за газ"}, "transactionIdLabel": {"message": "ID транзакции", "description": "Label for the source transaction ID field."}, "transactionIncludesTypes": {"message": "Эта транзакция включает: $1."}, "transactionResubmitted": {"message": "Транзакция отправлена повторно с платой за газ, увеличенной до $1, в $2"}, "transactionSettings": {"message": "Настройки транзакции"}, "transactionSubmitted": {"message": "Транзакция отправлена с платой за газ в размере $1 в $2."}, "transactionTotalGasFee": {"message": "Итого платы за газ", "description": "Label for the total gas fee incurred in the transaction."}, "transactionUpdated": {"message": "Транзакция обновлена в $2."}, "transactions": {"message": "Транзакции"}, "transfer": {"message": "Перевести"}, "transferCrypto": {"message": "Перевести криптовалюту"}, "transferFrom": {"message": "Перевести из"}, "transferRequest": {"message": "Запрос на перевод"}, "trillionAbbreviation": {"message": "трлн", "description": "Shortened form of 'trillion'"}, "troubleConnectingToLedgerU2FOnFirefox": {"message": "У нас возникли проблемы с подключением вашего Ledger. $1", "description": "$1 is a link to the wallet connection guide;"}, "troubleConnectingToLedgerU2FOnFirefox2": {"message": "Ознакомьтесь с нашим руководством по подключению аппаратного кошелька и повторите попытку.", "description": "$1 of the ledger wallet connection guide"}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution": {"message": "Если вы используете последнюю версию Firefox, у вас может возникнуть проблема, связанная с отказом Firefox от поддержки U2F. Узнайте, как решить эту проблему $1.", "description": "It is a link to the ledger website for the workaround."}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution2": {"message": "здесь", "description": "Second part of the error message; It is a link to the ledger website for the workaround."}, "troubleConnectingToWallet": {"message": "Не удалось подключиться к вашему $1, попробуйте проверить $2 и повторите попытку.", "description": "$1 is the wallet device name; $2 is a link to wallet connection guide"}, "troubleStarting": {"message": "У NeoNix возникли проблемы с запуском. Эта ошибка может быть непостоянной, поэтому попробуйте перезапустить расширение."}, "tryAgain": {"message": "Попробуйте еще раз"}, "turnOff": {"message": "Выключить"}, "turnOffNeoNixNotificationsError": {"message": "Произошла ошибка при отключении уведомлений. Повторите попытку позже."}, "turnOn": {"message": "Включить"}, "turnOnNeoNixNotifications": {"message": "Включить уведомления"}, "turnOnNeoNixNotificationsButton": {"message": "Включить"}, "turnOnNeoNixNotificationsError": {"message": "Произошла ошибка при создании уведомлений. Повторите попытку позже."}, "turnOnNeoNixNotificationsMessageFirst": {"message": "Будьте в курсе того, что происходит в вашем кошельке, с помощью уведомлений."}, "turnOnNeoNixNotificationsMessagePrivacyBold": {"message": "настройки уведомлений."}, "turnOnNeoNixNotificationsMessagePrivacyLink": {"message": "Узнайте, как мы защищаем вашу конфиденциальность при использовании этой функции."}, "turnOnNeoNixNotificationsMessageSecond": {"message": "Чтобы отправлять уведомления кошелька, мы используем профиль для синхронизации некоторых настроек на ваших устройствах ($1)."}, "turnOnNeoNixNotificationsMessageThird": {"message": "Вы можете отключить уведомления в любое время в $1"}, "turnOnTokenDetection": {"message": "Включите расширенное определение токенов"}, "tutorial": {"message": "Руководство"}, "twelveHrTitle": {"message": "12 ч:"}, "u2f": {"message": "U2F", "description": "A name on an API for the browser to interact with devices that support the U2F protocol. On some browsers we use it to connect NeoNix to Ledger devices."}, "unapproved": {"message": "Не одобрен"}, "unexpectedBehavior": {"message": "Такое поведение является неожиданным, и о нем следует сообщить как об ошибке, даже если ваши счета восстановлены должным образом. Используйте ссылку ниже, чтобы отправить в NeoNix отчет об ошибке."}, "units": {"message": "единицы"}, "unknown": {"message": "Неизвестно"}, "unknownCollection": {"message": "Безымянная коллекция"}, "unknownNetworkForKeyEntropy": {"message": "Неизвестная сеть", "description": "Displayed on places like Snap install warning when regular name is not available."}, "unknownQrCode": {"message": "Ошибка: мы не смогли идентифицировать этот QR-код"}, "unlimited": {"message": "Без ограничений"}, "unlock": {"message": "Разблокировать"}, "unpin": {"message": "Открепить"}, "unrecognizedChain": {"message": "Эта пользовательская сеть не распознана. Мы рекомендуем $1, прежде чем продолжить", "description": "$1 is a clickable link with text defined by the 'unrecognizedChanLinkText' key. The link will open to instructions for users to validate custom network details."}, "unsendableAsset": {"message": "Отправка токенов NFT (ERC-721) сейчас не поддерживается", "description": "This is an error message we show the user if they attempt to send an NFT asset type, for which currently don't support sending"}, "unstableTokenPriceDescription": {"message": "Цена этого токена в долларах США крайне нестабильна, что указывает на высокий риск потери значительной стоимости при взаимодействии с ним."}, "unstableTokenPriceTitle": {"message": "Нестабильная цена токена"}, "upArrow": {"message": "стрелка «вверх»"}, "update": {"message": "Обновить"}, "updateEthereumChainConfirmationDescription": {"message": "Этот сайт запрашивает обновление URL вашей сети по умолчанию. Вы можете редактировать настройки по умолчанию и информацию о сети в любое время."}, "updateNetworkConfirmationTitle": {"message": "Обновить $1", "description": "$1 represents network name"}, "updateOrEditNetworkInformations": {"message": "Обновите свою информацию или"}, "updateRequest": {"message": "Запрос обновления"}, "updatedRpcForNetworks": {"message": "Обновлены сетевые RPC"}, "uploadDropFile": {"message": "Переместите свой файл сюда"}, "uploadFile": {"message": "Загрузить файл"}, "urlErrorMsg": {"message": "URL должен иметь соответствующий префикс HTTP/HTTPS."}, "use4ByteResolution": {"message": "Расшифровать смарт-контракты"}, "useMultiAccountBalanceChecker": {"message": "Пакетные запросы баланса счета"}, "useMultiAccountBalanceCheckerSettingDescription": {"message": "Получайте более быстрые обновления сведений о балансе, группируя запросы по балансу счета. Это позволяет нам извлекать совокупные данные о балансах ваших счетов, чтобы вы могли быстрее получать обновления для улучшения впечатлений от использования. Когда эта функция отключена, снижается вероятность того, что третьи стороны свяжут ваши счета друг с другом."}, "useNftDetection": {"message": "Автоопределение NFT"}, "useNftDetectionDescriptionText": {"message": "Разрешает NeoNix добавлять ваши собственные NFT с помощью сторонних сервисов. Автоопределение NFT раскрывает ваш IP-адрес и адрес счета этим сервисам. Включение этой функции может связать ваш IP-адрес с вашим адресом Ethereum и отобразить поддельные NFT-файлы, аирдропнутые мошенниками. Вы можете добавлять токены вручную, чтобы избежать этой угрозы."}, "usePhishingDetection": {"message": "Использовать обнаружение фишинга"}, "usePhishingDetectionDescription": {"message": "Показывать предупреждение для фишинговых доменов, нацеленных на пользователей Ethereum"}, "useSafeChainsListValidation": {"message": "Проверка сведений о сети"}, "useSafeChainsListValidationDescription": {"message": "NeoNix использует сторонний сервис под названием $1 для отображения точных и стандартизированных сведений о сети. Это снижает ваши шансы подключиться к вредоносной или неправильной сети. При использовании этой функции ваш IP-адрес доступен сети chainid.network."}, "useSafeChainsListValidationWebsite": {"message": "chainid.network", "description": "useSafeChainsListValidationWebsite is separated from the rest of the text so that we can bold the third party service name in the middle of them"}, "useTokenDetectionPrivacyDesc": {"message": "Автоматическое отображение токенов, отправленных на ваш счет, требует обмена данными со сторонними серверами для получения изображений токенов. Эти серверы получат доступ к вашему IP-адресу."}, "usedByClients": {"message": "Используется множеством разных клиентов"}, "userName": {"message": "Имя пользователя"}, "userOpContractDeployError": {"message": "Развертывание контракта из счета смарт-контракта не поддерживается"}, "version": {"message": "Версия"}, "view": {"message": "Просмотр"}, "viewActivity": {"message": "Смотреть активность"}, "viewAllQuotes": {"message": "смотреть все котировки"}, "viewContact": {"message": "Смотреть контакт"}, "viewDetails": {"message": "Просмотр сведений"}, "viewMore": {"message": "Больше"}, "viewOnBlockExplorer": {"message": "Смотреть в обозревателе блоков"}, "viewOnCustomBlockExplorer": {"message": "Смотреть $1 в $2", "description": "$1 is the action type. e.g (Account, Transaction, Swap) and $2 is the Custom Block Explorer URL"}, "viewOnEtherscan": {"message": "Смотреть 1$ на Etherscan", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "viewOnExplorer": {"message": "Смотреть в обозревателе"}, "viewOnOpensea": {"message": "Смотреть на OpenSea"}, "viewSolanaAccount": {"message": "Смотреть счет Solana"}, "viewTransaction": {"message": "Смотреть транзакцию"}, "viewinExplorer": {"message": "Смотреть $1 в обозревателе", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "visitSite": {"message": "Посетить сайт"}, "visitSupportDataConsentModalAccept": {"message": "Подтвердить"}, "visitSupportDataConsentModalDescription": {"message": "Хотите сообщить свой идентификатор NeoNix и версию приложения в наш Центр поддержки? Возможно, это поможет нам лучше решить вашу проблему, хотя это не гарантируется."}, "visitSupportDataConsentModalReject": {"message": "Не сообщать"}, "visitSupportDataConsentModalTitle": {"message": "Передать сведения об устройстве в службу поддержки"}, "visitWebSite": {"message": "Посетите наш веб-сайт"}, "wallet": {"message": "Кошелек"}, "walletConnectionGuide": {"message": "наше руководство по подключению аппаратного кошелька"}, "wantToAddThisNetwork": {"message": "Хотите добавить эту сеть?"}, "wantsToAddThisAsset": {"message": "$1 хочет добавить этот актив в ваш кошелек."}, "warning": {"message": "Предупреждение"}, "warningFromSnap": {"message": "Предупреждение от $1", "description": "$1 represents the name of the snap"}, "watchEthereumAccountsDescription": {"message": "Включение этой опции даст вам возможность отслеживать счета Ethereum через публичный адрес или имя ENS. Чтобы оставить отзыв об этой бета-функции, заполните этот $1.", "description": "$1 is the link to a product feedback form"}, "watchEthereumAccountsToggle": {"message": "Следить за счетами Ethereum (бета)"}, "watchOutMessage": {"message": "Остерегайтесь $1.", "description": "$1 is a link with text that is provided by the 'securityMessageLinkForNetworks' key"}, "weak": {"message": "Слабый"}, "web3": {"message": "Web3"}, "web3ShimUsageNotification": {"message": "Мы заметили, что текущий веб-сайт пытался использовать удаленный API window.web3. Если сайт не работает, нажмите $1 для получения дополнительной информации.", "description": "$1 is a clickable link."}, "webhid": {"message": "WebHID", "description": "Refers to a interface for connecting external devices to the browser. Used for connecting ledger to the browser. Read more here https://developer.mozilla.org/en-US/docs/Web/API/WebHID_API"}, "websites": {"message": "веб-сайты", "description": "Used in the 'permission_rpc' message."}, "welcomeBack": {"message": "С возвращением!"}, "welcomeToNeoNix": {"message": "Давайте приступим к делу"}, "whatsThis": {"message": "Что это?"}, "willApproveAmountForBridging": {"message": "Это позволит утвердить $1 для создания моста."}, "willApproveAmountForBridgingHardware": {"message": "Вам нужно будет подтвердить две транзакции в своем аппаратном кошельке."}, "withdrawing": {"message": "Выполняется вывод"}, "wrongNetworkName": {"message": "Согласно нашим записям, имя сети может не соответствовать этому ID блокчейна."}, "yes": {"message": "Да"}, "you": {"message": "Вы"}, "youDeclinedTheTransaction": {"message": "Вы отклонили транзакцию."}, "youNeedToAllowCameraAccess": {"message": "Для использования этой функции вам необходимо предоставить доступ к камере."}, "youReceived": {"message": "Вы получили", "description": "Label indicating the amount and asset the user received."}, "youSent": {"message": "Вы отправили", "description": "Label indicating the amount and asset the user sent."}, "yourAccounts": {"message": "Ваши счета"}, "yourActivity": {"message": "Ваша деятельность"}, "yourBalance": {"message": "<PERSON>а<PERSON> баланс"}, "yourNFTmayBeAtRisk": {"message": "Ваш NFT могут быть в опасности"}, "yourNetworks": {"message": "Ваши сети"}, "yourPrivateSeedPhrase": {"message": "Ваша личная секретная фраза для восстановления"}, "yourTransactionConfirmed": {"message": "Транзакция уже подтверждена"}, "yourTransactionJustConfirmed": {"message": "Нам не удалось отменить вашу транзакцию до ее подтверждения в блокчейне."}, "yourWalletIsReady": {"message": "Ваш кошелек готов"}}